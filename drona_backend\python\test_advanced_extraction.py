#!/usr/bin/env python3
"""
Test script for Advanced Question Extraction System
Tests the new multi-stage extraction pipeline
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_pdf_analysis(pdf_path):
    """Test PDF analysis functionality"""
    try:
        log_print("🔍 [TEST] Testing PDF analysis...")
        
        from smart_pdf_analyzer import SmartPDFAnalyzer
        analyzer = SmartPDFAnalyzer()
        
        analysis = analyzer.analyze_pdf_structure(pdf_path)
        
        if 'error' in analysis:
            log_print(f"❌ [ANALYSIS] Analysis failed: {analysis['error']}")
            return False
        
        # Display analysis results
        file_info = analysis.get('file_info', {})
        log_print(f"📄 [ANALYSIS] File size: {file_info.get('size_mb', 0):.1f}MB")
        log_print(f"📄 [ANALYSIS] Text length: {file_info.get('text_length', 0):,} characters")
        log_print(f"📄 [ANALYSIS] Images: {file_info.get('image_count', 0)}")
        
        question_patterns = analysis.get('question_patterns', {})
        estimated_questions = question_patterns.get('best_estimate', 0)
        log_print(f"📊 [ANALYSIS] Estimated questions: {estimated_questions}")
        
        content_quality = analysis.get('content_quality', {})
        quality_score = content_quality.get('quality_score', 0)
        log_print(f"📊 [ANALYSIS] Content quality: {quality_score:.1f}/100")
        
        extraction_strategy = analysis.get('extraction_strategy', {})
        recommended_method = extraction_strategy.get('primary_method', 'unknown')
        confidence = extraction_strategy.get('confidence', 0)
        log_print(f"💡 [ANALYSIS] Recommended method: {recommended_method} (confidence: {confidence:.1f})")
        
        return True
        
    except Exception as e:
        log_print(f"❌ [ANALYSIS] Analysis test failed: {e}")
        return False

def test_advanced_extractor_direct(pdf_path):
    """Test advanced extractor directly"""
    try:
        log_print("🚀 [TEST] Testing advanced extractor directly...")
        
        from advanced_question_extractor import AdvancedQuestionExtractor
        
        extractor = AdvancedQuestionExtractor(ai_provider='gemini')
        
        start_time = time.time()
        result = extractor.extract_questions_comprehensive(pdf_path)
        duration = time.time() - start_time
        
        questions_json = result.get('questions', '[]')
        questions = json.loads(questions_json) if questions_json else []
        
        metadata = result.get('extraction_metadata', {})
        statistics = metadata.get('statistics', {})
        
        log_print(f"✅ [DIRECT] Extraction completed in {duration:.2f}s")
        log_print(f"📊 [DIRECT] Questions extracted: {len(questions)}")
        log_print(f"📊 [DIRECT] Pattern extracted: {statistics.get('pattern_extracted', 0)}")
        log_print(f"📊 [DIRECT] AI extracted: {statistics.get('ai_extracted', 0)}")
        log_print(f"📊 [DIRECT] Duplicates removed: {statistics.get('duplicates_removed', 0)}")
        log_print(f"📊 [DIRECT] Success rate: {metadata.get('success_rate', 0):.1f}%")
        
        # Show sample questions
        if questions:
            log_print("📝 [DIRECT] Sample questions:")
            for i, q in enumerate(questions[:3]):
                content = q.get('content', '')[:100]
                method = q.get('extraction_method', 'unknown')
                log_print(f"   {i+1}. {content}... (method: {method})")
        
        return len(questions) > 0
        
    except Exception as e:
        log_print(f"❌ [DIRECT] Direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_server_advanced(pdf_path):
    """Test advanced extraction via API server"""
    try:
        log_print("🌐 [TEST] Testing advanced extraction via API server...")
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:5000/', timeout=5)
            if response.status_code != 200:
                log_print("❌ [API] API server not responding correctly")
                return False
        except requests.exceptions.ConnectionError:
            log_print("❌ [API] Cannot connect to API server. Start with: python api_server.py")
            return False
        
        # Test advanced extraction
        log_print("📤 [API] Sending PDF for advanced extraction...")
        
        with open(pdf_path, 'rb') as f:
            files = {'file': f}
            data = {
                'ai_provider': 'gemini',
                'use_enhanced': 'true',
                'use_advanced': 'true'  # Enable advanced extraction
            }
            
            start_time = time.time()
            response = requests.post(
                'http://localhost:5000/api/extract', 
                files=files, 
                data=data, 
                timeout=1800  # 30 minutes timeout
            )
            duration = time.time() - start_time
        
        log_print(f"📥 [API] Response received in {duration:.2f}s")
        log_print(f"📊 [API] Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            status = result.get('status')
            questions_count = result.get('questions_count', 0)
            performance_metrics = result.get('performance_metrics', {})
            
            log_print(f"✅ [API] Advanced extraction successful!")
            log_print(f"📊 [API] Status: {status}")
            log_print(f"📊 [API] Questions extracted: {questions_count}")
            log_print(f"📊 [API] Total duration: {performance_metrics.get('total_duration', 0):.2f}s")
            log_print(f"📊 [API] Extraction method: {performance_metrics.get('extraction_method', 'unknown')}")
            
            # Show advanced-specific metrics
            if 'stages_used' in performance_metrics:
                log_print(f"📊 [API] Stages used: {', '.join(performance_metrics['stages_used'])}")
            if 'success_rate' in performance_metrics:
                log_print(f"📊 [API] Success rate: {performance_metrics['success_rate']:.1f}%")
            
            return questions_count > 0
        else:
            log_print(f"❌ [API] Extraction failed with status {response.status_code}")
            try:
                error_data = response.json()
                log_print(f"📄 [API] Error details: {error_data}")
            except:
                log_print(f"📄 [API] Response text: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        log_print("❌ [API] Request timed out (>30 minutes)")
        return False
    except Exception as e:
        log_print(f"❌ [API] API test failed: {e}")
        return False

def compare_extraction_methods(pdf_path):
    """Compare different extraction methods"""
    try:
        log_print("⚖️ [COMPARE] Comparing extraction methods...")
        
        methods = [
            ('Standard', {'use_enhanced': 'false', 'use_advanced': 'false'}),
            ('Enhanced', {'use_enhanced': 'true', 'use_advanced': 'false'}),
            ('Advanced', {'use_enhanced': 'true', 'use_advanced': 'true'})
        ]
        
        results = {}
        
        for method_name, params in methods:
            try:
                log_print(f"🔄 [COMPARE] Testing {method_name} method...")
                
                with open(pdf_path, 'rb') as f:
                    files = {'file': f}
                    data = {
                        'ai_provider': 'gemini',
                        **params
                    }
                    
                    start_time = time.time()
                    response = requests.post(
                        'http://localhost:5000/api/extract', 
                        files=files, 
                        data=data, 
                        timeout=1800
                    )
                    duration = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    questions_count = result.get('questions_count', 0)
                    
                    results[method_name] = {
                        'questions': questions_count,
                        'duration': duration,
                        'success': True
                    }
                    
                    log_print(f"✅ [COMPARE] {method_name}: {questions_count} questions in {duration:.1f}s")
                else:
                    results[method_name] = {
                        'questions': 0,
                        'duration': duration,
                        'success': False
                    }
                    log_print(f"❌ [COMPARE] {method_name}: Failed")
                    
            except Exception as e:
                log_print(f"❌ [COMPARE] {method_name} failed: {e}")
                results[method_name] = {'questions': 0, 'duration': 0, 'success': False}
        
        # Display comparison
        log_print("\n📊 [COMPARE] Comparison Results:")
        log_print("=" * 50)
        for method, result in results.items():
            status = "✅" if result['success'] else "❌"
            log_print(f"{status} {method:10} | {result['questions']:3d} questions | {result['duration']:6.1f}s")
        
        # Find best method
        best_method = max(results.items(), key=lambda x: x[1]['questions'])
        log_print(f"\n🏆 [COMPARE] Best method: {best_method[0]} with {best_method[1]['questions']} questions")
        
        return results
        
    except Exception as e:
        log_print(f"❌ [COMPARE] Comparison failed: {e}")
        return {}

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Advanced Question Extraction System")
    parser.add_argument("--pdf", help="Path to test PDF file", default=None)
    parser.add_argument("--analysis-only", action="store_true", help="Only run PDF analysis")
    parser.add_argument("--direct-only", action="store_true", help="Only test direct extraction")
    parser.add_argument("--api-only", action="store_true", help="Only test API extraction")
    parser.add_argument("--compare", action="store_true", help="Compare all extraction methods")
    
    args = parser.parse_args()
    
    # Find PDF file
    pdf_path = args.pdf
    if not pdf_path:
        pdf_files = list(Path('.').glob('*.pdf'))
        if not pdf_files:
            log_print("❌ No PDF files found. Please specify --pdf path/to/file.pdf")
            return False
        pdf_path = str(pdf_files[0])
        log_print(f"📄 Using PDF: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        log_print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    log_print("🚀 Testing Advanced Question Extraction System")
    log_print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    # Test 1: PDF Analysis
    if not args.direct_only and not args.api_only:
        log_print("\n🔍 Test 1: PDF Structure Analysis")
        total_tests += 1
        if test_pdf_analysis(pdf_path):
            success_count += 1
        
        if args.analysis_only:
            log_print(f"\n📊 Analysis completed. Quality check done.")
            return True
    
    # Test 2: Direct Extraction
    if not args.analysis_only and not args.api_only:
        log_print("\n🚀 Test 2: Direct Advanced Extraction")
        total_tests += 1
        if test_advanced_extractor_direct(pdf_path):
            success_count += 1
        
        if args.direct_only:
            log_print(f"\n📊 Direct extraction test completed.")
            return success_count > 0
    
    # Test 3: API Extraction
    if not args.analysis_only and not args.direct_only:
        log_print("\n🌐 Test 3: API Advanced Extraction")
        total_tests += 1
        if test_api_server_advanced(pdf_path):
            success_count += 1
        
        if args.api_only:
            log_print(f"\n📊 API extraction test completed.")
            return success_count > 0
    
    # Test 4: Method Comparison
    if args.compare:
        log_print("\n⚖️ Test 4: Method Comparison")
        total_tests += 1
        comparison_results = compare_extraction_methods(pdf_path)
        if comparison_results:
            success_count += 1
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 Test Results Summary:")
    log_print(f"✅ Passed: {success_count}/{total_tests} tests")
    
    if success_count == total_tests:
        log_print("🎉 All tests passed! Advanced extraction system is working correctly.")
        log_print("\n💡 Next steps:")
        log_print("  1. Use 'use_advanced=true' parameter in API calls")
        log_print("  2. Monitor extraction performance with large PDFs")
        log_print("  3. Fine-tune extraction parameters based on your PDF types")
    else:
        log_print("⚠️ Some tests failed. Check the logs above for details.")
        log_print("\n🔧 Troubleshooting:")
        log_print("  1. Ensure all dependencies are installed")
        log_print("  2. Check API server is running: python api_server.py")
        log_print("  3. Verify PDF file is readable and contains text")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
