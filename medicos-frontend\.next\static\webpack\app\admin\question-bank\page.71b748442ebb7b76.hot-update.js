"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q, questionIndex)=>{\n        var _q_topicId;\n        let parsedOptions = [];\n        // Ensure options is an array and filter out null/undefined values\n        const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n        if (safeOptions.length > 0) {\n            if (typeof safeOptions[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = safeOptions[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = safeOptions.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = safeOptions.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                        imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                    }));\n            }\n        } else {\n            // Log warning for questions without valid options\n            console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n            // Fallback: create empty options if none exist\n            parsedOptions = [\n                {\n                    label: 'a',\n                    text: 'No options available'\n                },\n                {\n                    label: 'b',\n                    text: 'No options available'\n                },\n                {\n                    label: 'c',\n                    text: 'No options available'\n                },\n                {\n                    label: 'd',\n                    text: 'No options available'\n                }\n            ];\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus,\n            solution: q.solution,\n            hints: q.hints\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});