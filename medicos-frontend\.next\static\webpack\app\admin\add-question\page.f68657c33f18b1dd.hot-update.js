"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/admin/add-question-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/add-question-form.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_IMAGE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\nconst ACCEPTED_PDF_TYPES = [\n    \"application/pdf\"\n];\n// Manual form schema with custom validation for options\nconst manualFormSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\n// PDF upload form schema\nconst pdfUploadSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional()\n});\nfunction AddQuestionForm() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Manual form states\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [manualTopics, setManualTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // PDF upload states\n    const [pdfTopics, setPdfTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPdfFile, setSelectedPdfFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"AddQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AddQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"AddQuestionForm.useEffect\"], []);\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize manual form\n    const manualForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(manualFormSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Initialize PDF upload form\n    const pdfForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(pdfUploadSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\"\n        }\n    });\n    // Handle subject change for manual form\n    const handleManualSubjectChange = (value)=>{\n        manualForm.setValue(\"subject\", value);\n        manualForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setManualTopics(selectedSubject.topics || []);\n        } else {\n            setManualTopics([]);\n        }\n    };\n    // Handle subject change for PDF form\n    const handlePdfSubjectChange = (value)=>{\n        pdfForm.setValue(\"subject\", value);\n        pdfForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setPdfTopics(selectedSubject.topics || []);\n        } else {\n            setPdfTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            manualForm.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function for manual form\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle manual form submission\n    const onManualSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Manual form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Get user ID from localStorage if available\n            const userData = localStorage.getItem(\"userData\");\n            let userId;\n            try {\n                if (userData) {\n                    const parsed = JSON.parse(userData);\n                    userId = parsed._id || parsed.id;\n                }\n            } catch (e) {\n                console.error(\"Error parsing user data:\", e);\n            }\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add createdBy if we have a valid user ID\n            if (userId) {\n                baseQuestionData.createdBy = userId;\n            }\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            // If question has an image, embed it in the question text as base64\n            let finalQuestionData = {\n                ...questionData\n            };\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.createQuestion)(finalQuestionData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                // Reset manual form\n                resetManualForm();\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error adding question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle PDF form submission\n    const onPdfSubmit = async (data)=>{\n        if (!selectedPdfFile) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"Please select a PDF file to upload.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"PDF form data:\", data);\n            // Submit to bulk upload API\n            const result = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"PDF Upload Successful\",\n                description: \"Successfully uploaded \".concat(result.questionsCreated || 'questions', \" from PDF.\")\n            });\n            // Reset PDF form\n            resetPdfForm();\n        } catch (error) {\n            console.error(\"Error uploading PDF:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to upload PDF. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset manual form\n    const resetManualForm = ()=>{\n        manualForm.reset({\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        });\n        setQuestionImage(null);\n        setOptionImages({\n            A: null,\n            B: null,\n            C: null,\n            D: null\n        });\n        setManualTopics([]);\n        setOptionValidationErrors({\n            A: false,\n            B: false,\n            C: false,\n            D: false\n        });\n    };\n    // Reset PDF form\n    const resetPdfForm = ()=>{\n        pdfForm.reset({\n            subject: \"\",\n            topic: \"\"\n        });\n        setSelectedPdfFile(null);\n        setPdfTopics([]);\n    };\n    // Handle PDF file selection\n    const handlePdfFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (file.type !== \"application/pdf\") {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Invalid File Type\",\n                    description: \"Please select a PDF file.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"File Too Large\",\n                    description: \"File size must be less than 50MB.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setSelectedPdfFile(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    children: \"Add Questions\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"manual\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manual Entry\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"pdf\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upload PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"manual\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...manualForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: manualForm.handleSubmit(onManualSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handleManualSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 494,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: manualTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : manualTopics.length > 0 ? \"Select a topic\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: manualTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"questionText\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Question Text *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                        placeholder: \"Enter your question here...\",\n                                                                        className: \"min-h-[100px]\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Question Image (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Upload an image to accompany your question\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 576,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-9\",\n                                                                    onClick: ()=>{\n                                                                        var _questionImageRef_current;\n                                                                        return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Upload Image\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    ref: questionImageRef,\n                                                                    className: \"hidden\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: (e)=>handleImageUpload(e, \"question\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: questionImage || \"/placeholder.svg\",\n                                                                            alt: \"Question image\",\n                                                                            width: 100,\n                                                                            height: 100,\n                                                                            className: \"object-cover rounded-md border h-[100px] w-[100px]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"destructive\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 absolute -top-2 -right-2 rounded-full\",\n                                                                            onClick: ()=>removeImage(\"question\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 617,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: \"Answer Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                [\n                                                    \"A\",\n                                                    \"B\",\n                                                    \"C\",\n                                                    \"D\"\n                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                                control: manualForm.control,\n                                                                name: \"option\".concat(option),\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-green-600 ml-2\",\n                                                                                        children: \"(Image uploaded)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 642,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 639,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                    placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                                    ...field\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 646,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-red-600\",\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    \" requires either text or an image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 31\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 27\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 mt-8 md:mt-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-9 text-xs\",\n                                                                            onClick: ()=>{\n                                                                                var _optionImageRefs_option_current;\n                                                                                return (_optionImageRefs_option_current = optionImageRefs[option].current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Image\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 668,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            ref: optionImageRefs[option],\n                                                                            className: \"hidden\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>handleImageUpload(e, option)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                    src: optionImages[option] || \"/placeholder.svg\",\n                                                                                    alt: \"Option \".concat(option, \" image\"),\n                                                                                    width: 60,\n                                                                                    height: 60,\n                                                                                    className: \"object-cover rounded-md border h-[60px] w-[60px]\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 688,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: \"destructive\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-5 w-5 absolute -top-2 -right-2 rounded-full\",\n                                                                                    onClick: ()=>removeImage(option),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 702,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 695,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, option, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"correctAnswer\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Correct Answer *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"A\",\n                                                                            \"B\",\n                                                                            \"C\",\n                                                                            \"D\"\n                                                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: option,\n                                                                                            id: \"manual-option-\".concat(option)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 729,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 728,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-option-\".concat(option),\n                                                                                        children: option\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 731,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, option, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"difficulty\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Difficulty Level *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"Easy\",\n                                                                            \"Medium\",\n                                                                            \"Hard\"\n                                                                        ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: level,\n                                                                                            id: \"manual-level-\".concat(level)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 758,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 757,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-level-\".concat(level),\n                                                                                        children: level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 760,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, level, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 756,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                            control: manualForm.control,\n                                            name: \"explanation\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Explanation (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 784,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 787,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                placeholder: \"Explain why the correct answer is right...\",\n                                                                className: \"min-h-[80px]\",\n                                                                ...field\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormDescription, {\n                                                            children: \"This will be shown to students after they answer the question.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding Question...\"\n                                                        ]\n                                                    }, void 0, true) : \"Add Question\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetManualForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"pdf\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...pdfForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: pdfForm.handleSubmit(onPdfSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handlePdfSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 845,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 851,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 856,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: pdfTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : pdfTopics.length > 0 ? \"Select a topic (optional)\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 877,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 876,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: pdfTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 890,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                            children: \"PDF File *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 908,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Upload a PDF file containing questions to be extracted and added to the question bank\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 911,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"pdf-upload\",\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                                                children: selectedPdfFile ? selectedPdfFile.name : \"Choose PDF file or drag and drop\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-1 block text-xs text-gray-500\",\n                                                                                children: \"PDF up to 50MB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 926,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"pdf-upload\",\n                                                                        type: \"file\",\n                                                                        className: \"sr-only\",\n                                                                        accept: \".pdf\",\n                                                                        onChange: handlePdfFileChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 930,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById('pdf-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: \"Select PDF File\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedPdfFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: selectedPdfFile.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (selectedPdfFile.size / 1024 / 1024).toFixed(2),\n                                                                        \" MB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 955,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setSelectedPdfFile(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting || !selectedPdfFile,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Uploading PDF...\"\n                                                        ]\n                                                    }, void 0, true) : \"Upload PDF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetPdfForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 972,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n        lineNumber: 462,\n        columnNumber: 5\n    }, this);\n}\n_s(AddQuestionForm, \"joDXuotR1AEwTKyVAuSv1r8xHX8=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AddQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"AddQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/add-question-form.tsx\n"));

/***/ })

});