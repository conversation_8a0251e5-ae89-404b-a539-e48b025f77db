// Test script to verify image processing in frontend
const { extractImagesFromText } = require('./medicos-frontend/src/utils/imageUtils.ts');

// Test data simulating what comes from the backend
const testQuestionWithImageRefs = {
  text: "Identify X in the series, ![img-5.jpeg](img-5.jpeg)",
  imageData: {
    "img-5": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA=="
  }
};

// Test the image processing
console.log("Testing image processing...");

const result = extractImagesFromText(
  testQuestionWithImageRefs.text, 
  testQuestionWithImageRefs.imageData
);

console.log("Result:", result);
console.log("Clean text:", result.cleanText);
console.log("Images found:", result.images.length);

if (result.images.length > 0) {
  console.log("✅ Image processing working - found", result.images.length, "images");
  console.log("First image:", result.images[0]);
} else {
  console.log("❌ Image processing failed - no images found");
}

// Test with missing image
const testMissingImage = {
  text: "Question with missing image ![img-99.jpeg](img-99.jpeg)",
  imageData: {
    "img-5": "data:image/jpeg;base64,..."
  }
};

const result2 = extractImagesFromText(
  testMissingImage.text, 
  testMissingImage.imageData
);

console.log("\nTesting missing image:");
console.log("Clean text:", result2.cleanText);
console.log("Should show placeholder for missing image");
