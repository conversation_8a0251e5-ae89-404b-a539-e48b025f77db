"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/ui/enhanced-text-renderer.tsx":
/*!******************************************************!*\
  !*** ./src/components/ui/enhanced-text-renderer.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedTextRenderer: () => (/* binding */ EnhancedTextRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_katex__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-katex */ \"(app-pages-browser)/./node_modules/react-katex/dist/react-katex.js\");\n/* harmony import */ var react_katex__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_katex__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ EnhancedTextRenderer auto */ \n\n\n\n\n/**\n * Enhanced text renderer that handles:\n * - LaTeX mathematical expressions\n * - Markdown tables\n * - HTML tables\n * - Regular text formatting\n */ function EnhancedTextRenderer(param) {\n    let { text, className } = param;\n    if (!text) return null;\n    // First, extract and render tables\n    const processedContent = processTablesAndMath(text);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"enhanced-text-renderer\", className),\n        children: processedContent\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c = EnhancedTextRenderer;\nfunction processTablesAndMath(text) {\n    const elements = [];\n    let currentIndex = 0;\n    let elementKey = 0;\n    // First, try to fix malformed tables\n    const cleanedText = fixMalformedTables(text);\n    // Find all tables (both markdown and HTML-like)\n    const tableRegex = /(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g;\n    const htmlTableRegex = /<table[\\s\\S]*?<\\/table>/gi;\n    let match;\n    const tableMatches = [];\n    // Find markdown tables\n    while((match = tableRegex.exec(cleanedText)) !== null){\n        tableMatches.push({\n            start: match.index,\n            end: match.index + match[0].length,\n            content: match[0],\n            type: 'markdown'\n        });\n    }\n    // Find HTML tables\n    while((match = htmlTableRegex.exec(text)) !== null){\n        tableMatches.push({\n            start: match.index,\n            end: match.index + match[0].length,\n            content: match[0],\n            type: 'html'\n        });\n    }\n    // Sort matches by position\n    tableMatches.sort((a, b)=>a.start - b.start);\n    // Process text with tables\n    for (const tableMatch of tableMatches){\n        // Add text before table\n        if (currentIndex < tableMatch.start) {\n            const beforeText = text.slice(currentIndex, tableMatch.start);\n            const mathProcessed = processMathInText(beforeText, elementKey);\n            elements.push(...mathProcessed.elements);\n            elementKey = mathProcessed.nextKey;\n        }\n        // Add table\n        if (tableMatch.type === 'markdown') {\n            const tableElement = renderMarkdownTable(tableMatch.content, elementKey++);\n            if (tableElement) {\n                elements.push(tableElement);\n            }\n        } else {\n            const tableElement = renderHtmlTable(tableMatch.content, elementKey++);\n            if (tableElement) {\n                elements.push(tableElement);\n            }\n        }\n        currentIndex = tableMatch.end;\n    }\n    // Add remaining text\n    if (currentIndex < text.length) {\n        const remainingText = text.slice(currentIndex);\n        const mathProcessed = processMathInText(remainingText, elementKey);\n        elements.push(...mathProcessed.elements);\n    }\n    return elements;\n}\nfunction processMathInText(text, startKey) {\n    const elements = [];\n    let key = startKey;\n    // Split text by math expressions while preserving the delimiters\n    const parts = text.split(/(\\$\\$[\\s\\S]*?\\$\\$|\\$[^$]*?\\$)/);\n    for (const part of parts){\n        // Block math ($$...$$)\n        if (part.startsWith('$$') && part.endsWith('$$')) {\n            const mathContent = part.slice(2, -2).trim();\n            try {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 katex-isolated\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_katex__WEBPACK_IMPORTED_MODULE_2__.BlockMath, {\n                        math: mathContent,\n                        errorColor: \"#dc2626\",\n                        renderError: (error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-red-50 border border-red-200 rounded text-red-700\",\n                                children: [\n                                    \"Error rendering math: \",\n                                    error.message\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this));\n            } catch (error) {\n                console.warn('Error rendering block math:', error);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 p-2 bg-red-50 border border-red-200 rounded text-red-700\",\n                    children: [\n                        \"Error rendering math: \",\n                        mathContent\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this));\n            }\n        } else if (part.startsWith('$') && part.endsWith('$') && part.length > 2) {\n            const mathContent = part.slice(1, -1).trim();\n            try {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_katex__WEBPACK_IMPORTED_MODULE_2__.InlineMath, {\n                    math: mathContent,\n                    errorColor: \"#dc2626\",\n                    renderError: (error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-1 bg-red-50 border border-red-200 rounded text-red-700\",\n                            children: [\n                                \"Error: \",\n                                error.message\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, void 0)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this));\n            } catch (error) {\n                console.warn('Error rendering inline math:', error);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-1 bg-red-50 border border-red-200 rounded text-red-700\",\n                    children: [\n                        \"Error: \",\n                        mathContent\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this));\n            }\n        } else if (part.trim()) {\n            // Process line breaks and basic formatting\n            const formattedText = part.split('\\n').map((line, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                    children: [\n                        line,\n                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 40\n                        }, this)\n                    ]\n                }, \"\".concat(key, \"-line-\").concat(index), true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this));\n            elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: formattedText\n            }, key++, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                lineNumber: 181,\n                columnNumber: 21\n            }, this));\n        }\n    }\n    return {\n        elements,\n        nextKey: key\n    };\n}\nfunction fixMalformedTables(text) {\n    // Fix malformed table syntax like the example:\n    // \"| Age Group | Relative Proportion in <br> Population | | :-- | :-- | | 12−17 | 0.17 | |\"\n    // Pattern to detect malformed tables\n    const malformedTablePattern = /(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g;\n    return text.replace(malformedTablePattern, (match)=>{\n        try {\n            // Clean up the match by removing extra spaces and fixing structure\n            let cleaned = match.trim();\n            // Replace <br> tags with spaces\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            // Split by | and clean up\n            const parts = cleaned.split('|').map((part)=>part.trim()).filter((part)=>part);\n            if (parts.length < 4) return match; // Not enough parts for a table\n            // Try to reconstruct as a proper table\n            const lines = [];\n            let currentLine = [];\n            for(let i = 0; i < parts.length; i++){\n                const part = parts[i];\n                // Check if this looks like a header separator (contains :-- or similar)\n                if (part.match(/^:?-+:?$/)) {\n                    if (currentLine.length > 0) {\n                        lines.push('| ' + currentLine.join(' | ') + ' |');\n                        currentLine = [];\n                    }\n                    // Add separator line\n                    const separators = Array(Math.max(2, currentLine.length || 2)).fill(':--');\n                    lines.push('| ' + separators.join(' | ') + ' |');\n                    continue;\n                }\n                // Check if this looks like a new row (contains numbers or specific patterns)\n                if (part.match(/^\\d+/) && currentLine.length >= 2) {\n                    // Start new row\n                    if (currentLine.length > 0) {\n                        lines.push('| ' + currentLine.join(' | ') + ' |');\n                    }\n                    currentLine = [\n                        part\n                    ];\n                } else {\n                    currentLine.push(part);\n                }\n            }\n            // Add final line\n            if (currentLine.length > 0) {\n                lines.push('| ' + currentLine.join(' | ') + ' |');\n            }\n            // If we have at least 3 lines (header, separator, data), return the table\n            if (lines.length >= 3) {\n                return '\\n\\n' + lines.join('\\n') + '\\n\\n';\n            }\n            return match; // Return original if we can't fix it\n        } catch (error) {\n            console.warn('Error fixing malformed table:', error);\n            return match;\n        }\n    });\n}\nfunction parseMarkdownTable(tableText) {\n    try {\n        const lines = tableText.trim().split('\\n').filter((line)=>line.trim());\n        if (lines.length < 2) return null;\n        // Parse header\n        const headerLine = lines[0];\n        const headers = headerLine.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n        // Parse alignment line\n        const alignmentLine = lines[1];\n        const alignments = alignmentLine.split('|').map((cell)=>{\n            const trimmed = cell.trim();\n            if (trimmed.startsWith(':') && trimmed.endsWith(':')) return 'center';\n            if (trimmed.endsWith(':')) return 'right';\n            return 'left';\n        }).filter((_, index)=>index < headers.length);\n        // Parse data rows\n        const rows = [];\n        for(let i = 2; i < lines.length; i++){\n            const line = lines[i];\n            const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n            if (cells.length > 0) {\n                // Pad cells to match header count\n                while(cells.length < headers.length){\n                    cells.push('');\n                }\n                rows.push(cells.slice(0, headers.length));\n            }\n        }\n        return {\n            headers,\n            rows,\n            alignments\n        };\n    } catch (error) {\n        console.warn('Error parsing markdown table:', error);\n        return null;\n    }\n}\nfunction renderMarkdownTable(tableText, key) {\n    const tableData = parseMarkdownTable(tableText);\n    if (!tableData) return null;\n    const { headers, rows, alignments } = tableData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: headers.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border border-gray-300 px-4 py-2 font-semibold text-gray-900\", alignments[index] === 'center' && \"text-center\", alignments[index] === 'right' && \"text-right\", alignments[index] === 'left' && \"text-left\"),\n                                children: header\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: rows.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                            children: row.map((cell, cellIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border border-gray-300 px-4 py-2 text-gray-700\", alignments[cellIndex] === 'center' && \"text-center\", alignments[cellIndex] === 'right' && \"text-right\", alignments[cellIndex] === 'left' && \"text-left\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedTextRenderer, {\n                                        text: cell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this)\n                                }, cellIndex, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 17\n                                }, this))\n                        }, rowIndex, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, this)\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, this);\n}\nfunction renderHtmlTable(tableHtml, key) {\n    // For HTML tables, we'll render them as-is but with better styling\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 overflow-x-auto\",\n        dangerouslySetInnerHTML: {\n            __html: tableHtml.replace(/<table/g, '<table class=\"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg\"').replace(/<th/g, '<th class=\"border border-gray-300 px-4 py-2 font-semibold text-gray-900 bg-gray-50\"').replace(/<td/g, '<td class=\"border border-gray-300 px-4 py-2 text-gray-700\"')\n        }\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, this);\n}\nvar _c;\n$RefreshReg$(_c, \"EnhancedTextRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/enhanced-text-renderer.tsx\n"));

/***/ })

});