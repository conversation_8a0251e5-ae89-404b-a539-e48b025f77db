"use client"

import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import QuestionList from "./question-list"
import { Pagination } from "@/components/ui/pagination"
import { QuestionSkeleton } from "./question-skeleton"
import { toast } from "@/components/ui/use-toast"
import { ApiQuestion, FormattedQuestion, PaginationInfo, Subject, Topic } from "@/types/question"
import { isBase64Image, ensureDataUrl } from "@/utils/imageUtils"
import { reviewQuestion } from "@/lib/api/questions"

export default function QuestionBank() {
  // State for subjects and topics
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [topics, setTopics] = useState<Topic[]>([])
  
  // State for filters
  const [selectedSubject, setSelectedSubject] = useState<string>("all_subjects")
  const [selectedTopic, setSelectedTopic] = useState<string>("all_topics")
  const [searchQuery, setSearchQuery] = useState<string>("")
  
  // State for questions and pagination
  const [questions, setQuestions] = useState<ApiQuestion[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  })
  const [pageSize, setPageSize] = useState<number>(10)
  const [loading, setLoading] = useState<boolean>(true)

  // Fetch subjects with topics
  useEffect(() => {
    const fetchSubjectsWithTopics = async () => {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
        const response = await fetch(`${baseUrl}/subjects/with-topics`, {
          headers: {
            "Authorization": `Bearer ${localStorage.getItem("backendToken")}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch subjects: ${response.status}`);
        }
        
        const data = await response.json();
        setSubjects(data);
      } catch (error) {
        console.error("Error fetching subjects:", error);
        toast({
          title: "Error",
          description: "Failed to load subjects. Please try again.",
          variant: "destructive"
        });
      }
    };
    
    fetchSubjectsWithTopics();
  }, []);

  // Update topics when subject changes
  useEffect(() => {
    if (selectedSubject && selectedSubject !== "all_subjects") {
      const selectedSubjectObj = subjects.find(s => s._id === selectedSubject);
      if (selectedSubjectObj && selectedSubjectObj.topics) {
        setTopics(selectedSubjectObj.topics);
      } else {
        setTopics([]);
      }
      setSelectedTopic("all_topics");
    } else {
      setTopics([]);
    }
  }, [selectedSubject, subjects]);

  // Fetch questions with filters
  useEffect(() => {
    const fetchQuestions = async () => {
      setLoading(true);
      try {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
        
        // Build query parameters
        const params = new URLSearchParams();
        if (selectedSubject && selectedSubject !== "all_subjects") params.append('subjectId', selectedSubject);
        if (selectedTopic && selectedTopic !== "all_topics") params.append('topicId', selectedTopic);
        if (searchQuery) params.append('search', searchQuery);
        params.append('page', pagination.currentPage.toString());
        params.append('limit', pageSize.toString());
        
        const response = await fetch(`${baseUrl}/questions?${params.toString()}`, {
          headers: {
            "Authorization": `Bearer ${localStorage.getItem("backendToken")}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch questions: ${response.status}`);
        }
        
        const data = await response.json();
        setQuestions(data.questions);
        setPagination(data.pagination);
      } catch (error) {
        console.error("Error fetching questions:", error);
        toast({
          title: "Error",
          description: "Failed to load questions. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchQuestions();
  }, [selectedSubject, selectedTopic, searchQuery, pagination.currentPage, pageSize]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setPagination(prev => ({
      ...prev,
      currentPage: pageNumber
    }));
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPagination(prev => ({
      ...prev,
      currentPage: 1, // Reset to first page when changing page size
      itemsPerPage: newPageSize
    }));
  };

  // Handle difficulty change
  const handleDifficultyChange = async (questionId: string, difficulty: string) => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
      const response = await fetch(`${baseUrl}/questions/${questionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("backendToken")}`
        },
        body: JSON.stringify({ difficulty })
      });

      if (!response.ok) {
        throw new Error(`Failed to update question: ${response.status}`);
      }

      // Update local state
      setQuestions(prev =>
        prev.map(q => q._id === questionId ? { ...q, difficulty } : q)
      );

      toast({
        title: "Success",
        description: "Question difficulty updated successfully",
      });
    } catch (error) {
      console.error("Error updating question difficulty:", error);
      toast({
        title: "Error",
        description: "Failed to update question difficulty",
        variant: "destructive"
      });
    }
  };

  // Handle review status change
  const handleReviewStatusChange = async (questionId: string, reviewStatus: string) => {
    try {
      await reviewQuestion(questionId, reviewStatus as 'approved' | 'rejected');

      // Update local state
      setQuestions(prev =>
        prev.map(q => q._id === questionId ? { ...q, reviewStatus } : q)
      );

      toast({
        title: "Success",
        description: `Question ${reviewStatus} successfully`,
      });
    } catch (error) {
      console.error("Error updating question review status:", error);
      toast({
        title: "Error",
        description: "Failed to update question review status",
        variant: "destructive"
      });
    }
  };

  // Format questions for the QuestionList component
  const formattedQuestions: FormattedQuestion[] = questions.map(q => {
    try {
    // Parse options - they might be a string array or an array of objects
    interface ParsedOption {
      label: string;
      text: string;
      imageUrl?: string;
    }
    let parsedOptions: ParsedOption[] = [];

    // Ensure options is an array and filter out null/undefined values
    const safeOptions = Array.isArray(q.options) ? q.options.filter(opt => opt !== null && opt !== undefined) : [];

    if (safeOptions.length > 0) {
      if (typeof safeOptions[0] === 'string') {
        // Check if it's a single comma-separated string or an array of individual strings
        if (safeOptions.length === 1 && safeOptions[0].includes(',')) {
          // Single comma-separated string: ["Paris,London,Berlin,Madrid"]
          const optionTexts = (safeOptions as string[])[0].split(',');
          parsedOptions = optionTexts.map((text, index) => {
            const trimmedText = text.trim();

            // Check if the text is a base64 image
            if (isBase64Image(trimmedText)) {
              return {
                label: String.fromCharCode(97 + index), // a, b, c, d...
                text: '', // Empty text since this is an image option
                imageUrl: ensureDataUrl(trimmedText),
                isImageOption: true
              };
            }

            return {
              label: String.fromCharCode(97 + index), // a, b, c, d...
              text: trimmedText
            };
          });
        } else {
          // Array of individual strings: ["Cerebrum", "Cerebellum", "Medulla", "Pons"]
          parsedOptions = (safeOptions as string[]).map((text, index) => {
            const trimmedText = text.trim();

            // Check if the text is a base64 image
            if (isBase64Image(trimmedText)) {
              return {
                label: String.fromCharCode(97 + index), // a, b, c, d...
                text: '', // Empty text since this is an image option
                imageUrl: ensureDataUrl(trimmedText),
                isImageOption: true
              };
            }

            return {
              label: String.fromCharCode(97 + index), // a, b, c, d...
              text: trimmedText
            };
          });
        }
      } else {
        // If options is already an array of objects
        parsedOptions = (safeOptions as any[]).map((opt, index) => ({
          label: String.fromCharCode(97 + index),
          text: typeof opt === 'string' ? opt : (opt && opt.text) || '',
          imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined
        }));
      }
    } else {
      // Log warning for questions without valid options
      console.warn(`Question ${q._id} has no valid options:`, q.options);

      // Fallback: create empty options if none exist
      parsedOptions = [
        { label: 'a', text: 'No options available' },
        { label: 'b', text: 'No options available' },
        { label: 'c', text: 'No options available' },
        { label: 'd', text: 'No options available' }
      ];
    }

    return {
      id: q._id,
      subject: q.subjectId.name,
      topic: q.topicId?.name || "No Topic",
      text: q.content,
      options: parsedOptions,
      difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1), // Capitalize
      correctAnswer: q.answer,
      reviewStatus: q.reviewStatus,
      solution: q.solution,
      hints: q.hints
    };
    } catch (error) {
      console.error(`Error formatting question ${q._id}:`, error, q);
      // Return a fallback question structure
      return {
        id: q._id || 'unknown',
        subject: q.subjectId?.name || 'Unknown Subject',
        topic: q.topicId?.name || 'No Topic',
        text: q.content || 'Error loading question content',
        options: [
          { label: 'a', text: 'Error loading options' },
          { label: 'b', text: 'Error loading options' },
          { label: 'c', text: 'Error loading options' },
          { label: 'd', text: 'Error loading options' }
        ],
        difficulty: q.difficulty || 'Unknown',
        correctAnswer: q.answer || 'a',
        reviewStatus: q.reviewStatus || 'pending',
        solution: q.solution,
        hints: q.hints
      };
    }
  });

  return (
    <div className="space-y-6">
      {/* Header Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Subject</label>
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger>
              <SelectValue placeholder="Select Subject" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_subjects">All Subjects</SelectItem>
              {subjects.map((subject) => (
                <SelectItem key={subject._id} value={subject._id}>
                  {subject.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Topic</label>
          <Select
            value={selectedTopic}
            onValueChange={setSelectedTopic}
            disabled={selectedSubject === "all_subjects" || topics.length === 0}
          >
            <SelectTrigger>
              <SelectValue placeholder={selectedSubject !== "all_subjects" ? "Select Topic" : "Select Subject First"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_topics">All Topics</SelectItem>
              {topics.map((topic) => (
                <SelectItem key={topic._id} value={topic._id}>
                  {topic.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Search</label>
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search questions..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Question List */}
      {loading ? (
        <div className="space-y-4">
          <QuestionSkeleton />
          <QuestionSkeleton />
          <QuestionSkeleton />
        </div>
      ) : formattedQuestions.length > 0 ? (
        <>
          <QuestionList
            questions={formattedQuestions}
            onDifficultyChange={handleDifficultyChange}
            onReviewStatusChange={handleReviewStatusChange}
            onQuestionDeleted={() => {
              // Refresh the questions list after deletion
              setPagination(prev => ({ ...prev, currentPage: 1 }));
              // The useEffect will automatically refetch when pagination changes
            }}
          />

          {/* Pagination */}
          {pagination.totalItems > 0 && (
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              pageSize={pageSize}
              totalItems={pagination.totalItems}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              pageSizeOptions={[5, 10, 20, 50]}
            />
          )}
        </>
      ) : (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No questions found. Try adjusting your filters.</p>
        </div>
      )}
    </div>
  )
}
