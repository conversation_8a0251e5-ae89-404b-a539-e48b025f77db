"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        try {\n            var _q_topicId;\n            let parsedOptions = [];\n            // Ensure options is an array and filter out null/undefined values\n            const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n            if (safeOptions.length > 0) {\n                if (typeof safeOptions[0] === 'string') {\n                    // Check if it's a single comma-separated string or an array of individual strings\n                    if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                        // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                        const optionTexts = safeOptions[0].split(',');\n                        parsedOptions = optionTexts.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    } else {\n                        // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                        parsedOptions = safeOptions.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    }\n                } else {\n                    // If options is already an array of objects\n                    parsedOptions = safeOptions.map((opt, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                            imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                        }));\n                }\n            } else {\n                // Log warning for questions without valid options\n                console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n                // Fallback: create empty options if none exist\n                parsedOptions = [\n                    {\n                        label: 'a',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'b',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'c',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'd',\n                        text: 'No options available'\n                    }\n                ];\n            }\n            return {\n                id: q._id,\n                subject: q.subjectId.name,\n                topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n                text: q.content,\n                options: parsedOptions,\n                difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n                correctAnswer: q.answer,\n                reviewStatus: q.reviewStatus,\n                solution: q.solution,\n                hints: q.hints\n            };\n        } catch (error) {\n            var _q_subjectId, _q_topicId1;\n            console.error(\"Error formatting question \".concat(q._id, \":\"), error, q);\n            // Return a fallback question structure\n            return {\n                id: q._id || 'unknown',\n                subject: ((_q_subjectId = q.subjectId) === null || _q_subjectId === void 0 ? void 0 : _q_subjectId.name) || 'Unknown Subject',\n                topic: ((_q_topicId1 = q.topicId) === null || _q_topicId1 === void 0 ? void 0 : _q_topicId1.name) || 'No Topic',\n                text: q.content || 'Error loading question content',\n                options: [\n                    {\n                        label: 'a',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'b',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'c',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'd',\n                        text: 'Error loading options'\n                    }\n                ],\n                difficulty: q.difficulty || 'Unknown',\n                correctAnswer: q.answer || 'a',\n                reviewStatus: q.reviewStatus || 'pending',\n                solution: q.solution,\n                hints: q.hints\n            };\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});