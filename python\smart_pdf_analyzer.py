#!/usr/bin/env python3
"""
Smart PDF Analyzer for Question Papers
Analyzes PDF structure and content to optimize extraction strategy
"""

import os
import sys
import re
import json
from typing import Dict, List, Tuple, Optional
from collections import Counter

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class SmartPDFAnalyzer:
    """
    Intelligent PDF analyzer that determines the best extraction strategy
    based on PDF content structure and patterns
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_pdf_structure(self, pdf_path: str) -> Dict:
        """
        Comprehensive PDF structure analysis
        
        Args:
            pdf_path (str): Path to PDF file
            
        Returns:
            Dict: Analysis results with extraction recommendations
        """
        try:
            log_print("🔍 [ANALYZER] Starting comprehensive PDF analysis...")
            
            # Import extractor for OCR
            from question_extractor import QuestionExtractor
            extractor = QuestionExtractor(ai_provider='gemini')
            
            # Extract text and images
            log_print("📄 [ANALYZER] Extracting text and images...")
            ocr_data = extractor.extract_ocr_data_from_pdf(pdf_path)
            full_text = ocr_data['full_text']
            all_images = ocr_data['all_images']
            
            # Analyze different aspects
            structure_analysis = self._analyze_structure_patterns(full_text)
            content_analysis = self._analyze_content_quality(full_text)
            question_analysis = self._analyze_question_patterns(full_text)
            format_analysis = self._analyze_format_type(full_text)
            image_analysis = self._analyze_image_content(all_images)
            
            # Combine all analyses
            comprehensive_analysis = {
                'file_info': {
                    'path': pdf_path,
                    'size_mb': os.path.getsize(pdf_path) / (1024 * 1024),
                    'text_length': len(full_text),
                    'image_count': len(all_images)
                },
                'structure': structure_analysis,
                'content_quality': content_analysis,
                'question_patterns': question_analysis,
                'format_type': format_analysis,
                'image_analysis': image_analysis,
                'extraction_strategy': self._recommend_extraction_strategy(
                    structure_analysis, content_analysis, question_analysis, format_analysis
                )
            }
            
            log_print("✅ [ANALYZER] Analysis completed successfully")
            return comprehensive_analysis
            
        except Exception as e:
            log_print(f"❌ [ANALYZER] Analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_structure_patterns(self, text: str) -> Dict:
        """Analyze structural patterns in the text"""
        patterns = {
            # Question numbering patterns
            'numbered_questions': len(re.findall(r'^\s*\d+\.\s*', text, re.MULTILINE)),
            'q_format': len(re.findall(r'Q\.?\s*\d+', text, re.IGNORECASE)),
            'question_word': len(re.findall(r'Question\s*\d+', text, re.IGNORECASE)),
            
            # Option patterns
            'option_a_paren': len(re.findall(r'\ba\)', text, re.IGNORECASE)),
            'option_a_bracket': len(re.findall(r'\(a\)', text, re.IGNORECASE)),
            'option_a_dot': len(re.findall(r'\ba\.', text, re.IGNORECASE)),
            
            # Answer key patterns
            'answer_key_format1': len(re.findall(r'\d+\.\s*[ABCD]', text)),
            'answer_key_format2': len(re.findall(r'\d+\)\s*[abcd]', text)),
            'answer_key_format3': len(re.findall(r'Ans[:\.]?\s*[ABCD]', text, re.IGNORECASE)),
            
            # Section patterns
            'section_headers': len(re.findall(r'Section\s*[A-Z]', text, re.IGNORECASE)),
            'part_headers': len(re.findall(r'Part\s*[A-Z]', text, re.IGNORECASE)),
            
            # Special patterns
            'multiple_choice': len(re.findall(r'multiple\s*choice', text, re.IGNORECASE)),
            'choose_correct': len(re.findall(r'choose.*correct', text, re.IGNORECASE)),
        }
        
        # Determine dominant patterns
        dominant_question_pattern = max([
            ('numbered', patterns['numbered_questions']),
            ('q_format', patterns['q_format']),
            ('question_word', patterns['question_word'])
        ], key=lambda x: x[1])
        
        dominant_option_pattern = max([
            ('parentheses', patterns['option_a_paren']),
            ('brackets', patterns['option_a_bracket']),
            ('dots', patterns['option_a_dot'])
        ], key=lambda x: x[1])
        
        return {
            'patterns': patterns,
            'dominant_question_pattern': dominant_question_pattern,
            'dominant_option_pattern': dominant_option_pattern,
            'has_answer_key': max(patterns['answer_key_format1'], 
                                patterns['answer_key_format2'], 
                                patterns['answer_key_format3']) > 0,
            'has_sections': patterns['section_headers'] > 0 or patterns['part_headers'] > 0
        }
    
    def _analyze_content_quality(self, text: str) -> Dict:
        """Analyze text quality and OCR issues"""
        # Basic quality metrics
        total_chars = len(text)
        if total_chars == 0:
            return {'quality_score': 0, 'issues': ['no_text_extracted']}
        
        # Count different types of characters
        letters = len(re.findall(r'[a-zA-Z]', text))
        digits = len(re.findall(r'\d', text))
        spaces = len(re.findall(r'\s', text))
        punctuation = len(re.findall(r'[.,;:!?()]', text))
        special_chars = total_chars - letters - digits - spaces - punctuation
        
        # Quality indicators
        letter_ratio = letters / total_chars
        digit_ratio = digits / total_chars
        special_ratio = special_chars / total_chars
        
        # OCR issues
        issues = []
        if special_ratio > 0.1:
            issues.append('high_special_characters')
        if letter_ratio < 0.5:
            issues.append('low_letter_content')
        
        # Repeated character patterns (OCR artifacts)
        repeated_chars = len(re.findall(r'(.)\1{5,}', text))
        if repeated_chars > 10:
            issues.append('repeated_characters')
        
        # Broken words
        broken_words = len(re.findall(r'\b\w{1,2}\b', text))
        if broken_words > total_chars / 100:
            issues.append('broken_words')
        
        # Calculate quality score
        quality_score = min(100, max(0, 
            100 - (special_ratio * 200) - (len(issues) * 20)
        ))
        
        return {
            'quality_score': quality_score,
            'character_distribution': {
                'letters': letter_ratio,
                'digits': digit_ratio,
                'spaces': spaces / total_chars,
                'punctuation': punctuation / total_chars,
                'special': special_ratio
            },
            'issues': issues,
            'total_characters': total_chars
        }
    
    def _analyze_question_patterns(self, text: str) -> Dict:
        """Analyze question-specific patterns"""
        # Find potential questions
        question_indicators = [
            r'\?',  # Question marks
            r'what\s+is', r'which\s+of', r'how\s+many',
            r'calculate', r'find\s+the', r'determine',
            r'choose', r'select', r'identify'
        ]
        
        question_count_estimates = []
        for pattern in question_indicators:
            matches = len(re.findall(pattern, text, re.IGNORECASE))
            question_count_estimates.append(matches)
        
        # Estimate based on different methods
        estimates = {
            'by_question_marks': len(re.findall(r'\?', text)),
            'by_numbering': len(re.findall(r'^\s*\d+\.\s*', text, re.MULTILINE)),
            'by_keywords': max(question_count_estimates),
            'by_options': len(re.findall(r'\ba\)', text, re.IGNORECASE)) // 4  # Assume 4 options per question
        }
        
        # Best estimate
        best_estimate = max(estimates.values())
        
        # Question complexity analysis
        math_indicators = len(re.findall(r'[+\-*/=∫∑√π]', text))
        formula_indicators = len(re.findall(r'[a-z]\s*=\s*[a-z0-9]', text))
        
        return {
            'estimates': estimates,
            'best_estimate': best_estimate,
            'complexity_indicators': {
                'math_symbols': math_indicators,
                'formulas': formula_indicators,
                'has_math_content': math_indicators > 10 or formula_indicators > 5
            }
        }
    
    def _analyze_format_type(self, text: str) -> Dict:
        """Determine the format type of the PDF"""
        # Check for different format indicators
        format_indicators = {
            'structured_exam': (
                len(re.findall(r'answer\s*key', text, re.IGNORECASE)) > 0 and
                len(re.findall(r'^\s*\d+\.\s*[ABCD]', text, re.MULTILINE)) > 10
            ),
            'question_bank': (
                len(re.findall(r'^\s*\d+\.\s*', text, re.MULTILINE)) > 50 and
                len(re.findall(r'\ba\)', text, re.IGNORECASE)) > 50
            ),
            'practice_test': (
                len(re.findall(r'test|exam|practice', text, re.IGNORECASE)) > 0 and
                len(re.findall(r'time|duration|marks', text, re.IGNORECASE)) > 0
            ),
            'textbook_questions': (
                len(re.findall(r'chapter|exercise|problem', text, re.IGNORECASE)) > 0
            )
        }
        
        # Determine most likely format
        likely_format = 'unknown'
        for format_type, is_match in format_indicators.items():
            if is_match:
                likely_format = format_type
                break
        
        return {
            'format_indicators': format_indicators,
            'likely_format': likely_format
        }
    
    def _analyze_image_content(self, images: Dict) -> Dict:
        """Analyze image content in the PDF"""
        if not images:
            return {'has_images': False, 'image_count': 0}
        
        # Basic image analysis
        image_sizes = []
        for img_id, img_data in images.items():
            if isinstance(img_data, str) and img_data.startswith('data:image'):
                # Estimate size from base64 length
                base64_part = img_data.split(',')[1] if ',' in img_data else img_data
                estimated_size = len(base64_part) * 3 / 4  # Base64 to bytes conversion
                image_sizes.append(estimated_size)
        
        return {
            'has_images': len(images) > 0,
            'image_count': len(images),
            'average_image_size': sum(image_sizes) / len(image_sizes) if image_sizes else 0,
            'large_images': sum(1 for size in image_sizes if size > 50000),  # > 50KB
            'likely_diagrams': len(images) > 10  # Many small images likely diagrams
        }
    
    def _recommend_extraction_strategy(self, structure: Dict, quality: Dict, 
                                     questions: Dict, format_info: Dict) -> Dict:
        """Recommend the best extraction strategy based on analysis"""
        recommendations = {
            'primary_method': 'advanced_multi_stage',
            'chunking_strategy': 'adaptive',
            'ai_provider': 'gemini',
            'use_patterns': True,
            'use_adobe': True,
            'confidence': 0.8
        }
        
        # Adjust based on quality
        if quality['quality_score'] < 50:
            recommendations['primary_method'] = 'pattern_heavy'
            recommendations['use_patterns'] = True
            recommendations['confidence'] = 0.6
        
        # Adjust based on question count
        estimated_questions = questions['best_estimate']
        if estimated_questions > 200:
            recommendations['chunking_strategy'] = 'aggressive'
            recommendations['ai_provider'] = 'gemini'  # Better for large files
        elif estimated_questions < 20:
            recommendations['chunking_strategy'] = 'minimal'
            recommendations['primary_method'] = 'standard'
        
        # Adjust based on format
        if format_info['likely_format'] == 'structured_exam':
            recommendations['use_patterns'] = True
            recommendations['primary_method'] = 'pattern_first'
        
        # Adjust based on structure
        if structure['has_answer_key']:
            recommendations['extract_answers'] = True
        
        return recommendations
