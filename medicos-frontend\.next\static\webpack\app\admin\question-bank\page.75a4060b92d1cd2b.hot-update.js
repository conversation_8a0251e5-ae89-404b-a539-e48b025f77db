"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/ui/enhanced-text-renderer.tsx":
/*!******************************************************!*\
  !*** ./src/components/ui/enhanced-text-renderer.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedTextRenderer: () => (/* binding */ EnhancedTextRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_katex__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-katex */ \"(app-pages-browser)/./node_modules/react-katex/dist/react-katex.js\");\n/* harmony import */ var react_katex__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_katex__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ EnhancedTextRenderer auto */ \n\n\n\n\n/**\n * Enhanced text renderer that handles:\n * - LaTeX mathematical expressions\n * - Markdown tables\n * - HTML tables\n * - Regular text formatting\n */ function EnhancedTextRenderer(param) {\n    let { text, className } = param;\n    if (!text) return null;\n    // First, extract and render tables\n    const processedContent = processTablesAndMath(text);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"enhanced-text-renderer\", className),\n        children: processedContent\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c = EnhancedTextRenderer;\nfunction processTablesAndMath(text) {\n    const elements = [];\n    let currentIndex = 0;\n    let elementKey = 0;\n    // First, try to fix malformed tables\n    const cleanedText = fixMalformedTables(text);\n    // Find all tables (both markdown and HTML-like)\n    const tableRegex = /(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g;\n    const htmlTableRegex = /<table[\\s\\S]*?<\\/table>/gi;\n    let match;\n    const tableMatches = [];\n    // Find markdown tables\n    while((match = tableRegex.exec(cleanedText)) !== null){\n        tableMatches.push({\n            start: match.index,\n            end: match.index + match[0].length,\n            content: match[0],\n            type: 'markdown'\n        });\n    }\n    // Find HTML tables\n    while((match = htmlTableRegex.exec(text)) !== null){\n        tableMatches.push({\n            start: match.index,\n            end: match.index + match[0].length,\n            content: match[0],\n            type: 'html'\n        });\n    }\n    // Sort matches by position\n    tableMatches.sort((a, b)=>a.start - b.start);\n    // Process text with tables\n    for (const tableMatch of tableMatches){\n        // Add text before table\n        if (currentIndex < tableMatch.start) {\n            const beforeText = text.slice(currentIndex, tableMatch.start);\n            const mathProcessed = processMathInText(beforeText, elementKey);\n            elements.push(...mathProcessed.elements);\n            elementKey = mathProcessed.nextKey;\n        }\n        // Add table\n        if (tableMatch.type === 'markdown') {\n            const tableElement = renderMarkdownTable(tableMatch.content, elementKey++);\n            if (tableElement) {\n                elements.push(tableElement);\n            }\n        } else {\n            const tableElement = renderHtmlTable(tableMatch.content, elementKey++);\n            if (tableElement) {\n                elements.push(tableElement);\n            }\n        }\n        currentIndex = tableMatch.end;\n    }\n    // Add remaining text\n    if (currentIndex < text.length) {\n        const remainingText = text.slice(currentIndex);\n        const mathProcessed = processMathInText(remainingText, elementKey);\n        elements.push(...mathProcessed.elements);\n    }\n    return elements;\n}\nfunction processMathInText(text, startKey) {\n    const elements = [];\n    let key = startKey;\n    // Split text by math expressions while preserving the delimiters\n    const parts = text.split(/(\\$\\$[\\s\\S]*?\\$\\$|\\$[^$]*?\\$)/);\n    for (const part of parts){\n        // Block math ($$...$$)\n        if (part.startsWith('$$') && part.endsWith('$$')) {\n            const mathContent = part.slice(2, -2).trim();\n            try {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 katex-isolated\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_katex__WEBPACK_IMPORTED_MODULE_2__.BlockMath, {\n                        math: mathContent,\n                        errorColor: \"#dc2626\",\n                        renderError: (error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-red-50 border border-red-200 rounded text-red-700\",\n                                children: [\n                                    \"Error rendering math: \",\n                                    error.message\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this));\n            } catch (error) {\n                console.warn('Error rendering block math:', error);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 p-2 bg-red-50 border border-red-200 rounded text-red-700\",\n                    children: [\n                        \"Error rendering math: \",\n                        mathContent\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this));\n            }\n        } else if (part.startsWith('$') && part.endsWith('$') && part.length > 2) {\n            const mathContent = part.slice(1, -1).trim();\n            try {\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_katex__WEBPACK_IMPORTED_MODULE_2__.InlineMath, {\n                    math: mathContent,\n                    errorColor: \"#dc2626\",\n                    renderError: (error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-1 bg-red-50 border border-red-200 rounded text-red-700\",\n                            children: [\n                                \"Error: \",\n                                error.message\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, void 0)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this));\n            } catch (error) {\n                console.warn('Error rendering inline math:', error);\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-1 bg-red-50 border border-red-200 rounded text-red-700\",\n                    children: [\n                        \"Error: \",\n                        mathContent\n                    ]\n                }, key++, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this));\n            }\n        } else if (part.trim()) {\n            // Process line breaks and basic formatting\n            const formattedText = part.split('\\n').map((line, index, array)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                    children: [\n                        line,\n                        index < array.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 40\n                        }, this)\n                    ]\n                }, \"\".concat(key, \"-line-\").concat(index), true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this));\n            elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: formattedText\n            }, key++, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                lineNumber: 181,\n                columnNumber: 21\n            }, this));\n        }\n    }\n    return {\n        elements,\n        nextKey: key\n    };\n}\nfunction parseMarkdownTable(tableText) {\n    try {\n        const lines = tableText.trim().split('\\n').filter((line)=>line.trim());\n        if (lines.length < 2) return null;\n        // Parse header\n        const headerLine = lines[0];\n        const headers = headerLine.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n        // Parse alignment line\n        const alignmentLine = lines[1];\n        const alignments = alignmentLine.split('|').map((cell)=>{\n            const trimmed = cell.trim();\n            if (trimmed.startsWith(':') && trimmed.endsWith(':')) return 'center';\n            if (trimmed.endsWith(':')) return 'right';\n            return 'left';\n        }).filter((_, index)=>index < headers.length);\n        // Parse data rows\n        const rows = [];\n        for(let i = 2; i < lines.length; i++){\n            const line = lines[i];\n            const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n            if (cells.length > 0) {\n                // Pad cells to match header count\n                while(cells.length < headers.length){\n                    cells.push('');\n                }\n                rows.push(cells.slice(0, headers.length));\n            }\n        }\n        return {\n            headers,\n            rows,\n            alignments\n        };\n    } catch (error) {\n        console.warn('Error parsing markdown table:', error);\n        return null;\n    }\n}\nfunction renderMarkdownTable(tableText, key) {\n    const tableData = parseMarkdownTable(tableText);\n    if (!tableData) return null;\n    const { headers, rows, alignments } = tableData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: headers.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border border-gray-300 px-4 py-2 font-semibold text-gray-900\", alignments[index] === 'center' && \"text-center\", alignments[index] === 'right' && \"text-right\", alignments[index] === 'left' && \"text-left\"),\n                                children: header\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: rows.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                            children: row.map((cell, cellIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border border-gray-300 px-4 py-2 text-gray-700\", alignments[cellIndex] === 'center' && \"text-center\", alignments[cellIndex] === 'right' && \"text-right\", alignments[cellIndex] === 'left' && \"text-left\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedTextRenderer, {\n                                        text: cell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, cellIndex, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, this))\n                        }, rowIndex, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this)\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\nfunction renderHtmlTable(tableHtml, key) {\n    // For HTML tables, we'll render them as-is but with better styling\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 overflow-x-auto\",\n        dangerouslySetInnerHTML: {\n            __html: tableHtml.replace(/<table/g, '<table class=\"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg\"').replace(/<th/g, '<th class=\"border border-gray-300 px-4 py-2 font-semibold text-gray-900 bg-gray-50\"').replace(/<td/g, '<td class=\"border border-gray-300 px-4 py-2 text-gray-700\"')\n        }\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\enhanced-text-renderer.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\nvar _c;\n$RefreshReg$(_c, \"EnhancedTextRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/enhanced-text-renderer.tsx\n"));

/***/ })

});