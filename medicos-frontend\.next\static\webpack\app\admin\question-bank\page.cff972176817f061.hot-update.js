"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-list.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/text-with-images */ \"(app-pages-browser)/./src/components/ui/text-with-images.tsx\");\n/* harmony import */ var _components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/chemical-image-display */ \"(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction QuestionList(param) {\n    let { questions, onDifficultyChange, onReviewStatusChange, onQuestionDeleted } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionToDelete, setQuestionToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedSolutions, setExpandedSolutions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [expandedHints, setExpandedHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Toggle solution expansion\n    const toggleSolution = (questionId)=>{\n        setExpandedSolutions((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Toggle hints expansion\n    const toggleHints = (questionId)=>{\n        setExpandedHints((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Handle delete button click\n    const handleDelete = (questionId)=>{\n        setQuestionToDelete(questionId);\n        setIsDeleteDialogOpen(true);\n    };\n    // Handle edit button click\n    const handleEdit = (questionId)=>{\n        router.push(\"/admin/edit-question/\".concat(questionId));\n    };\n    // Confirm delete action\n    const confirmDelete = async ()=>{\n        if (!questionToDelete) return;\n        try {\n            setIsDeleting(true);\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_10__.deleteQuestion)(questionToDelete);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                if (onQuestionDeleted) {\n                    onQuestionDeleted() // Refresh the list\n                    ;\n                }\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error deleting question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n            setIsDeleteDialogOpen(false);\n            setQuestionToDelete(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap items-center justify-between gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.subject\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.topic\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                question.reviewStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    className: question.reviewStatus === \"approved\" ? \"bg-green-100 text-green-800 hover:bg-green-100\" : question.reviewStatus === \"rejected\" ? \"bg-red-100 text-red-800 hover:bg-red-100\" : \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                    children: question.reviewStatus.charAt(0).toUpperCase() + question.reviewStatus.slice(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.difficulty.toLowerCase(),\n                                                    onValueChange: (value)=>onDifficultyChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"easy\",\n                                                                    children: \"Easy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"hard\",\n                                                                    children: \"Hard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.reviewStatus.toLowerCase(),\n                                                    onValueChange: (value)=>onReviewStatusChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Review Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"pending\",\n                                                                    children: \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"approved\",\n                                                                    children: \"Approved\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"rejected\",\n                                                                    children: \"Rejected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleEdit(question.id),\n                                                    title: \"Edit question\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleDelete(question.id),\n                                                    title: \"Delete question\",\n                                                    className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: question.isChemical && (question.chemicalImages || question.imageData) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__.ChemicalImageDisplay, {\n                                        text: question.text,\n                                        images: question.imageData || question.chemicalImages,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                        text: question.text,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300,\n                                        questionImages: // Convert imageUrls array to object mapping for compatibility\n                                        question.imageUrls && Array.isArray(question.imageUrls) ? {\n                                            'image-1': question.imageUrls[0]\n                                        } : question.imageData || question.chemicalImages\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: question.options.map((option, index)=>question.isChemical && (question.chemicalImages || question.imageData) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__.ChemicalOptionDisplay, {\n                                            option: option,\n                                            images: question.imageData || question.chemicalImages,\n                                            isCorrect: option.text === question.correctAnswer\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start p-3 rounded-md border \".concat(option.text === question.correctAnswer ? \"border-green-500 bg-green-50\" : \"border-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: option.text,\n                                                                maxImageWidth: 200,\n                                                                maxImageHeight: 150,\n                                                                questionImages: question.imageData || question.chemicalImages || question.imageUrls\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: option.isImageOption ? \"\" : \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__.Base64Image, {\n                                                                src: option.imageUrl,\n                                                                alt: \"Option \".concat(option.label),\n                                                                maxWidth: 200,\n                                                                maxHeight: 150,\n                                                                className: \"border-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        option.isImageOption && !option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500 italic\",\n                                                            children: \"Image option\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                question.solution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleSolution(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-blue-600 hover:text-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Solution\",\n                                                expandedSolutions.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedSolutions.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-3 bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Methodology:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: question.solution.methodology\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 23\n                                                }, this),\n                                                question.solution.key_concepts && question.solution.key_concepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Key Concepts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: question.solution.key_concepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: concept\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 25\n                                                }, this),\n                                                question.solution.steps && question.solution.steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-2\",\n                                                            children: \"Solution Steps:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: question.solution.steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                        text: step,\n                                                                        maxImageWidth: 300,\n                                                                        maxImageHeight: 200,\n                                                                        questionImages: question.imageData || question.chemicalImages || question.imageUrls\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Final Explanation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: question.solution.final_explanation,\n                                                                maxImageWidth: 300,\n                                                                maxImageHeight: 200,\n                                                                questionImages: question.imageData || question.chemicalImages || question.imageUrls\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 17\n                                }, this),\n                                question.hints && question.hints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleHints(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-amber-600 hover:text-amber-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Hints (\",\n                                                question.hints.length,\n                                                \")\",\n                                                expandedHints.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedHints.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-2 bg-amber-50 p-4 rounded-lg\",\n                                            children: question.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                        text: hint,\n                                                        maxImageWidth: 300,\n                                                        maxImageHeight: 200\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogTitle, {\n                                    children: \"Are you sure you want to delete this question?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogDescription, {\n                                    children: \"This action cannot be undone. This will permanently delete the question and all associated data.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogAction, {\n                                    onClick: confirmDelete,\n                                    className: \"bg-red-600 hover:bg-red-700\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"Deleting...\" : \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionList, \"HyLOnO+LyIVOiMu1okpM+izBbNU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter\n    ];\n});\n_c = QuestionList;\nvar _c;\n$RefreshReg$(_c, \"QuestionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\n"));

/***/ })

});