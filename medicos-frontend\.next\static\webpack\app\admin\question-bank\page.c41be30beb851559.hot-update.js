"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/beaker.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/beaker.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Beaker)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.485.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4.5 3h15\",\n            key: \"c7n0jr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3\",\n            key: \"m1uhx7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 14h12\",\n            key: \"4cwo0f\"\n        }\n    ]\n];\nconst Beaker = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"beaker\", __iconNode);\n //# sourceMappingURL=beaker.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/beaker.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-list.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/text-with-images */ \"(app-pages-browser)/./src/components/ui/text-with-images.tsx\");\n/* harmony import */ var _components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/chemical-image-display */ \"(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction QuestionList(param) {\n    let { questions, onDifficultyChange, onReviewStatusChange, onQuestionDeleted } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionToDelete, setQuestionToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedSolutions, setExpandedSolutions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [expandedHints, setExpandedHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Toggle solution expansion\n    const toggleSolution = (questionId)=>{\n        setExpandedSolutions((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Toggle hints expansion\n    const toggleHints = (questionId)=>{\n        setExpandedHints((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Handle delete button click\n    const handleDelete = (questionId)=>{\n        setQuestionToDelete(questionId);\n        setIsDeleteDialogOpen(true);\n    };\n    // Handle edit button click\n    const handleEdit = (questionId)=>{\n        router.push(\"/admin/edit-question/\".concat(questionId));\n    };\n    // Confirm delete action\n    const confirmDelete = async ()=>{\n        if (!questionToDelete) return;\n        try {\n            setIsDeleting(true);\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_10__.deleteQuestion)(questionToDelete);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                if (onQuestionDeleted) {\n                    onQuestionDeleted() // Refresh the list\n                    ;\n                }\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error deleting question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n            setIsDeleteDialogOpen(false);\n            setQuestionToDelete(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap items-center justify-between gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.subject\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.topic\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                question.reviewStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    className: question.reviewStatus === \"approved\" ? \"bg-green-100 text-green-800 hover:bg-green-100\" : question.reviewStatus === \"rejected\" ? \"bg-red-100 text-red-800 hover:bg-red-100\" : \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                    children: question.reviewStatus.charAt(0).toUpperCase() + question.reviewStatus.slice(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.difficulty.toLowerCase(),\n                                                    onValueChange: (value)=>onDifficultyChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"easy\",\n                                                                    children: \"Easy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"hard\",\n                                                                    children: \"Hard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.reviewStatus.toLowerCase(),\n                                                    onValueChange: (value)=>onReviewStatusChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Review Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"pending\",\n                                                                    children: \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"approved\",\n                                                                    children: \"Approved\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"rejected\",\n                                                                    children: \"Rejected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleEdit(question.id),\n                                                    title: \"Edit question\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleDelete(question.id),\n                                                    title: \"Delete question\",\n                                                    className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: question.isChemical && question.chemicalImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__.ChemicalImageDisplay, {\n                                        text: question.text,\n                                        images: question.chemicalImages,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                        text: question.text,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start p-3 rounded-md border \".concat(option.text === question.correctAnswer ? \"border-green-500 bg-green-50\" : \"border-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: option.text,\n                                                                maxImageWidth: 200,\n                                                                maxImageHeight: 150\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: option.isImageOption ? \"\" : \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__.Base64Image, {\n                                                                src: option.imageUrl,\n                                                                alt: \"Option \".concat(option.label),\n                                                                maxWidth: 200,\n                                                                maxHeight: 150,\n                                                                className: \"border-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        option.isImageOption && !option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500 italic\",\n                                                            children: \"Image option\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                question.solution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleSolution(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-blue-600 hover:text-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Solution\",\n                                                expandedSolutions.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedSolutions.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-3 bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Methodology:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: question.solution.methodology\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 23\n                                                }, this),\n                                                question.solution.key_concepts && question.solution.key_concepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Key Concepts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: question.solution.key_concepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: concept\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 25\n                                                }, this),\n                                                question.solution.steps && question.solution.steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-2\",\n                                                            children: \"Solution Steps:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: question.solution.steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                        text: step,\n                                                                        maxImageWidth: 300,\n                                                                        maxImageHeight: 200\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Final Explanation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: question.solution.final_explanation,\n                                                                maxImageWidth: 300,\n                                                                maxImageHeight: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, this),\n                                question.hints && question.hints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleHints(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-amber-600 hover:text-amber-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Hints (\",\n                                                question.hints.length,\n                                                \")\",\n                                                expandedHints.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedHints.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-2 bg-amber-50 p-4 rounded-lg\",\n                                            children: question.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                        text: hint,\n                                                        maxImageWidth: 300,\n                                                        maxImageHeight: 200\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogTitle, {\n                                    children: \"Are you sure you want to delete this question?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogDescription, {\n                                    children: \"This action cannot be undone. This will permanently delete the question and all associated data.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogAction, {\n                                    onClick: confirmDelete,\n                                    className: \"bg-red-600 hover:bg-red-700\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"Deleting...\" : \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionList, \"HyLOnO+LyIVOiMu1okpM+izBbNU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter\n    ];\n});\n_c = QuestionList;\nvar _c;\n$RefreshReg$(_c, \"QuestionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/chemical-image-display.tsx":
/*!******************************************************!*\
  !*** ./src/components/ui/chemical-image-display.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChemicalImageDisplay: () => (/* binding */ ChemicalImageDisplay),\n/* harmony export */   ChemicalOptionDisplay: () => (/* binding */ ChemicalOptionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _base64_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _math_text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./math-text */ \"(app-pages-browser)/./src/components/ui/math-text.tsx\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/beaker.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChemicalImageDisplay(param) {\n    let { text, images = {}, className, imageClassName, maxImageWidth = 400, maxImageHeight = 300, showImageToggle = true } = param;\n    _s();\n    const [showImages, setShowImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedImages, setExpandedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Process text to handle chemical image placeholders\n    const processChemicalText = (inputText)=>{\n        if (!inputText) return {\n            cleanText: '',\n            imageRefs: []\n        };\n        let cleanText = inputText;\n        const imageRefs = [];\n        // Find chemical image placeholders like [CHEMICAL_IMAGE_1_question]\n        const chemicalImagePattern = /\\[CHEMICAL_IMAGE_(\\d+)_([^\\]]+)\\]/g;\n        let match;\n        while((match = chemicalImagePattern.exec(inputText)) !== null){\n            const [fullMatch, questionNum, context] = match;\n            const imageKey = \"chemical_img_\".concat(questionNum, \"_\").concat(context);\n            if (images[imageKey]) {\n                imageRefs.push(imageKey);\n                // Replace placeholder with a marker for rendering\n                cleanText = cleanText.replace(fullMatch, \"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"));\n            } else {\n                // Remove placeholder if no image found\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        }\n        // Also handle markdown image references that weren't converted\n        const markdownPattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n        cleanText = cleanText.replace(markdownPattern, (match, alt, src)=>{\n            // Try to find matching image\n            const matchingKey = Object.keys(images).find((key)=>key.includes(src) || src.includes(key.replace('chemical_img_', '')));\n            if (matchingKey) {\n                imageRefs.push(matchingKey);\n                return \"[IMAGE_PLACEHOLDER_\".concat(matchingKey, \"]\");\n            }\n            return \"[Missing Image: \".concat(src, \"]\");\n        });\n        return {\n            cleanText,\n            imageRefs: [\n                ...new Set(imageRefs)\n            ]\n        };\n    };\n    const { cleanText, imageRefs } = processChemicalText(text);\n    const hasImages = imageRefs.length > 0;\n    const toggleImageExpansion = (imageKey)=>{\n        setExpandedImages((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(imageKey)) {\n                newSet.delete(imageKey);\n            } else {\n                newSet.add(imageKey);\n            }\n            return newSet;\n        });\n    };\n    const renderTextWithImagePlaceholders = (text)=>{\n        if (!text) return null;\n        const parts = text.split(/(\\[IMAGE_PLACEHOLDER_[^\\]]+\\])/);\n        return parts.map((part, index)=>{\n            const placeholderMatch = part.match(/\\[IMAGE_PLACEHOLDER_([^\\]]+)\\]/);\n            if (placeholderMatch) {\n                const imageKey = placeholderMatch[1];\n                const imageData = images[imageKey];\n                if (imageData && showImages) {\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Chemical Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 p-2 bg-gray-100 border border-gray-200 rounded text-sm text-gray-600\",\n                    children: [\n                        \"[Chemical Structure - \",\n                        imageKey,\n                        \"]\"\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this);\n            }\n            return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_math_text__WEBPACK_IMPORTED_MODULE_4__.MathText, {\n                    text: part\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this) : null;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-3\", className),\n        children: [\n            hasImages && showImageToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            imageRefs.length,\n                            \" Chemical Structure\",\n                            imageRefs.length !== 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>setShowImages(!showImages),\n                        className: \"h-7 px-3 text-xs\",\n                        children: showImages ? 'Hide Images' : 'Show Images'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-base leading-relaxed\",\n                children: renderTextWithImagePlaceholders(cleanText)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            showImages && imageRefs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: imageRefs.map((imageKey)=>{\n                    // Skip if this image was already rendered inline\n                    if (cleanText.includes(\"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"))) {\n                        return null;\n                    }\n                    const imageData = images[imageKey];\n                    if (!imageData) return null;\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chemical Structure - \",\n                                            imageKey.replace('chemical_img_', '').replace(/_/g, ' ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, imageKey, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(ChemicalImageDisplay, \"WkjgmkVJW42NseduZzN6jEUfq8M=\");\n_c = ChemicalImageDisplay;\nfunction ChemicalOptionDisplay(param) {\n    let { option, images = {}, isCorrect = false, className } = param;\n    // If option has imageUrl as string, it might be a key to the images object\n    const imageData = option.imageUrl && typeof option.imageUrl === 'string' ? images[option.imageUrl] || option.imageUrl : option.imageUrl;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-start p-3 rounded-md border transition-colors\", isCorrect ? \"border-green-500 bg-green-50\" : \"border-gray-200 hover:border-blue-300\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: option.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChemicalImageDisplay, {\n                            text: option.text,\n                            images: images,\n                            maxImageWidth: 200,\n                            maxImageHeight: 150,\n                            showImageToggle: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: option.isImageOption ? \"\" : \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-50 border border-blue-200 rounded-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2 text-xs text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Chemical Structure Option\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                    src: imageData,\n                                    alt: \"Option \".concat(option.label, \" - Chemical Structure\"),\n                                    maxWidth: 200,\n                                    maxHeight: 150,\n                                    className: \"border border-blue-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    option.isImageOption && !imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 italic text-sm\",\n                        children: \"Chemical structure option (image not available)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChemicalOptionDisplay;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChemicalImageDisplay\");\n$RefreshReg$(_c1, \"ChemicalOptionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2NoZW1pY2FsLWltYWdlLWRpc3BsYXkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ1A7QUFDWTtBQUNOO0FBQ3VCO0FBQzVCO0FBWTNCLFNBQVNTLHFCQUFxQixLQVFUO1FBUlMsRUFDbkNDLElBQUksRUFDSkMsU0FBUyxDQUFDLENBQUMsRUFDWEMsU0FBUyxFQUNUQyxjQUFjLEVBQ2RDLGdCQUFnQixHQUFHLEVBQ25CQyxpQkFBaUIsR0FBRyxFQUNwQkMsa0JBQWtCLElBQUksRUFDSSxHQVJTOztJQVNuQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ2tCLGdCQUFnQkMsa0JBQWtCLEdBQUduQiwrQ0FBUUEsQ0FBYyxJQUFJb0I7SUFFdEUscURBQXFEO0lBQ3JELE1BQU1DLHNCQUFzQixDQUFDQztRQUMzQixJQUFJLENBQUNBLFdBQVcsT0FBTztZQUFFQyxXQUFXO1lBQUlDLFdBQVcsRUFBRTtRQUFDO1FBRXRELElBQUlELFlBQVlEO1FBQ2hCLE1BQU1FLFlBQXNCLEVBQUU7UUFFOUIsb0VBQW9FO1FBQ3BFLE1BQU1DLHVCQUF1QjtRQUM3QixJQUFJQztRQUVKLE1BQU8sQ0FBQ0EsUUFBUUQscUJBQXFCRSxJQUFJLENBQUNMLFVBQVMsTUFBTyxLQUFNO1lBQzlELE1BQU0sQ0FBQ00sV0FBV0MsYUFBYUMsUUFBUSxHQUFHSjtZQUMxQyxNQUFNSyxXQUFXLGdCQUErQkQsT0FBZkQsYUFBWSxLQUFXLE9BQVJDO1lBRWhELElBQUlwQixNQUFNLENBQUNxQixTQUFTLEVBQUU7Z0JBQ3BCUCxVQUFVUSxJQUFJLENBQUNEO2dCQUNmLGtEQUFrRDtnQkFDbERSLFlBQVlBLFVBQVVVLE9BQU8sQ0FBQ0wsV0FBVyxzQkFBK0IsT0FBVEcsVUFBUztZQUMxRSxPQUFPO2dCQUNMLHVDQUF1QztnQkFDdkNSLFlBQVlBLFVBQVVVLE9BQU8sQ0FBQ0wsV0FBVztZQUMzQztRQUNGO1FBRUEsK0RBQStEO1FBQy9ELE1BQU1NLGtCQUFrQjtRQUN4QlgsWUFBWUEsVUFBVVUsT0FBTyxDQUFDQyxpQkFBaUIsQ0FBQ1IsT0FBT1MsS0FBS0M7WUFDMUQsNkJBQTZCO1lBQzdCLE1BQU1DLGNBQWNDLE9BQU9DLElBQUksQ0FBQzdCLFFBQVE4QixJQUFJLENBQUNDLENBQUFBLE1BQzNDQSxJQUFJQyxRQUFRLENBQUNOLFFBQVFBLElBQUlNLFFBQVEsQ0FBQ0QsSUFBSVIsT0FBTyxDQUFDLGlCQUFpQjtZQUdqRSxJQUFJSSxhQUFhO2dCQUNmYixVQUFVUSxJQUFJLENBQUNLO2dCQUNmLE9BQU8sc0JBQWtDLE9BQVpBLGFBQVk7WUFDM0M7WUFFQSxPQUFPLG1CQUF1QixPQUFKRCxLQUFJO1FBQ2hDO1FBRUEsT0FBTztZQUFFYjtZQUFXQyxXQUFXO21CQUFJLElBQUlKLElBQUlJO2FBQVc7UUFBQztJQUN6RDtJQUVBLE1BQU0sRUFBRUQsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBR0gsb0JBQW9CWjtJQUNyRCxNQUFNa0MsWUFBWW5CLFVBQVVvQixNQUFNLEdBQUc7SUFFckMsTUFBTUMsdUJBQXVCLENBQUNkO1FBQzVCWixrQkFBa0IyQixDQUFBQTtZQUNoQixNQUFNQyxTQUFTLElBQUkzQixJQUFJMEI7WUFDdkIsSUFBSUMsT0FBT0MsR0FBRyxDQUFDakIsV0FBVztnQkFDeEJnQixPQUFPRSxNQUFNLENBQUNsQjtZQUNoQixPQUFPO2dCQUNMZ0IsT0FBT0csR0FBRyxDQUFDbkI7WUFDYjtZQUNBLE9BQU9nQjtRQUNUO0lBQ0Y7SUFFQSxNQUFNSSxrQ0FBa0MsQ0FBQzFDO1FBQ3ZDLElBQUksQ0FBQ0EsTUFBTSxPQUFPO1FBRWxCLE1BQU0yQyxRQUFRM0MsS0FBSzRDLEtBQUssQ0FBQztRQUV6QixPQUFPRCxNQUFNRSxHQUFHLENBQUMsQ0FBQ0MsTUFBTUM7WUFDdEIsTUFBTUMsbUJBQW1CRixLQUFLN0IsS0FBSyxDQUFDO1lBRXBDLElBQUkrQixrQkFBa0I7Z0JBQ3BCLE1BQU0xQixXQUFXMEIsZ0JBQWdCLENBQUMsRUFBRTtnQkFDcEMsTUFBTUMsWUFBWWhELE1BQU0sQ0FBQ3FCLFNBQVM7Z0JBRWxDLElBQUkyQixhQUFhMUMsWUFBWTtvQkFDM0IsTUFBTTJDLGFBQWF6QyxlQUFlOEIsR0FBRyxDQUFDakI7b0JBRXRDLHFCQUNFLDhEQUFDNkI7d0JBQWdCakQsV0FBVTs7MENBQ3pCLDhEQUFDaUQ7Z0NBQUlqRCxXQUFVOztrREFDYiw4REFBQ2lEO3dDQUFJakQsV0FBVTs7MERBQ2IsOERBQUNMLHdHQUFNQTtnREFBQ0ssV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7O2tEQUdoQyw4REFBQ0osMkNBQU1BO3dDQUNMc0QsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEMsU0FBUyxJQUFNbEIscUJBQXFCZDt3Q0FDcENwQixXQUFVO2tEQUVUZ0QsMkJBQ0M7OzhEQUNFLDhEQUFDdEQsd0dBQVNBO29EQUFDTSxXQUFVOzs7Ozs7Z0RBQWlCOzt5RUFJeEM7OzhEQUNFLDhEQUFDUCx3R0FBV0E7b0RBQUNPLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7Ozs7OzBDQU9oRCw4REFBQ1Qsc0RBQVdBO2dDQUNWa0MsS0FBS3NCO2dDQUNMdkIsS0FBSyxzQkFBK0IsT0FBVEo7Z0NBQzNCcEIsV0FBV1YsOENBQUVBLENBQ1gsaUVBQ0FXLGdCQUNBK0MsYUFBYSxvQkFBb0I7Z0NBRW5DSyxVQUFVTCxhQUFhOUMsZ0JBQWdCLE1BQU1BO2dDQUM3Q29ELFdBQVdOLGFBQWE3QyxpQkFBaUIsTUFBTUE7Z0NBQy9DaUQsU0FBUyxJQUFNbEIscUJBQXFCZDs7Ozs7Ozt1QkFwQzlCeUI7Ozs7O2dCQXdDZDtnQkFFQSxxQkFDRSw4REFBQ0k7b0JBQWdCakQsV0FBVTs7d0JBQTRFO3dCQUM5RW9CO3dCQUFTOzttQkFEeEJ5Qjs7Ozs7WUFJZDtZQUVBLE9BQU9ELHFCQUNMLDhEQUFDVzswQkFDQyw0RUFBQy9ELGdEQUFRQTtvQkFBQ00sTUFBTThDOzs7Ozs7ZUFEUEM7Ozs7dUJBR1Q7UUFDTjtJQUNGO0lBRUEscUJBQ0UsOERBQUNJO1FBQUlqRCxXQUFXViw4Q0FBRUEsQ0FBQyxhQUFhVTs7WUFFN0JnQyxhQUFhNUIsaUNBQ1osOERBQUM2QztnQkFBSWpELFdBQVU7O2tDQUNiLDhEQUFDaUQ7d0JBQUlqRCxXQUFVOzswQ0FDYiw4REFBQ0wsd0dBQU1BO2dDQUFDSyxXQUFVOzs7Ozs7NEJBQ2pCYSxVQUFVb0IsTUFBTTs0QkFBQzs0QkFBb0JwQixVQUFVb0IsTUFBTSxLQUFLLElBQUksTUFBTTs7Ozs7OztrQ0FFdkUsOERBQUNyQywyQ0FBTUE7d0JBQ0xzRCxTQUFRO3dCQUNSQyxNQUFLO3dCQUNMQyxTQUFTLElBQU05QyxjQUFjLENBQUNEO3dCQUM5QkwsV0FBVTtrQ0FFVEssYUFBYSxnQkFBZ0I7Ozs7Ozs7Ozs7OzswQkFNcEMsOERBQUM0QztnQkFBSWpELFdBQVU7MEJBQ1p3QyxnQ0FBZ0M1Qjs7Ozs7O1lBSWxDUCxjQUFjUSxVQUFVb0IsTUFBTSxHQUFHLG1CQUNoQyw4REFBQ2dCO2dCQUFJakQsV0FBVTswQkFDWmEsVUFBVThCLEdBQUcsQ0FBQyxDQUFDdkI7b0JBQ2QsaURBQWlEO29CQUNqRCxJQUFJUixVQUFVbUIsUUFBUSxDQUFDLHNCQUErQixPQUFUWCxVQUFTLE9BQUs7d0JBQ3pELE9BQU87b0JBQ1Q7b0JBRUEsTUFBTTJCLFlBQVloRCxNQUFNLENBQUNxQixTQUFTO29CQUNsQyxJQUFJLENBQUMyQixXQUFXLE9BQU87b0JBRXZCLE1BQU1DLGFBQWF6QyxlQUFlOEIsR0FBRyxDQUFDakI7b0JBRXRDLHFCQUNFLDhEQUFDNkI7d0JBQW1CakQsV0FBVTs7MENBQzVCLDhEQUFDaUQ7Z0NBQUlqRCxXQUFVOztrREFDYiw4REFBQ2lEO3dDQUFJakQsV0FBVTs7MERBQ2IsOERBQUNMLHdHQUFNQTtnREFBQ0ssV0FBVTs7Ozs7OzRDQUFZOzRDQUNSb0IsU0FBU0UsT0FBTyxDQUFDLGlCQUFpQixJQUFJQSxPQUFPLENBQUMsTUFBTTs7Ozs7OztrREFFNUUsOERBQUMxQiwyQ0FBTUE7d0NBQ0xzRCxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTLElBQU1sQixxQkFBcUJkO3dDQUNwQ3BCLFdBQVU7a0RBRVRnRCwyQkFDQzs7OERBQ0UsOERBQUN0RCx3R0FBU0E7b0RBQUNNLFdBQVU7Ozs7OztnREFBaUI7O3lFQUl4Qzs7OERBQ0UsOERBQUNQLHdHQUFXQTtvREFBQ08sV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7Ozs7MENBT2hELDhEQUFDVCxzREFBV0E7Z0NBQ1ZrQyxLQUFLc0I7Z0NBQ0x2QixLQUFLLHNCQUErQixPQUFUSjtnQ0FDM0JwQixXQUFXViw4Q0FBRUEsQ0FDWCxpRUFDQVcsZ0JBQ0ErQyxhQUFhLG9CQUFvQjtnQ0FFbkNLLFVBQVVMLGFBQWE5QyxnQkFBZ0IsTUFBTUE7Z0NBQzdDb0QsV0FBV04sYUFBYTdDLGlCQUFpQixNQUFNQTtnQ0FDL0NpRCxTQUFTLElBQU1sQixxQkFBcUJkOzs7Ozs7O3VCQXBDOUJBOzs7OztnQkF3Q2Q7Ozs7Ozs7Ozs7OztBQUtWO0dBck9nQnZCO0tBQUFBO0FBb1BULFNBQVMyRCxzQkFBc0IsS0FLVDtRQUxTLEVBQ3BDQyxNQUFNLEVBQ04xRCxTQUFTLENBQUMsQ0FBQyxFQUNYMkQsWUFBWSxLQUFLLEVBQ2pCMUQsU0FBUyxFQUNrQixHQUxTO0lBTXBDLDJFQUEyRTtJQUMzRSxNQUFNK0MsWUFBWVUsT0FBT0UsUUFBUSxJQUFJLE9BQU9GLE9BQU9FLFFBQVEsS0FBSyxXQUMzRDVELE1BQU0sQ0FBQzBELE9BQU9FLFFBQVEsQ0FBQyxJQUFJRixPQUFPRSxRQUFRLEdBQzNDRixPQUFPRSxRQUFRO0lBRW5CLHFCQUNFLDhEQUFDVjtRQUNDakQsV0FBV1YsOENBQUVBLENBQ1gsNERBQ0FvRSxZQUNJLGlDQUNBLHlDQUNKMUQ7OzBCQUdGLDhEQUFDaUQ7Z0JBQUlqRCxXQUFVOzBCQUNiLDRFQUFDdUQ7b0JBQUt2RCxXQUFVOzhCQUF1QnlELE9BQU9HLEtBQUs7Ozs7Ozs7Ozs7OzBCQUdyRCw4REFBQ1g7Z0JBQUlqRCxXQUFVOztvQkFFWnlELE9BQU8zRCxJQUFJLElBQUksQ0FBQzJELE9BQU9JLGFBQWEsa0JBQ25DLDhEQUFDWjt3QkFBSWpELFdBQVU7a0NBQ2IsNEVBQUNIOzRCQUNDQyxNQUFNMkQsT0FBTzNELElBQUk7NEJBQ2pCQyxRQUFRQTs0QkFDUkcsZUFBZTs0QkFDZkMsZ0JBQWdCOzRCQUNoQkMsaUJBQWlCOzs7Ozs7Ozs7OztvQkFNdEIyQywyQkFDQyw4REFBQ0U7d0JBQUlqRCxXQUFXeUQsT0FBT0ksYUFBYSxHQUFHLEtBQUs7a0NBQzFDLDRFQUFDWjs0QkFBSWpELFdBQVU7OzhDQUNiLDhEQUFDaUQ7b0NBQUlqRCxXQUFVOztzREFDYiw4REFBQ0wsd0dBQU1BOzRDQUFDSyxXQUFVOzs7Ozs7d0NBQVk7Ozs7Ozs7OENBR2hDLDhEQUFDVCxzREFBV0E7b0NBQ1ZrQyxLQUFLc0I7b0NBQ0x2QixLQUFLLFVBQXVCLE9BQWJpQyxPQUFPRyxLQUFLLEVBQUM7b0NBQzVCUCxVQUFVO29DQUNWQyxXQUFXO29DQUNYdEQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT2pCeUQsT0FBT0ksYUFBYSxJQUFJLENBQUNkLDJCQUN4Qiw4REFBQ0U7d0JBQUlqRCxXQUFVO2tDQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3hEO01BbkVnQndEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcbWVkaWNvc1xcbWVkaWNvcy1mcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcY2hlbWljYWwtaW1hZ2UtZGlzcGxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBCYXNlNjRJbWFnZSB9IGZyb20gJy4vYmFzZTY0LWltYWdlJztcbmltcG9ydCB7IE1hdGhUZXh0IH0gZnJvbSAnLi9tYXRoLXRleHQnO1xuaW1wb3J0IHsgQ2hldnJvbkRvd24sIENoZXZyb25VcCwgQmVha2VyIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vYnV0dG9uJztcblxuaW50ZXJmYWNlIENoZW1pY2FsSW1hZ2VEaXNwbGF5UHJvcHMge1xuICB0ZXh0OiBzdHJpbmc7XG4gIGltYWdlcz86IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaW1hZ2VDbGFzc05hbWU/OiBzdHJpbmc7XG4gIG1heEltYWdlV2lkdGg/OiBudW1iZXI7XG4gIG1heEltYWdlSGVpZ2h0PzogbnVtYmVyO1xuICBzaG93SW1hZ2VUb2dnbGU/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQ2hlbWljYWxJbWFnZURpc3BsYXkoe1xuICB0ZXh0LFxuICBpbWFnZXMgPSB7fSxcbiAgY2xhc3NOYW1lLFxuICBpbWFnZUNsYXNzTmFtZSxcbiAgbWF4SW1hZ2VXaWR0aCA9IDQwMCxcbiAgbWF4SW1hZ2VIZWlnaHQgPSAzMDAsXG4gIHNob3dJbWFnZVRvZ2dsZSA9IHRydWVcbn06IENoZW1pY2FsSW1hZ2VEaXNwbGF5UHJvcHMpIHtcbiAgY29uc3QgW3Nob3dJbWFnZXMsIHNldFNob3dJbWFnZXNdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtleHBhbmRlZEltYWdlcywgc2V0RXhwYW5kZWRJbWFnZXNdID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSk7XG5cbiAgLy8gUHJvY2VzcyB0ZXh0IHRvIGhhbmRsZSBjaGVtaWNhbCBpbWFnZSBwbGFjZWhvbGRlcnNcbiAgY29uc3QgcHJvY2Vzc0NoZW1pY2FsVGV4dCA9IChpbnB1dFRleHQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghaW5wdXRUZXh0KSByZXR1cm4geyBjbGVhblRleHQ6ICcnLCBpbWFnZVJlZnM6IFtdIH07XG5cbiAgICBsZXQgY2xlYW5UZXh0ID0gaW5wdXRUZXh0O1xuICAgIGNvbnN0IGltYWdlUmVmczogc3RyaW5nW10gPSBbXTtcblxuICAgIC8vIEZpbmQgY2hlbWljYWwgaW1hZ2UgcGxhY2Vob2xkZXJzIGxpa2UgW0NIRU1JQ0FMX0lNQUdFXzFfcXVlc3Rpb25dXG4gICAgY29uc3QgY2hlbWljYWxJbWFnZVBhdHRlcm4gPSAvXFxbQ0hFTUlDQUxfSU1BR0VfKFxcZCspXyhbXlxcXV0rKVxcXS9nO1xuICAgIGxldCBtYXRjaDtcblxuICAgIHdoaWxlICgobWF0Y2ggPSBjaGVtaWNhbEltYWdlUGF0dGVybi5leGVjKGlucHV0VGV4dCkpICE9PSBudWxsKSB7XG4gICAgICBjb25zdCBbZnVsbE1hdGNoLCBxdWVzdGlvbk51bSwgY29udGV4dF0gPSBtYXRjaDtcbiAgICAgIGNvbnN0IGltYWdlS2V5ID0gYGNoZW1pY2FsX2ltZ18ke3F1ZXN0aW9uTnVtfV8ke2NvbnRleHR9YDtcbiAgICAgIFxuICAgICAgaWYgKGltYWdlc1tpbWFnZUtleV0pIHtcbiAgICAgICAgaW1hZ2VSZWZzLnB1c2goaW1hZ2VLZXkpO1xuICAgICAgICAvLyBSZXBsYWNlIHBsYWNlaG9sZGVyIHdpdGggYSBtYXJrZXIgZm9yIHJlbmRlcmluZ1xuICAgICAgICBjbGVhblRleHQgPSBjbGVhblRleHQucmVwbGFjZShmdWxsTWF0Y2gsIGBbSU1BR0VfUExBQ0VIT0xERVJfJHtpbWFnZUtleX1dYCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBSZW1vdmUgcGxhY2Vob2xkZXIgaWYgbm8gaW1hZ2UgZm91bmRcbiAgICAgICAgY2xlYW5UZXh0ID0gY2xlYW5UZXh0LnJlcGxhY2UoZnVsbE1hdGNoLCAnJyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWxzbyBoYW5kbGUgbWFya2Rvd24gaW1hZ2UgcmVmZXJlbmNlcyB0aGF0IHdlcmVuJ3QgY29udmVydGVkXG4gICAgY29uc3QgbWFya2Rvd25QYXR0ZXJuID0gLyFcXFsoW15cXF1dKilcXF1cXCgoW14pXSspXFwpL2c7XG4gICAgY2xlYW5UZXh0ID0gY2xlYW5UZXh0LnJlcGxhY2UobWFya2Rvd25QYXR0ZXJuLCAobWF0Y2gsIGFsdCwgc3JjKSA9PiB7XG4gICAgICAvLyBUcnkgdG8gZmluZCBtYXRjaGluZyBpbWFnZVxuICAgICAgY29uc3QgbWF0Y2hpbmdLZXkgPSBPYmplY3Qua2V5cyhpbWFnZXMpLmZpbmQoa2V5ID0+IFxuICAgICAgICBrZXkuaW5jbHVkZXMoc3JjKSB8fCBzcmMuaW5jbHVkZXMoa2V5LnJlcGxhY2UoJ2NoZW1pY2FsX2ltZ18nLCAnJykpXG4gICAgICApO1xuICAgICAgXG4gICAgICBpZiAobWF0Y2hpbmdLZXkpIHtcbiAgICAgICAgaW1hZ2VSZWZzLnB1c2gobWF0Y2hpbmdLZXkpO1xuICAgICAgICByZXR1cm4gYFtJTUFHRV9QTEFDRUhPTERFUl8ke21hdGNoaW5nS2V5fV1gO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gYFtNaXNzaW5nIEltYWdlOiAke3NyY31dYDtcbiAgICB9KTtcblxuICAgIHJldHVybiB7IGNsZWFuVGV4dCwgaW1hZ2VSZWZzOiBbLi4ubmV3IFNldChpbWFnZVJlZnMpXSB9O1xuICB9O1xuXG4gIGNvbnN0IHsgY2xlYW5UZXh0LCBpbWFnZVJlZnMgfSA9IHByb2Nlc3NDaGVtaWNhbFRleHQodGV4dCk7XG4gIGNvbnN0IGhhc0ltYWdlcyA9IGltYWdlUmVmcy5sZW5ndGggPiAwO1xuXG4gIGNvbnN0IHRvZ2dsZUltYWdlRXhwYW5zaW9uID0gKGltYWdlS2V5OiBzdHJpbmcpID0+IHtcbiAgICBzZXRFeHBhbmRlZEltYWdlcyhwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICBpZiAobmV3U2V0LmhhcyhpbWFnZUtleSkpIHtcbiAgICAgICAgbmV3U2V0LmRlbGV0ZShpbWFnZUtleSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXdTZXQuYWRkKGltYWdlS2V5KTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgcmVuZGVyVGV4dFdpdGhJbWFnZVBsYWNlaG9sZGVycyA9ICh0ZXh0OiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXRleHQpIHJldHVybiBudWxsO1xuXG4gICAgY29uc3QgcGFydHMgPSB0ZXh0LnNwbGl0KC8oXFxbSU1BR0VfUExBQ0VIT0xERVJfW15cXF1dK1xcXSkvKTtcbiAgICBcbiAgICByZXR1cm4gcGFydHMubWFwKChwYXJ0LCBpbmRleCkgPT4ge1xuICAgICAgY29uc3QgcGxhY2Vob2xkZXJNYXRjaCA9IHBhcnQubWF0Y2goL1xcW0lNQUdFX1BMQUNFSE9MREVSXyhbXlxcXV0rKVxcXS8pO1xuICAgICAgXG4gICAgICBpZiAocGxhY2Vob2xkZXJNYXRjaCkge1xuICAgICAgICBjb25zdCBpbWFnZUtleSA9IHBsYWNlaG9sZGVyTWF0Y2hbMV07XG4gICAgICAgIGNvbnN0IGltYWdlRGF0YSA9IGltYWdlc1tpbWFnZUtleV07XG4gICAgICAgIFxuICAgICAgICBpZiAoaW1hZ2VEYXRhICYmIHNob3dJbWFnZXMpIHtcbiAgICAgICAgICBjb25zdCBpc0V4cGFuZGVkID0gZXhwYW5kZWRJbWFnZXMuaGFzKGltYWdlS2V5KTtcbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJteS0zIHAtMyBiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxCZWFrZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICBDaGVtaWNhbCBTdHJ1Y3R1cmVcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUltYWdlRXhwYW5zaW9uKGltYWdlS2V5KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiBweC0yIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25VcCBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIENvbGxhcHNlXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBFeHBhbmRcbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxCYXNlNjRJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz17aW1hZ2VEYXRhfVxuICAgICAgICAgICAgICAgIGFsdD17YENoZW1pY2FsIHN0cnVjdHVyZSAke2ltYWdlS2V5fWB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgIFwiYm9yZGVyIGJvcmRlci1ibHVlLTMwMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgaW1hZ2VDbGFzc05hbWUsXG4gICAgICAgICAgICAgICAgICBpc0V4cGFuZGVkID8gXCJjdXJzb3Item9vbS1vdXRcIiA6IFwiY3Vyc29yLXpvb20taW5cIlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgbWF4V2lkdGg9e2lzRXhwYW5kZWQgPyBtYXhJbWFnZVdpZHRoICogMS41IDogbWF4SW1hZ2VXaWR0aH1cbiAgICAgICAgICAgICAgICBtYXhIZWlnaHQ9e2lzRXhwYW5kZWQgPyBtYXhJbWFnZUhlaWdodCAqIDEuNSA6IG1heEltYWdlSGVpZ2h0fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUltYWdlRXhwYW5zaW9uKGltYWdlS2V5KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJteS0yIHAtMiBiZy1ncmF5LTEwMCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICBbQ2hlbWljYWwgU3RydWN0dXJlIC0ge2ltYWdlS2V5fV1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuIHBhcnQgPyAoXG4gICAgICAgIDxzcGFuIGtleT17aW5kZXh9PlxuICAgICAgICAgIDxNYXRoVGV4dCB0ZXh0PXtwYXJ0fSAvPlxuICAgICAgICA8L3NwYW4+XG4gICAgICApIDogbnVsbDtcbiAgICB9KTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcInNwYWNlLXktM1wiLCBjbGFzc05hbWUpfT5cbiAgICAgIHsvKiBJbWFnZSB0b2dnbGUgYnV0dG9uICovfVxuICAgICAge2hhc0ltYWdlcyAmJiBzaG93SW1hZ2VUb2dnbGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICA8QmVha2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAge2ltYWdlUmVmcy5sZW5ndGh9IENoZW1pY2FsIFN0cnVjdHVyZXtpbWFnZVJlZnMubGVuZ3RoICE9PSAxID8gJ3MnIDogJyd9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dJbWFnZXMoIXNob3dJbWFnZXMpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC03IHB4LTMgdGV4dC14c1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge3Nob3dJbWFnZXMgPyAnSGlkZSBJbWFnZXMnIDogJ1Nob3cgSW1hZ2VzJ31cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogUmVuZGVyIHRleHQgd2l0aCBlbWJlZGRlZCBpbWFnZXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmFzZSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAge3JlbmRlclRleHRXaXRoSW1hZ2VQbGFjZWhvbGRlcnMoY2xlYW5UZXh0KX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU3RhbmRhbG9uZSBpbWFnZXMgKG5vdCBlbWJlZGRlZCBpbiB0ZXh0KSAqL31cbiAgICAgIHtzaG93SW1hZ2VzICYmIGltYWdlUmVmcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICB7aW1hZ2VSZWZzLm1hcCgoaW1hZ2VLZXkpID0+IHtcbiAgICAgICAgICAgIC8vIFNraXAgaWYgdGhpcyBpbWFnZSB3YXMgYWxyZWFkeSByZW5kZXJlZCBpbmxpbmVcbiAgICAgICAgICAgIGlmIChjbGVhblRleHQuaW5jbHVkZXMoYFtJTUFHRV9QTEFDRUhPTERFUl8ke2ltYWdlS2V5fV1gKSkge1xuICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3QgaW1hZ2VEYXRhID0gaW1hZ2VzW2ltYWdlS2V5XTtcbiAgICAgICAgICAgIGlmICghaW1hZ2VEYXRhKSByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc3QgaXNFeHBhbmRlZCA9IGV4cGFuZGVkSW1hZ2VzLmhhcyhpbWFnZUtleSk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpbWFnZUtleX0gY2xhc3NOYW1lPVwicC0zIGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJlYWtlciBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgQ2hlbWljYWwgU3RydWN0dXJlIC0ge2ltYWdlS2V5LnJlcGxhY2UoJ2NoZW1pY2FsX2ltZ18nLCAnJykucmVwbGFjZSgvXy9nLCAnICcpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUltYWdlRXhwYW5zaW9uKGltYWdlS2V5KX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHB4LTIgdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtNzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uVXAgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIENvbGxhcHNlXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgRXhwYW5kXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8QmFzZTY0SW1hZ2VcbiAgICAgICAgICAgICAgICAgIHNyYz17aW1hZ2VEYXRhfVxuICAgICAgICAgICAgICAgICAgYWx0PXtgQ2hlbWljYWwgc3RydWN0dXJlICR7aW1hZ2VLZXl9YH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgIFwiYm9yZGVyIGJvcmRlci1ibHVlLTMwMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICBpbWFnZUNsYXNzTmFtZSxcbiAgICAgICAgICAgICAgICAgICAgaXNFeHBhbmRlZCA/IFwiY3Vyc29yLXpvb20tb3V0XCIgOiBcImN1cnNvci16b29tLWluXCJcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICBtYXhXaWR0aD17aXNFeHBhbmRlZCA/IG1heEltYWdlV2lkdGggKiAxLjUgOiBtYXhJbWFnZVdpZHRofVxuICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0PXtpc0V4cGFuZGVkID8gbWF4SW1hZ2VIZWlnaHQgKiAxLjUgOiBtYXhJbWFnZUhlaWdodH1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUltYWdlRXhwYW5zaW9uKGltYWdlS2V5KX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuLy8gSGVscGVyIGNvbXBvbmVudCBmb3IgY2hlbWljYWwgcXVlc3Rpb24gb3B0aW9uc1xuaW50ZXJmYWNlIENoZW1pY2FsT3B0aW9uRGlzcGxheVByb3BzIHtcbiAgb3B0aW9uOiB7XG4gICAgdGV4dD86IHN0cmluZztcbiAgICBpbWFnZVVybD86IHN0cmluZztcbiAgICBsYWJlbDogc3RyaW5nO1xuICAgIGlzSW1hZ2VPcHRpb24/OiBib29sZWFuO1xuICB9O1xuICBpbWFnZXM/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+O1xuICBpc0NvcnJlY3Q/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDaGVtaWNhbE9wdGlvbkRpc3BsYXkoe1xuICBvcHRpb24sXG4gIGltYWdlcyA9IHt9LFxuICBpc0NvcnJlY3QgPSBmYWxzZSxcbiAgY2xhc3NOYW1lXG59OiBDaGVtaWNhbE9wdGlvbkRpc3BsYXlQcm9wcykge1xuICAvLyBJZiBvcHRpb24gaGFzIGltYWdlVXJsIGFzIHN0cmluZywgaXQgbWlnaHQgYmUgYSBrZXkgdG8gdGhlIGltYWdlcyBvYmplY3RcbiAgY29uc3QgaW1hZ2VEYXRhID0gb3B0aW9uLmltYWdlVXJsICYmIHR5cGVvZiBvcHRpb24uaW1hZ2VVcmwgPT09ICdzdHJpbmcnIFxuICAgID8gKGltYWdlc1tvcHRpb24uaW1hZ2VVcmxdIHx8IG9wdGlvbi5pbWFnZVVybClcbiAgICA6IG9wdGlvbi5pbWFnZVVybDtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiZmxleCBpdGVtcy1zdGFydCBwLTMgcm91bmRlZC1tZCBib3JkZXIgdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgaXNDb3JyZWN0IFxuICAgICAgICAgID8gXCJib3JkZXItZ3JlZW4tNTAwIGJnLWdyZWVuLTUwXCIgXG4gICAgICAgICAgOiBcImJvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItYmx1ZS0zMDBcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCB3LTYgaC02IHJvdW5kZWQtZnVsbCBiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zXCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj57b3B0aW9uLmxhYmVsfTwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICB7LyogVGV4dCBjb250ZW50ICovfVxuICAgICAgICB7b3B0aW9uLnRleHQgJiYgIW9wdGlvbi5pc0ltYWdlT3B0aW9uICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTJcIj5cbiAgICAgICAgICAgIDxDaGVtaWNhbEltYWdlRGlzcGxheVxuICAgICAgICAgICAgICB0ZXh0PXtvcHRpb24udGV4dH1cbiAgICAgICAgICAgICAgaW1hZ2VzPXtpbWFnZXN9XG4gICAgICAgICAgICAgIG1heEltYWdlV2lkdGg9ezIwMH1cbiAgICAgICAgICAgICAgbWF4SW1hZ2VIZWlnaHQ9ezE1MH1cbiAgICAgICAgICAgICAgc2hvd0ltYWdlVG9nZ2xlPXtmYWxzZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICB7LyogSW1hZ2UgY29udGVudCAqL31cbiAgICAgICAge2ltYWdlRGF0YSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e29wdGlvbi5pc0ltYWdlT3B0aW9uID8gXCJcIiA6IFwibXQtMlwifT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMiB0ZXh0LXhzIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICA8QmVha2VyIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgIENoZW1pY2FsIFN0cnVjdHVyZSBPcHRpb25cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxCYXNlNjRJbWFnZVxuICAgICAgICAgICAgICAgIHNyYz17aW1hZ2VEYXRhfVxuICAgICAgICAgICAgICAgIGFsdD17YE9wdGlvbiAke29wdGlvbi5sYWJlbH0gLSBDaGVtaWNhbCBTdHJ1Y3R1cmVgfVxuICAgICAgICAgICAgICAgIG1heFdpZHRoPXsyMDB9XG4gICAgICAgICAgICAgICAgbWF4SGVpZ2h0PXsxNTB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ibHVlLTMwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAgey8qIEZhbGxiYWNrIGZvciBpbWFnZSBvcHRpb25zIHdpdGhvdXQgZGF0YSAqL31cbiAgICAgICAge29wdGlvbi5pc0ltYWdlT3B0aW9uICYmICFpbWFnZURhdGEgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgQ2hlbWljYWwgc3RydWN0dXJlIG9wdGlvbiAoaW1hZ2Ugbm90IGF2YWlsYWJsZSlcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJjbiIsIkJhc2U2NEltYWdlIiwiTWF0aFRleHQiLCJDaGV2cm9uRG93biIsIkNoZXZyb25VcCIsIkJlYWtlciIsIkJ1dHRvbiIsIkNoZW1pY2FsSW1hZ2VEaXNwbGF5IiwidGV4dCIsImltYWdlcyIsImNsYXNzTmFtZSIsImltYWdlQ2xhc3NOYW1lIiwibWF4SW1hZ2VXaWR0aCIsIm1heEltYWdlSGVpZ2h0Iiwic2hvd0ltYWdlVG9nZ2xlIiwic2hvd0ltYWdlcyIsInNldFNob3dJbWFnZXMiLCJleHBhbmRlZEltYWdlcyIsInNldEV4cGFuZGVkSW1hZ2VzIiwiU2V0IiwicHJvY2Vzc0NoZW1pY2FsVGV4dCIsImlucHV0VGV4dCIsImNsZWFuVGV4dCIsImltYWdlUmVmcyIsImNoZW1pY2FsSW1hZ2VQYXR0ZXJuIiwibWF0Y2giLCJleGVjIiwiZnVsbE1hdGNoIiwicXVlc3Rpb25OdW0iLCJjb250ZXh0IiwiaW1hZ2VLZXkiLCJwdXNoIiwicmVwbGFjZSIsIm1hcmtkb3duUGF0dGVybiIsImFsdCIsInNyYyIsIm1hdGNoaW5nS2V5IiwiT2JqZWN0Iiwia2V5cyIsImZpbmQiLCJrZXkiLCJpbmNsdWRlcyIsImhhc0ltYWdlcyIsImxlbmd0aCIsInRvZ2dsZUltYWdlRXhwYW5zaW9uIiwicHJldiIsIm5ld1NldCIsImhhcyIsImRlbGV0ZSIsImFkZCIsInJlbmRlclRleHRXaXRoSW1hZ2VQbGFjZWhvbGRlcnMiLCJwYXJ0cyIsInNwbGl0IiwibWFwIiwicGFydCIsImluZGV4IiwicGxhY2Vob2xkZXJNYXRjaCIsImltYWdlRGF0YSIsImlzRXhwYW5kZWQiLCJkaXYiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJtYXhXaWR0aCIsIm1heEhlaWdodCIsInNwYW4iLCJDaGVtaWNhbE9wdGlvbkRpc3BsYXkiLCJvcHRpb24iLCJpc0NvcnJlY3QiLCJpbWFnZVVybCIsImxhYmVsIiwiaXNJbWFnZU9wdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\n"));

/***/ })

});