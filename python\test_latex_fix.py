#!/usr/bin/env python3
"""
Test script to verify LaTeX and image fixes
"""

from question_extractor import QuestionExtractor

def test_latex_conversion():
    """Test LaTeX conversion improvements"""
    
    print("🧪 Testing LaTeX conversion fixes...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Test cases from your examples
    test_cases = [
        # Malformed fraction
        "When a DC voltage of 200 V is applied to a coil of self-inductance \\left(\\frac{2\\sqrt{3}}{6}\\right) \\mathrm{H}",
        
        # Malformed square root
        "\\frac{1}{2 π √{L C}}",
        
        # Subscripts and superscripts
        "V_{1} and V_{2} with 10^{-3} and 20×10^{-6}",
        
        # Mixed symbols
        "\\omega = 2000 \\mathrm{rad}/\\mathrm{sec} with \\Omega resistance",
        
        # Garbled text
        "m a i n t a i n i n g t h e s a m e v o l t a g e",
        
        # Solution contamination
        "225(c) f=1/(2π√LC) ∴ f=1/(2×3.14√{0.5×10^-3×20×10^-6}) ≈1600 Hz The natural frequency is"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}:")
        print(f"   Input:  {test_case}")
        
        # Test LaTeX conversion
        latex_result = extractor._convert_latex_to_katex(test_case)
        print(f"   LaTeX:  {latex_result}")
        
        # Test solution contamination removal
        clean_result = extractor._remove_solution_contamination(test_case)
        print(f"   Clean:  {clean_result}")
        
        # Test full cleaning
        full_clean = extractor._clean_text_for_json(test_case)
        print(f"   Final:  {full_clean}")

def test_image_processing():
    """Test image processing improvements"""
    
    print("\n🖼️ Testing image processing fixes...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Mock OCR data with proper base64 images (including problematic double prefix)
    extractor.current_ocr_data = {
        'all_images': {
            'img-132.jpeg': 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAEmAd0DASIAAhEBAxEB',
            'img-108.jpeg': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        }
    }

    print(f"   Mock OCR data loaded with {len(extractor.current_ocr_data['all_images'])} images")
    
    test_images = [
        "In the circuit shown below, the key K is closed at t=0. ![img-132.jpeg](img-132.jpeg) The current through the battery is",
        "The following series L-C-R circuit ![img-109.jpeg](img-109.jpeg) behaves like",
        "In the circuit shown below ![img-108.jpeg](img-108.jpeg) the amplitude will be"
    ]
    
    for i, test_case in enumerate(test_images, 1):
        print(f"\n📷 Image Test {i}:")
        print(f"   Input:  {test_case}")
        
        result = extractor._process_images_in_text(test_case)
        print(f"   Result: {result[:200]}...")

        # Check if image was found
        if 'base64' in result:
            print(f"   ✅ Image converted to base64")
        elif 'Circuit Diagram' in result:
            print(f"   ⚠️ Image not found, using placeholder")

def test_option_cleaning():
    """Test option cleaning"""
    
    print("\n🔤 Testing option cleaning...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    test_options = [
        "\\frac{1}{2 π L C}",
        "\\sqrt{\\frac{3}{5}}",
        "Error: KaTeX parse error: Expected '}', got 'EOF' at end of input: \\sqrt{\\frac{2}",
        "10^{-3} × 20 × 10^{-6}"
    ]
    
    for i, option in enumerate(test_options, 1):
        print(f"\n🔤 Option {i}:")
        print(f"   Input:  {option}")
        
        cleaned = extractor._clean_text_for_json(option)
        print(f"   Clean:  {cleaned}")

def main():
    """Main test function"""
    
    print("🚀 LaTeX and Image Fix Testing")
    print("=" * 50)
    
    try:
        # Test LaTeX conversion
        test_latex_conversion()
        
        # Test image processing
        test_image_processing()
        
        # Test option cleaning
        test_option_cleaning()
        
        print(f"\n✅ All tests completed!")
        print(f"\n💡 Key improvements:")
        print(f"   ✅ LaTeX expressions converted to Unicode symbols")
        print(f"   ✅ Malformed KaTeX expressions fixed")
        print(f"   ✅ Garbled text patterns removed")
        print(f"   ✅ Solution contamination cleaned")
        print(f"   ✅ Images converted to base64 HTML")
        print(f"   ✅ Fallback placeholders for missing images")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
