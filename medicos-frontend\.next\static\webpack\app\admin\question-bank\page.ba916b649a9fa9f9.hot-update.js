"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Used to force refetch when a question is deleted\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Helper function to process question content and replace image references with actual images\n    const processQuestionContent = (content, imageUrls)=>{\n        if (!content || !imageUrls || imageUrls.length === 0) {\n            return content || '';\n        }\n        let processedContent = content;\n        // Pattern 1: Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images\n        processedContent = processedContent.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (match, alt, ref)=>{\n            // For references like \"img-13.jpeg\", use the first available image\n            // Since the backend extracts images in order, the first image should correspond to the first reference\n            if (imageUrls.length > 0) {\n                return '<img src=\"'.concat(imageUrls[0], '\" alt=\"').concat(alt || 'Question Image', '\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 2: Replace HTML img tags with src placeholders\n        processedContent = processedContent.replace(/<img[^>]*src=[\"']([^\"']*)[\"'][^>]*>/gi, (match, src)=>{\n            // If we have images available, use the first one\n            if (imageUrls.length > 0) {\n                return '<img src=\"'.concat(imageUrls[0], '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 3: If content mentions images but no image tags found, append the first image\n        if (!processedContent.includes('<img') && imageUrls.length > 0) {\n            // Check if content has any image-related keywords\n            const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(processedContent);\n            if (hasImageKeywords) {\n                processedContent += '\\n<img src=\"'.concat(imageUrls[0], '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n        }\n        return processedContent;\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        try {\n            var _q_topicId;\n            let parsedOptions = [];\n            // Ensure options is an array and filter out null/undefined values\n            const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n            if (safeOptions.length > 0) {\n                if (typeof safeOptions[0] === 'string') {\n                    // Check if it's a single comma-separated string or an array of individual strings\n                    if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                        // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                        const optionTexts = safeOptions[0].split(',');\n                        parsedOptions = optionTexts.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    } else {\n                        // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                        parsedOptions = safeOptions.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    }\n                } else {\n                    // If options is already an array of objects\n                    parsedOptions = safeOptions.map((opt, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                            imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                        }));\n                }\n            } else {\n                // Log warning for questions without valid options\n                console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n                // Fallback: create empty options if none exist\n                parsedOptions = [\n                    {\n                        label: 'a',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'b',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'c',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'd',\n                        text: 'No options available'\n                    }\n                ];\n            }\n            return {\n                id: q._id,\n                subject: q.subjectId.name,\n                topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n                text: processQuestionContent(q.content, q.imageUrls || []),\n                options: parsedOptions,\n                difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n                correctAnswer: q.answer,\n                reviewStatus: q.reviewStatus,\n                solution: q.solution,\n                hints: q.hints\n            };\n        } catch (error) {\n            var _q_subjectId, _q_topicId1;\n            console.error(\"Error formatting question \".concat(q._id, \":\"), error, q);\n            // Return a fallback question structure\n            return {\n                id: q._id || 'unknown',\n                subject: ((_q_subjectId = q.subjectId) === null || _q_subjectId === void 0 ? void 0 : _q_subjectId.name) || 'Unknown Subject',\n                topic: ((_q_topicId1 = q.topicId) === null || _q_topicId1 === void 0 ? void 0 : _q_topicId1.name) || 'No Topic',\n                text: processQuestionContent(q.content || 'Error loading question content', q.imageUrls || []),\n                options: [\n                    {\n                        label: 'a',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'b',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'c',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'd',\n                        text: 'Error loading options'\n                    }\n                ],\n                difficulty: q.difficulty || 'Unknown',\n                correctAnswer: q.answer || 'a',\n                reviewStatus: q.reviewStatus || 'pending',\n                solution: q.solution,\n                hints: q.hints\n            };\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 417,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"HQdOYEmIZrqalo67hGXpqfFK1Yc=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});