# Google Gemini AI Setup Guide

This guide explains how to set up the PDF Question Extraction API to use Google Gemini AI as the primary AI provider.

## Changes Made

The system has been updated to use **Google Gemini AI** as the default AI provider instead of Mistral AI. The changes include:

1. **Default AI Provider**: Changed from `mistral` to `gemini`
2. **Dependencies**: Added `google-generativeai` package to requirements.txt
3. **Fallback Support**: Mistral AI is still supported for OCR functionality and as a fallback option

## Prerequisites

### 1. Google Gemini API Key

You need a Google Gemini API key to use the service:

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Set it as an environment variable:

```bash
# Windows
set GEMINI_API_KEY=your_gemini_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. Mistral AI API Key (Optional but Recommended)

For OCR functionality and fallback support, you still need a Mistral AI API key:

1. Get your API key from [Mistral AI](https://console.mistral.ai/)
2. Save it in the `key.txt` file in the same directory as the scripts

## Installation

1. **Install Dependencies**:
```bash
pip install -r requirements.txt
```

2. **Set Environment Variables**:
```bash
# Required for Gemini AI
export GEMINI_API_KEY=your_gemini_api_key_here

# Optional but recommended for OCR functionality
# Create key.txt file with your Mistral API key
echo "your_mistral_api_key_here" > key.txt
```

## Usage

### API Server

Start the API server:
```bash
python api_server.py
```

The server will now use Gemini AI by default. You can still specify the AI provider in requests:

```bash
# Use Gemini AI (default)
curl -X POST -F "file=@document.pdf" http://localhost:5000/api/extract

# Use Gemini AI explicitly
curl -X POST -F "file=@document.pdf" -F "ai_provider=gemini" http://localhost:5000/api/extract

# Use Mistral AI as fallback
curl -X POST -F "file=@document.pdf" -F "ai_provider=mistral" http://localhost:5000/api/extract
```

### Direct Usage

```python
from question_extractor import QuestionExtractor

# Use Gemini AI (default)
extractor = QuestionExtractor()

# Use Gemini AI explicitly
extractor = QuestionExtractor(ai_provider='gemini')

# Use Mistral AI
extractor = QuestionExtractor(ai_provider='mistral')

# Extract questions
questions = extractor.extract_questions_from_pdf('document.pdf')
```

## Features

### Gemini AI Advantages

- **Better Language Understanding**: Improved comprehension of complex academic content
- **Enhanced JSON Generation**: More reliable structured output
- **Cost Effective**: Generally more affordable than Mistral AI
- **Faster Response Times**: Typically faster processing

### OCR Functionality

- **OCR Processing**: Still uses Mistral AI for PDF text extraction (OCR)
- **Automatic Fallback**: If Gemini is unavailable, the system can fall back to Mistral
- **Hybrid Approach**: Best of both worlds - Mistral's OCR + Gemini's text analysis

## Troubleshooting

### Common Issues

1. **Missing Gemini API Key**:
   ```
   Error: GEMINI_API_KEY environment variable not set
   ```
   Solution: Set the environment variable as shown above.

2. **OCR Functionality Not Working**:
   ```
   Error: No Mistral client available for OCR
   ```
   Solution: Ensure `key.txt` contains a valid Mistral API key.

3. **Import Errors**:
   ```
   ImportError: No module named 'google.generativeai'
   ```
   Solution: Install dependencies with `pip install -r requirements.txt`

### Verification

Test the setup:
```bash
# Test Gemini AI
python -c "from question_extractor import QuestionExtractor; print('Gemini setup OK')"

# Test API server
curl http://localhost:5000/
```

## Migration from Mistral

If you were previously using Mistral AI as the primary provider:

1. **No Code Changes Required**: The API interface remains the same
2. **Environment Setup**: Just add the `GEMINI_API_KEY` environment variable
3. **Gradual Migration**: You can still use `ai_provider=mistral` in requests during transition
4. **OCR Compatibility**: OCR functionality continues to work with existing Mistral setup

## Performance Comparison

| Feature | Gemini AI | Mistral AI |
|---------|-----------|------------|
| Question Extraction | ✅ Excellent | ✅ Good |
| JSON Reliability | ✅ High | ⚠️ Medium |
| Processing Speed | ✅ Fast | ✅ Fast |
| OCR Capability | ❌ No | ✅ Yes |
| Cost | ✅ Lower | ⚠️ Higher |

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify environment variables are set correctly
3. Ensure all dependencies are installed
4. Check API key validity
