#!/usr/bin/env python3
"""
Superior Image Extraction System
Advanced image extraction and processing for PDF question papers
"""

import os
import sys
import json
import base64
import re
import time
import tempfile
import shutil
from typing import Dict, List, Tuple, Optional
from PIL import Image, ImageEnhance, ImageFilter
import io
import cv2
import numpy as np

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class SuperiorImageExtractor:
    """
    Advanced image extraction system that combines multiple methods:
    1. Adobe PDF Extract API (highest quality)
    2. PyMuPDF (direct PDF image extraction)
    3. Mistral OCR (fallback)
    4. OpenCV image processing (enhancement)
    5. Intelligent image matching and linking
    """
    
    def __init__(self, adobe_credentials=None):
        """
        Initialize Superior Image Extractor
        
        Args:
            adobe_credentials (str): Path to Adobe credentials file
        """
        self.adobe_credentials = adobe_credentials
        self.temp_dir = None
        
        # Image storage
        self.extracted_images = {}
        self.processed_images = {}
        self.image_metadata = {}
        
        # Initialize extractors
        self._init_extractors()
    
    def _init_extractors(self):
        """Initialize all available image extractors"""
        try:
            # Try to initialize Adobe extractor
            if self.adobe_credentials and os.path.exists(self.adobe_credentials):
                try:
                    from adobe_pdf_extractor import AdobePDFExtractor
                    self.adobe_extractor = AdobePDFExtractor(self.adobe_credentials)
                    log_print("✅ [INIT] Adobe PDF Extract API initialized")
                except Exception as e:
                    log_print(f"⚠️ [INIT] Adobe extractor failed: {e}")
                    self.adobe_extractor = None
            else:
                self.adobe_extractor = None
            
            # Try to initialize PyMuPDF
            try:
                import fitz  # PyMuPDF
                self.pymupdf_available = True
                log_print("✅ [INIT] PyMuPDF initialized")
            except ImportError:
                self.pymupdf_available = False
                log_print("⚠️ [INIT] PyMuPDF not available")
            
            # Initialize Mistral OCR as fallback
            try:
                from question_extractor import QuestionExtractor
                self.mistral_extractor = QuestionExtractor(ai_provider='mistral')
                log_print("✅ [INIT] Mistral OCR initialized")
            except Exception as e:
                log_print(f"⚠️ [INIT] Mistral OCR failed: {e}")
                self.mistral_extractor = None
                
        except Exception as e:
            log_print(f"❌ [INIT] Extractor initialization failed: {e}")
    
    def extract_all_images(self, pdf_path: str) -> Dict:
        """
        Extract images using all available methods and combine results
        
        Args:
            pdf_path (str): Path to PDF file
            
        Returns:
            Dict: Combined image extraction results
        """
        try:
            log_print("🚀 [SUPERIOR_IMAGE] Starting superior image extraction...")
            start_time = time.time()
            
            # Create temp directory
            self.temp_dir = tempfile.mkdtemp(prefix="superior_images_")
            
            all_images = {}
            extraction_methods = []
            
            # Method 1: Adobe PDF Extract API (best quality)
            if self.adobe_extractor:
                log_print("📄 [ADOBE_IMAGES] Extracting images with Adobe PDF Extract API...")
                adobe_images = self._extract_with_adobe(pdf_path)
                if adobe_images:
                    all_images.update(adobe_images)
                    extraction_methods.append('adobe')
                    log_print(f"✅ [ADOBE_IMAGES] Extracted {len(adobe_images)} images")
            
            # Method 2: PyMuPDF (direct PDF access)
            if self.pymupdf_available:
                log_print("📄 [PYMUPDF_IMAGES] Extracting images with PyMuPDF...")
                pymupdf_images = self._extract_with_pymupdf(pdf_path)
                if pymupdf_images:
                    # Merge with existing images (avoid duplicates)
                    new_images = self._merge_image_collections(all_images, pymupdf_images)
                    all_images.update(new_images)
                    extraction_methods.append('pymupdf')
                    log_print(f"✅ [PYMUPDF_IMAGES] Added {len(new_images)} new images")
            
            # Method 3: Mistral OCR (fallback)
            if self.mistral_extractor:
                log_print("📄 [MISTRAL_IMAGES] Extracting images with Mistral OCR...")
                mistral_images = self._extract_with_mistral(pdf_path)
                if mistral_images:
                    # Merge with existing images
                    new_images = self._merge_image_collections(all_images, mistral_images)
                    all_images.update(new_images)
                    extraction_methods.append('mistral')
                    log_print(f"✅ [MISTRAL_IMAGES] Added {len(new_images)} new images")
            
            # Process and enhance all images
            log_print("✨ [IMAGE_PROCESSING] Processing and enhancing images...")
            processed_images = self._process_and_enhance_images(all_images)
            
            # Create intelligent image mapping
            log_print("🔗 [IMAGE_MAPPING] Creating intelligent image mapping...")
            image_mapping = self._create_intelligent_mapping(processed_images)
            
            total_duration = time.time() - start_time
            log_print(f"🎯 [SUPERIOR_IMAGE] Superior image extraction completed in {total_duration:.2f}s")
            
            return {
                'images': processed_images,
                'image_mapping': image_mapping,
                'extraction_metadata': {
                    'total_images': len(processed_images),
                    'extraction_methods': extraction_methods,
                    'processing_duration': total_duration,
                    'extraction_method': 'superior_multi_source'
                }
            }
            
        except Exception as e:
            log_print(f"❌ [SUPERIOR_IMAGE] Superior image extraction failed: {e}")
            return {'images': {}, 'image_mapping': {}, 'extraction_metadata': {'error': str(e)}}
        finally:
            self._cleanup_temp_dir()
    
    def _extract_with_adobe(self, pdf_path: str) -> Dict:
        """Extract images using Adobe PDF Extract API"""
        try:
            result = self.adobe_extractor.extract_images_and_text(pdf_path)
            adobe_images = result.get('images', {})
            
            # Process Adobe images
            processed_adobe = {}
            for img_id, img_data in adobe_images.items():
                # Adobe returns high-quality images
                processed_adobe[f"adobe_{img_id}"] = {
                    'data': img_data,
                    'source': 'adobe',
                    'quality': 'high',
                    'original_id': img_id
                }
            
            return processed_adobe
            
        except Exception as e:
            log_print(f"❌ [ADOBE_IMAGES] Adobe image extraction failed: {e}")
            return {}
    
    def _extract_with_pymupdf(self, pdf_path: str) -> Dict:
        """Extract images using PyMuPDF (direct PDF access)"""
        try:
            import fitz
            
            doc = fitz.open(pdf_path)
            pymupdf_images = {}
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    # Get image data
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        # Convert to PNG bytes
                        img_data = pix.tobytes("png")
                        
                        # Convert to base64
                        img_base64 = base64.b64encode(img_data).decode('utf-8')
                        data_url = f"data:image/png;base64,{img_base64}"
                        
                        img_id = f"pymupdf_page{page_num}_img{img_index}"
                        pymupdf_images[img_id] = {
                            'data': data_url,
                            'source': 'pymupdf',
                            'quality': 'high',
                            'page': page_num,
                            'index': img_index,
                            'width': pix.width,
                            'height': pix.height
                        }
                    
                    pix = None  # Free memory
            
            doc.close()
            return pymupdf_images
            
        except Exception as e:
            log_print(f"❌ [PYMUPDF_IMAGES] PyMuPDF image extraction failed: {e}")
            return {}
    
    def _extract_with_mistral(self, pdf_path: str) -> Dict:
        """Extract images using Mistral OCR"""
        try:
            ocr_data = self.mistral_extractor.extract_ocr_data_from_pdf(pdf_path)
            mistral_images = ocr_data.get('all_images', {})
            
            # Process Mistral images
            processed_mistral = {}
            for img_id, img_data in mistral_images.items():
                processed_mistral[f"mistral_{img_id}"] = {
                    'data': img_data,
                    'source': 'mistral',
                    'quality': 'medium',
                    'original_id': img_id
                }
            
            return processed_mistral
            
        except Exception as e:
            log_print(f"❌ [MISTRAL_IMAGES] Mistral image extraction failed: {e}")
            return {}
    
    def _merge_image_collections(self, existing_images: Dict, new_images: Dict) -> Dict:
        """Merge image collections, avoiding duplicates"""
        merged = {}
        
        for img_id, img_data in new_images.items():
            # Check for duplicates by comparing image data
            is_duplicate = False
            
            for existing_id, existing_data in existing_images.items():
                if self._are_images_similar(img_data.get('data'), existing_data.get('data')):
                    log_print(f"🔍 [MERGE] Skipping duplicate image: {img_id} (similar to {existing_id})")
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                merged[img_id] = img_data
        
        return merged
    
    def _are_images_similar(self, img_data1: str, img_data2: str, threshold: float = 0.95) -> bool:
        """Check if two images are similar using basic comparison"""
        try:
            if not img_data1 or not img_data2:
                return False
            
            # Simple comparison: if base64 data is very similar in length
            len1 = len(img_data1)
            len2 = len(img_data2)
            
            if len1 == 0 or len2 == 0:
                return False
            
            similarity = min(len1, len2) / max(len1, len2)
            return similarity > threshold
            
        except Exception:
            return False

    def _process_and_enhance_images(self, images: Dict) -> Dict:
        """Process and enhance all extracted images"""
        try:
            processed = {}

            for img_id, img_info in images.items():
                try:
                    img_data = img_info.get('data')
                    if not img_data:
                        continue

                    # Enhance image quality
                    enhanced_data = self._enhance_image_quality(img_data)

                    # Create processed image info
                    processed[img_id] = {
                        'data': enhanced_data,
                        'source': img_info.get('source', 'unknown'),
                        'quality': img_info.get('quality', 'medium'),
                        'enhanced': True,
                        'original_id': img_info.get('original_id', img_id)
                    }

                    # Add metadata if available
                    if 'width' in img_info:
                        processed[img_id]['width'] = img_info['width']
                    if 'height' in img_info:
                        processed[img_id]['height'] = img_info['height']
                    if 'page' in img_info:
                        processed[img_id]['page'] = img_info['page']

                except Exception as e:
                    log_print(f"⚠️ [PROCESS] Error processing image {img_id}: {e}")
                    # Keep original if processing fails
                    processed[img_id] = img_info

            log_print(f"✅ [PROCESS] Processed {len(processed)} images")
            return processed

        except Exception as e:
            log_print(f"❌ [PROCESS] Image processing failed: {e}")
            return images

    def _enhance_image_quality(self, img_data: str) -> str:
        """Enhance image quality using PIL and OpenCV"""
        try:
            # Extract base64 data
            if img_data.startswith('data:image'):
                base64_data = img_data.split(',')[1]
            else:
                base64_data = img_data

            # Decode image
            img_bytes = base64.b64decode(base64_data)
            img = Image.open(io.BytesIO(img_bytes))

            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Enhance image
            # 1. Increase contrast slightly
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.2)

            # 2. Increase sharpness
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(1.1)

            # 3. Adjust brightness if too dark
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(1.05)

            # Convert back to base64
            buffer = io.BytesIO()
            img.save(buffer, format='JPEG', quality=95, optimize=True)
            enhanced_bytes = buffer.getvalue()
            enhanced_base64 = base64.b64encode(enhanced_bytes).decode('utf-8')

            return f"data:image/jpeg;base64,{enhanced_base64}"

        except Exception as e:
            log_print(f"⚠️ [ENHANCE] Image enhancement failed: {e}")
            return img_data

    def _create_intelligent_mapping(self, images: Dict) -> Dict:
        """Create intelligent mapping between image IDs and question numbers"""
        try:
            mapping = {}

            # Create multiple mapping strategies
            # Strategy 1: Direct number extraction from image IDs
            for img_id, img_info in images.items():
                # Extract numbers from image ID
                numbers = re.findall(r'\d+', img_id)
                if numbers:
                    for num in numbers:
                        question_num = int(num)
                        if question_num not in mapping:
                            mapping[question_num] = []
                        mapping[question_num].append({
                            'img_id': img_id,
                            'source': img_info.get('source', 'unknown'),
                            'quality': img_info.get('quality', 'medium'),
                            'confidence': 0.8
                        })

            # Strategy 2: Page-based mapping (for PyMuPDF images)
            page_images = {}
            for img_id, img_info in images.items():
                if 'page' in img_info:
                    page = img_info['page']
                    if page not in page_images:
                        page_images[page] = []
                    page_images[page].append(img_id)

            # Estimate questions per page and map accordingly
            if page_images:
                estimated_questions_per_page = 10  # Adjust based on your PDFs
                for page, img_ids in page_images.items():
                    start_question = page * estimated_questions_per_page + 1
                    for i, img_id in enumerate(img_ids):
                        question_num = start_question + i
                        if question_num not in mapping:
                            mapping[question_num] = []
                        mapping[question_num].append({
                            'img_id': img_id,
                            'source': images[img_id].get('source', 'unknown'),
                            'quality': images[img_id].get('quality', 'medium'),
                            'confidence': 0.6
                        })

            # Strategy 3: Sequential mapping for remaining images
            unmapped_images = []
            for img_id in images.keys():
                is_mapped = any(img_id in [img['img_id'] for imgs in mapping.values() for img in imgs])
                if not is_mapped:
                    unmapped_images.append(img_id)

            # Map remaining images sequentially
            for i, img_id in enumerate(unmapped_images):
                question_num = i + 1
                while question_num in mapping:
                    question_num += 1

                mapping[question_num] = [{
                    'img_id': img_id,
                    'source': images[img_id].get('source', 'unknown'),
                    'quality': images[img_id].get('quality', 'medium'),
                    'confidence': 0.4
                }]

            log_print(f"✅ [MAPPING] Created intelligent mapping for {len(mapping)} question numbers")
            return mapping

        except Exception as e:
            log_print(f"❌ [MAPPING] Intelligent mapping failed: {e}")
            return {}

    def find_images_for_question(self, question_num: int, question_text: str = "") -> List[Dict]:
        """
        Find the best images for a specific question

        Args:
            question_num (int): Question number
            question_text (str): Question text for context

        Returns:
            List[Dict]: List of matching images with confidence scores
        """
        try:
            matches = []

            # Check direct mapping first
            if hasattr(self, 'image_mapping') and question_num in self.image_mapping:
                for img_info in self.image_mapping[question_num]:
                    img_id = img_info['img_id']
                    if img_id in self.processed_images:
                        matches.append({
                            'img_id': img_id,
                            'data': self.processed_images[img_id]['data'],
                            'confidence': img_info['confidence'],
                            'source': img_info['source'],
                            'method': 'direct_mapping'
                        })

            # Try fuzzy matching based on question text
            if question_text and not matches:
                # Look for image references in question text
                img_refs = re.findall(r'img[-_]?(\d+)', question_text.lower())
                for ref in img_refs:
                    ref_num = int(ref)
                    # Find images with this number
                    for img_id, img_info in self.processed_images.items():
                        if str(ref_num) in img_id:
                            matches.append({
                                'img_id': img_id,
                                'data': img_info['data'],
                                'confidence': 0.9,
                                'source': img_info.get('source', 'unknown'),
                                'method': 'text_reference'
                            })

            # Sort by confidence and quality
            matches.sort(key=lambda x: (x['confidence'], x.get('quality', 'medium')), reverse=True)

            return matches[:3]  # Return top 3 matches

        except Exception as e:
            log_print(f"❌ [FIND] Error finding images for question {question_num}: {e}")
            return []

    def _cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                log_print("🧹 [CLEANUP] Temporary directory cleaned up")
            except Exception as e:
                log_print(f"⚠️ [CLEANUP] Error cleaning up temp directory: {e}")

    def get_all_images_as_dict(self) -> Dict[str, str]:
        """Get all processed images as a simple dict for compatibility"""
        try:
            simple_dict = {}
            for img_id, img_info in self.processed_images.items():
                simple_dict[img_id] = img_info.get('data', '')
            return simple_dict
        except Exception as e:
            log_print(f"❌ [DICT] Error creating image dict: {e}")
            return {}
