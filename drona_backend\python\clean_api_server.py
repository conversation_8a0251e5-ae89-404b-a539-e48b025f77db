#!/usr/bin/env python3
"""
Clean PDF Question Extraction API Server
Simple, focused implementation for extracting questions from PDFs
"""

import os
import time
import json
import tempfile
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from clean_question_extractor import CleanQuestionExtractor

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# Add CORS headers manually
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

ALLOWED_EXTENSIONS = {'pdf'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'success',
        'message': 'Clean PDF Question Extraction API is running',
        'endpoints': {
            'health': 'GET /',
            'extract': 'POST /api/extract'
        }
    })

@app.route('/api/extract', methods=['POST'])
def extract_questions():
    """
    Extract questions, solutions, and hints from uploaded PDF
    
    Form parameters:
    - file: PDF file to process
    
    Returns:
    - JSON with extracted questions including images, solutions, and hints
    """
    start_time = time.time()
    
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type. Only PDF files are allowed'}), 400
        
        print(f"🚀 Processing file: {file.filename}")
        
        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        # Use a cross-platform temporary directory
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, filename)
        file.save(temp_path)
        print(f"📁 Saved file to: {temp_path}")
        
        try:
            # Initialize extractor
            extractor = CleanQuestionExtractor()
            
            # Extract questions
            extraction_start = time.time()
            result_json = extractor.extract_from_pdf(temp_path)
            extraction_time = time.time() - extraction_start
            
            # Parse result to get question count
            try:
                questions = json.loads(result_json)
                question_count = len(questions) if isinstance(questions, list) else 0
            except:
                question_count = 0
            
            total_time = time.time() - start_time
            
            print(f"✅ Extraction completed in {total_time:.2f}s")
            print(f"📊 Extracted {question_count} questions")
            
            # Return success response
            return jsonify({
                'status': 'success',
                'filename': file.filename,
                'questions_count': question_count,
                'data': questions,
                'processing_time': round(total_time, 2),
                'extraction_time': round(extraction_time, 2)
            })
            
        except Exception as e:
            print(f"❌ Extraction error: {e}")
            return jsonify({
                'status': 'error',
                'filename': file.filename,
                'error': f'Extraction failed: {str(e)}',
                'processing_time': round(time.time() - start_time, 2)
            }), 500
            
        finally:
            # Clean up temporary file
            try:
                os.remove(temp_path)
            except:
                pass
    
    except Exception as e:
        print(f"❌ Server error: {e}")
        return jsonify({
            'status': 'error',
            'error': f'Server error: {str(e)}',
            'processing_time': round(time.time() - start_time, 2)
        }), 500

@app.errorhandler(413)
def too_large(_):
    """Handle file too large error"""
    return jsonify({'error': 'File too large. Maximum size is 50MB'}), 413

@app.errorhandler(404)
def not_found(_):
    """Handle 404 errors"""
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(_):
    """Handle 500 errors"""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starting Clean PDF Question Extraction API Server...")
    print("📋 Available endpoints:")
    print("  GET  /                     - Health check")
    print("  POST /api/extract          - Extract questions from PDF")
    print()
    print("🔧 Server configuration:")
    print(f"  Max file size: 50MB")
    print(f"  Allowed extensions: {ALLOWED_EXTENSIONS}")
    print()
    
    # Test extractor initialization
    try:
        test_extractor = CleanQuestionExtractor()
        if test_extractor.client:
            print("✅ PDF extraction service initialized successfully")
        else:
            print("⚠️ PDF extraction service initialized but API key not found")
    except Exception as e:
        print(f"❌ Error initializing extraction service: {e}")
    
    print("🌐 Starting Flask server...")
    app.run(host='0.0.0.0', port=5000, debug=True)
