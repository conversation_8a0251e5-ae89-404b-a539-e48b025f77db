#!/usr/bin/env python3
"""
Debug JSON output to see what's causing the parsing failure
"""

import json
import sys
from question_extractor import QuestionExtractor

def debug_json_output():
    """Debug the JSON output from question extraction"""
    
    # Sample text that matches your PDF format
    sample_text = """
Session : 2024-25                                    AS PER NEW NTA SYLLABUS
Total Questions : 395

JEE/NEET PHYSICS
7. ALTERNATING CURRENT

Single Correct Answer Type

1. A resistor 30 Ω, inductor of reactance 10 Ω and capacitor of reactance 10 Ω are connected in series to an AC voltage source e=300√2 sin(ωt). The current in the circuit is
   a) 10√2 A    b) 10 A    c) 30√11 A    d) 30/√11 A

2. The natural frequency (ω₀) of oscillations in L - C circuit is given by
   a) 1/(2π√LC)    b) 1/(2π√LC)    c) 1/√LC    d) √LC

3. An AC source of angular frequency ω is fed across a resistor R and a capacitor C in series. The current registered is I. If the frequency of source is changed to ω/3 (maintaining the same voltage), the current in the circuit is found to be halved. Calculate the ratio of reactance to resistance at the original frequency ω
   a) √3/5    b) 2/√5    c) 1/5    d) 4/5

: ANSWER KEY :
1) b    2) a    3) a    4) d    5) a    6) b    7) a    8) b

: HINTS AND SOLUTIONS :
1 (b)
e = 300√2 sin ωt
I₀ = e₀/Z = 300√2/√(30)² + (10 - 10)²
I₀ = 300√2/30 = 10√2 A

2 (a)
For LC circuit: ω₀ = 1/√LC
Natural frequency = ω₀/2π = 1/(2π√LC)

3 (a)
Original: I = V/√(R² + (1/ωC)²)
New: I/2 = V/√(R² + (3/ωC)²)
Solving: XC/R = √3/5
"""
    
    print("🔍 Debugging JSON output...")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Test the structured extraction
        print("🔄 [STEP 1] Testing structured extraction...")
        result = extractor._extract_from_structured_format(sample_text, 3)
        
        print(f"📊 [RESULT] Raw result type: {type(result)}")
        print(f"📊 [RESULT] Raw result length: {len(result) if result else 0}")
        
        if result:
            print(f"📄 [RESULT] First 500 characters:")
            print(result[:500])
            print("...")
            print(f"📄 [RESULT] Last 500 characters:")
            print("..." + result[-500:])
        
        # Test JSON parsing
        print(f"\n🔄 [STEP 2] Testing JSON parsing...")
        try:
            parsed = json.loads(result)
            print(f"✅ [JSON] Successfully parsed JSON")
            print(f"📊 [JSON] Type: {type(parsed)}")
            print(f"📊 [JSON] Length: {len(parsed) if isinstance(parsed, list) else 'Not a list'}")
            
            if isinstance(parsed, list) and len(parsed) > 0:
                print(f"📝 [SAMPLE] First question:")
                first_q = parsed[0]
                print(f"   Keys: {list(first_q.keys()) if isinstance(first_q, dict) else 'Not a dict'}")
                if isinstance(first_q, dict):
                    print(f"   Question: {first_q.get('question', 'No question')[:100]}...")
                    print(f"   Answer: {first_q.get('answer', 'No answer')}")
                    print(f"   Options: {list(first_q.get('options', {}).keys())}")
            
        except json.JSONDecodeError as e:
            print(f"❌ [JSON] JSON parsing failed: {e}")
            print(f"📍 [JSON] Error position: {e.pos if hasattr(e, 'pos') else 'Unknown'}")
            
            # Show the problematic area
            if hasattr(e, 'pos') and e.pos < len(result):
                start = max(0, e.pos - 100)
                end = min(len(result), e.pos + 100)
                print(f"📄 [ERROR_CONTEXT] Around error position:")
                print(f"'{result[start:end]}'")
        
        # Test the cleaning function
        print(f"\n🔄 [STEP 3] Testing JSON cleaning...")
        if result:
            try:
                parsed_before_clean = json.loads(result)
                cleaned = extractor._clean_questions_for_json(parsed_before_clean)
                cleaned_json = json.dumps(cleaned, ensure_ascii=False, indent=2)
                
                print(f"✅ [CLEAN] Cleaning successful")
                print(f"📊 [CLEAN] Cleaned length: {len(cleaned_json)}")
                print(f"📄 [CLEAN] First 300 characters of cleaned JSON:")
                print(cleaned_json[:300])
                
            except Exception as clean_error:
                print(f"❌ [CLEAN] Cleaning failed: {clean_error}")
        
        return result
        
    except Exception as e:
        print(f"❌ [ERROR] Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_json_validation(json_text):
    """Test the JSON validation functions"""
    
    print(f"\n🧪 Testing JSON validation...")
    print(f"📊 Input length: {len(json_text) if json_text else 0}")
    
    if not json_text:
        print("❌ No JSON text provided")
        return
    
    # Test fast validation
    from api_server import fast_json_validation, fix_json_formatting
    
    print(f"🔄 [FAST_VALIDATION] Testing fast validation...")
    fast_valid = fast_json_validation(json_text)
    print(f"📊 [FAST_VALIDATION] Result: {fast_valid}")
    
    if not fast_valid:
        print(f"🔄 [JSON_FIX] Testing JSON fix...")
        fixed = fix_json_formatting(json_text)
        if fixed:
            print(f"📊 [JSON_FIX] Fixed length: {len(fixed)}")
            print(f"📄 [JSON_FIX] First 200 chars: {fixed[:200]}")
            
            # Test if fix worked
            fixed_valid = fast_json_validation(fixed)
            print(f"📊 [JSON_FIX] Fixed validation: {fixed_valid}")
        else:
            print(f"❌ [JSON_FIX] Fix failed")
    
    # Test actual JSON parsing
    print(f"🔄 [JSON_PARSE] Testing actual JSON parsing...")
    try:
        parsed = json.loads(json_text)
        print(f"✅ [JSON_PARSE] Parsing successful")
        print(f"📊 [JSON_PARSE] Type: {type(parsed)}")
        print(f"📊 [JSON_PARSE] Length: {len(parsed) if isinstance(parsed, (list, dict)) else 'Not countable'}")
    except json.JSONDecodeError as e:
        print(f"❌ [JSON_PARSE] Parsing failed: {e}")
        print(f"📍 [JSON_PARSE] Error at position: {e.pos if hasattr(e, 'pos') else 'Unknown'}")

def main():
    """Main function"""
    
    print("🚀 JSON Debug Tool")
    print("=" * 50)
    
    # Debug the JSON output
    result = debug_json_output()
    
    # Test validation if we got a result
    if result:
        test_json_validation(result)
    
    print(f"\n🎯 Summary:")
    if result:
        print("✅ Extraction produced output")
        try:
            json.loads(result)
            print("✅ JSON is valid")
        except:
            print("❌ JSON is invalid - this is the problem!")
    else:
        print("❌ No output from extraction")
    
    print(f"\n💡 Next steps:")
    print("1. Check the JSON structure in the output above")
    print("2. Look for missing brackets, quotes, or commas")
    print("3. Check for unescaped characters")
    print("4. Verify the cleaning function is working properly")

if __name__ == "__main__":
    main()
