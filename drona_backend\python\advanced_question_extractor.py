#!/usr/bin/env python3
"""
Advanced Question Extractor with Multi-Stage Pipeline
Combines multiple extraction strategies for maximum question recovery
"""

import os
import sys
import json
import time
import re
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Import existing extractors
from question_extractor import QuestionExtractor
try:
    from enhanced_question_extractor import EnhancedQuestionExtractor
except ImportError:
    EnhancedQuestionExtractor = None

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class AdvancedQuestionExtractor:
    """
    Advanced multi-stage question extraction system that combines:
    1. Pattern-based extraction (regex patterns)
    2. Multiple AI providers (Gemini + Mistral)
    3. Adobe PDF Extract API (if available)
    4. Intelligent chunking strategies
    5. Advanced deduplication
    6. Question validation and enhancement
    """
    
    def __init__(self, ai_provider='gemini', adobe_credentials=None):
        """
        Initialize Advanced Question Extractor
        
        Args:
            ai_provider (str): Primary AI provider ('gemini' or 'mistral')
            adobe_credentials (str): Path to Adobe credentials file
        """
        self.ai_provider = ai_provider
        self.adobe_credentials = adobe_credentials
        
        # Initialize extractors
        self.standard_extractor = QuestionExtractor(ai_provider=ai_provider)
        self.enhanced_extractor = None
        
        # Try to initialize enhanced extractor
        if EnhancedQuestionExtractor:
            try:
                self.enhanced_extractor = EnhancedQuestionExtractor(
                    ai_provider=ai_provider,
                    adobe_credentials_file=adobe_credentials
                )
                log_print("✅ [INIT] Enhanced extractor with Adobe PDF Extract API initialized")
            except Exception as e:
                log_print(f"⚠️ [INIT] Enhanced extractor failed: {e}")
        
        # Extraction statistics
        self.stats = {
            'total_questions': 0,
            'pattern_extracted': 0,
            'ai_extracted': 0,
            'adobe_extracted': 0,
            'duplicates_removed': 0,
            'validation_failed': 0
        }
    
    def extract_questions_comprehensive(self, pdf_path: str) -> Dict:
        """
        Comprehensive extraction using all available methods
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            Dict: Comprehensive extraction results
        """
        try:
            log_print("🚀 [ADVANCED_EXTRACT] Starting comprehensive multi-stage extraction...")
            start_time = time.time()
            
            # Stage 1: Pattern-based extraction (fast, high precision)
            log_print("🔍 [STAGE_1] Pattern-based extraction...")
            pattern_questions = self._extract_with_patterns(pdf_path)
            self.stats['pattern_extracted'] = len(pattern_questions)
            log_print(f"✅ [STAGE_1] Pattern extraction: {len(pattern_questions)} questions")
            
            # Stage 2: Enhanced extraction (Adobe + AI)
            log_print("🤖 [STAGE_2] Enhanced AI extraction...")
            ai_questions = self._extract_with_enhanced_ai(pdf_path)
            self.stats['ai_extracted'] = len(ai_questions)
            log_print(f"✅ [STAGE_2] AI extraction: {len(ai_questions)} questions")
            
            # Stage 3: Fallback extraction (standard method)
            log_print("🔄 [STAGE_3] Fallback extraction...")
            fallback_questions = self._extract_with_fallback(pdf_path)
            log_print(f"✅ [STAGE_3] Fallback extraction: {len(fallback_questions)} questions")
            
            # Stage 4: Combine and deduplicate
            log_print("🔗 [STAGE_4] Combining and deduplicating results...")
            all_questions = pattern_questions + ai_questions + fallback_questions
            log_print(f"📊 [STAGE_4] Total before deduplication: {len(all_questions)} questions")
            
            unique_questions = self._advanced_deduplication(all_questions)
            self.stats['duplicates_removed'] = len(all_questions) - len(unique_questions)
            log_print(f"✅ [STAGE_4] After deduplication: {len(unique_questions)} questions")
            
            # Stage 5: Validation and enhancement
            log_print("✨ [STAGE_5] Validation and enhancement...")
            validated_questions = self._validate_and_enhance(unique_questions)
            self.stats['validation_failed'] = len(unique_questions) - len(validated_questions)
            self.stats['total_questions'] = len(validated_questions)
            log_print(f"✅ [STAGE_5] Final validated questions: {len(validated_questions)}")
            
            # Stage 6: Format output
            log_print("📝 [STAGE_6] Formatting output...")
            final_result = self._format_final_output(validated_questions)
            
            total_duration = time.time() - start_time
            log_print(f"🎯 [ADVANCED_COMPLETE] Advanced extraction completed in {total_duration:.2f}s")
            
            return {
                'questions': json.dumps(validated_questions, indent=2),
                'extraction_metadata': {
                    'total_duration': total_duration,
                    'extraction_method': 'advanced_multi_stage',
                    'stages_used': ['pattern', 'ai', 'fallback', 'deduplication', 'validation'],
                    'statistics': self.stats,
                    'success_rate': self._calculate_success_rate()
                }
            }
            
        except Exception as e:
            log_print(f"❌ [ADVANCED_EXTRACT] Error: {e}")
            # Fallback to standard extraction
            return self._emergency_fallback(pdf_path)
    
    def _extract_with_patterns(self, pdf_path: str) -> List[Dict]:
        """Extract questions using regex patterns (fast and reliable)"""
        try:
            # Get OCR data
            ocr_data = self.standard_extractor.extract_ocr_data_from_pdf(pdf_path)
            full_text = ocr_data['full_text']
            
            questions = []
            
            # Pattern 1: Numbered questions with options
            pattern1 = r'(\d+)\.\s*(.+?)\n\s*(?:a\)|A\))\s*(.+?)\n\s*(?:b\)|B\))\s*(.+?)\n\s*(?:c\)|C\))\s*(.+?)\n\s*(?:d\)|D\))\s*(.+?)(?=\n\d+\.|$)'
            matches1 = re.findall(pattern1, full_text, re.DOTALL | re.IGNORECASE)
            
            for match in matches1:
                question = {
                    'content': match[1].strip(),
                    'options': {
                        'A': match[2].strip(),
                        'B': match[3].strip(),
                        'C': match[4].strip(),
                        'D': match[5].strip()
                    },
                    'answer': 'A',  # Default, will be enhanced later
                    'type': 'mcq',
                    'difficulty': 'medium',
                    'extraction_method': 'pattern_1'
                }
                questions.append(question)
            
            # Pattern 2: Questions with parenthetical options
            pattern2 = r'(\d+)\.\s*(.+?)\s*\(a\)\s*(.+?)\s*\(b\)\s*(.+?)\s*\(c\)\s*(.+?)\s*\(d\)\s*(.+?)(?=\n\d+\.|$)'
            matches2 = re.findall(pattern2, full_text, re.DOTALL | re.IGNORECASE)
            
            for match in matches2:
                question = {
                    'content': match[1].strip(),
                    'options': {
                        'A': match[2].strip(),
                        'B': match[3].strip(),
                        'C': match[4].strip(),
                        'D': match[5].strip()
                    },
                    'answer': 'A',
                    'type': 'mcq',
                    'difficulty': 'medium',
                    'extraction_method': 'pattern_2'
                }
                questions.append(question)
            
            # Pattern 3: Simple question-answer format
            pattern3 = r'Q\.?\s*(\d+)[:\.]?\s*(.+?)\n(?:Answer|Ans)[:\.]?\s*(.+?)(?=\nQ\.?\s*\d+|$)'
            matches3 = re.findall(pattern3, full_text, re.DOTALL | re.IGNORECASE)
            
            for match in matches3:
                question = {
                    'content': match[1].strip(),
                    'options': {
                        'A': match[2].strip(),
                        'B': 'Option B',
                        'C': 'Option C',
                        'D': 'Option D'
                    },
                    'answer': match[2].strip(),
                    'type': 'mcq',
                    'difficulty': 'medium',
                    'extraction_method': 'pattern_3'
                }
                questions.append(question)
            
            log_print(f"📊 [PATTERN] Found {len(questions)} questions using regex patterns")
            return questions
            
        except Exception as e:
            log_print(f"❌ [PATTERN] Pattern extraction failed: {e}")
            return []
    
    def _extract_with_enhanced_ai(self, pdf_path: str) -> List[Dict]:
        """Extract using enhanced AI methods"""
        try:
            if self.enhanced_extractor:
                log_print("🤖 [AI] Using enhanced extractor with Adobe PDF Extract API...")
                result = self.enhanced_extractor.extract_comprehensive_data_enhanced(pdf_path)
                questions_json = result.get('questions', '[]')
                questions = json.loads(questions_json) if questions_json else []
                
                # Mark extraction method
                for q in questions:
                    q['extraction_method'] = 'enhanced_ai'
                
                return questions
            else:
                log_print("🤖 [AI] Using standard AI extraction...")
                result = self.standard_extractor.extract_structured_questions_from_pdf(pdf_path)
                questions = json.loads(result) if result else []
                
                # Mark extraction method
                for q in questions:
                    q['extraction_method'] = 'standard_ai'
                
                return questions
                
        except Exception as e:
            log_print(f"❌ [AI] AI extraction failed: {e}")
            return []
    
    def _extract_with_fallback(self, pdf_path: str) -> List[Dict]:
        """Fallback extraction using aggressive methods"""
        try:
            log_print("🔄 [FALLBACK] Using aggressive fallback extraction...")
            
            # Try multiple AI approaches
            approaches = ['aggressive', 'simple', 'pattern_based']
            all_fallback_questions = []
            
            for approach in approaches:
                try:
                    if approach == 'aggressive':
                        result = self.standard_extractor._try_aggressive_extraction(
                            self.standard_extractor.extract_ocr_data_from_pdf(pdf_path)['full_text']
                        )
                    elif approach == 'simple':
                        result = self.standard_extractor._try_simple_extraction(
                            self.standard_extractor.extract_ocr_data_from_pdf(pdf_path)['full_text']
                        )
                    else:
                        result = self.standard_extractor._try_pattern_based_extraction(
                            self.standard_extractor.extract_ocr_data_from_pdf(pdf_path)['full_text']
                        )
                    
                    questions = json.loads(result) if result else []
                    
                    # Mark extraction method
                    for q in questions:
                        q['extraction_method'] = f'fallback_{approach}'
                    
                    all_fallback_questions.extend(questions)
                    log_print(f"✅ [FALLBACK] {approach}: {len(questions)} questions")
                    
                except Exception as e:
                    log_print(f"⚠️ [FALLBACK] {approach} failed: {e}")
                    continue
            
            return all_fallback_questions
            
        except Exception as e:
            log_print(f"❌ [FALLBACK] All fallback methods failed: {e}")
            return []

    def _advanced_deduplication(self, questions: List[Dict]) -> List[Dict]:
        """Advanced deduplication using multiple similarity metrics"""
        try:
            log_print("🔗 [DEDUP] Starting advanced deduplication...")

            if not questions:
                return []

            unique_questions = []
            seen_hashes = set()

            for question in questions:
                # Create multiple hash signatures
                content = question.get('content', '') or question.get('question', '')

                # Hash 1: Exact content match
                exact_hash = hash(content.strip().lower())

                # Hash 2: Normalized content (remove extra spaces, punctuation)
                normalized = re.sub(r'[^\w\s]', '', content.lower())
                normalized = ' '.join(normalized.split())
                normalized_hash = hash(normalized)

                # Hash 3: First 50 characters
                short_hash = hash(content[:50].strip().lower())

                # Check if any hash matches existing questions
                current_hashes = {exact_hash, normalized_hash, short_hash}

                if not any(h in seen_hashes for h in current_hashes):
                    unique_questions.append(question)
                    seen_hashes.update(current_hashes)
                else:
                    log_print(f"🔗 [DEDUP] Removed duplicate: {content[:50]}...")

            log_print(f"✅ [DEDUP] Removed {len(questions) - len(unique_questions)} duplicates")
            return unique_questions

        except Exception as e:
            log_print(f"❌ [DEDUP] Deduplication failed: {e}")
            return questions

    def _validate_and_enhance(self, questions: List[Dict]) -> List[Dict]:
        """Validate and enhance questions"""
        try:
            log_print("✨ [VALIDATE] Starting validation and enhancement...")

            validated_questions = []

            for i, question in enumerate(questions):
                try:
                    # Normalize question structure
                    normalized_q = self._normalize_question_structure(question)

                    # Validate required fields
                    if not self._validate_question(normalized_q):
                        log_print(f"⚠️ [VALIDATE] Question {i+1} failed validation")
                        continue

                    # Enhance question
                    enhanced_q = self._enhance_question(normalized_q)

                    validated_questions.append(enhanced_q)

                except Exception as e:
                    log_print(f"❌ [VALIDATE] Error processing question {i+1}: {e}")
                    continue

            log_print(f"✅ [VALIDATE] Validated {len(validated_questions)} questions")
            return validated_questions

        except Exception as e:
            log_print(f"❌ [VALIDATE] Validation failed: {e}")
            return questions

    def _normalize_question_structure(self, question: Dict) -> Dict:
        """Normalize question structure to standard format"""
        normalized = {}

        # Handle different content field names
        content = question.get('content') or question.get('question') or question.get('text', '')
        normalized['content'] = content.strip()

        # Handle options
        options = question.get('options', {})
        if isinstance(options, dict):
            normalized['options'] = [
                options.get('A', ''),
                options.get('B', ''),
                options.get('C', ''),
                options.get('D', '')
            ]
        elif isinstance(options, list):
            normalized['options'] = options[:4]  # Take first 4 options
            # Pad with empty strings if less than 4
            while len(normalized['options']) < 4:
                normalized['options'].append('')
        else:
            normalized['options'] = ['', '', '', '']

        # Handle answer
        answer = question.get('answer', 'A')
        if isinstance(answer, str) and len(answer) == 1 and answer.upper() in 'ABCD':
            normalized['answer'] = answer.upper()
        else:
            # Try to find answer in options
            normalized['answer'] = 'A'  # Default

        # Other fields
        normalized['type'] = question.get('type', 'mcq')
        normalized['difficulty'] = question.get('difficulty', 'medium')
        normalized['extraction_method'] = question.get('extraction_method', 'unknown')

        return normalized

    def _validate_question(self, question: Dict) -> bool:
        """Validate if question meets minimum requirements"""
        # Check content
        if not question.get('content') or len(question['content'].strip()) < 10:
            return False

        # Check options
        options = question.get('options', [])
        if not options or len(options) < 2:
            return False

        # Check if at least 2 options have content
        valid_options = sum(1 for opt in options if opt and len(opt.strip()) > 0)
        if valid_options < 2:
            return False

        return True

    def _enhance_question(self, question: Dict) -> Dict:
        """Enhance question with additional processing"""
        enhanced = question.copy()

        # Clean content
        enhanced['content'] = self._clean_text(enhanced['content'])

        # Clean options
        enhanced['options'] = [self._clean_text(opt) for opt in enhanced['options']]

        # Add metadata
        enhanced['id'] = f"q_{hash(enhanced['content'])}"
        enhanced['created_at'] = time.time()

        return enhanced

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ''

        # Remove extra whitespace
        text = ' '.join(text.split())

        # Remove common OCR artifacts
        text = re.sub(r'[^\w\s\.\?\!\,\;\:\(\)\[\]\-\+\=\*\/\%\$\#\@\&]', '', text)

        return text.strip()

    def _format_final_output(self, questions: List[Dict]) -> str:
        """Format questions for final output"""
        try:
            return json.dumps(questions, indent=2, ensure_ascii=False)
        except Exception as e:
            log_print(f"❌ [FORMAT] Error formatting output: {e}")
            return "[]"

    def _calculate_success_rate(self) -> float:
        """Calculate extraction success rate"""
        total_attempts = sum([
            self.stats['pattern_extracted'],
            self.stats['ai_extracted']
        ])

        if total_attempts == 0:
            return 0.0

        return (self.stats['total_questions'] / total_attempts) * 100

    def _emergency_fallback(self, pdf_path: str) -> Dict:
        """Emergency fallback when all methods fail"""
        try:
            log_print("🚨 [EMERGENCY] Using emergency fallback extraction...")

            # Use standard extractor as last resort
            result = self.standard_extractor.extract_questions_from_pdf_standard(pdf_path)
            questions = json.loads(result) if result else []

            return {
                'questions': json.dumps(questions, indent=2),
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'emergency_fallback',
                    'stages_used': ['emergency'],
                    'statistics': {'total_questions': len(questions)},
                    'success_rate': 0.0
                }
            }

        except Exception as e:
            log_print(f"❌ [EMERGENCY] Emergency fallback failed: {e}")
            return {
                'questions': "[]",
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'failed',
                    'stages_used': [],
                    'statistics': {'total_questions': 0},
                    'success_rate': 0.0
                }
            }
