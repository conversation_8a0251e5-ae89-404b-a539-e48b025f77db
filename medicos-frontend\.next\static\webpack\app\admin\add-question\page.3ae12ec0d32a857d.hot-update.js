"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/admin/add-question-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/add-question-form.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_IMAGE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\nconst ACCEPTED_PDF_TYPES = [\n    \"application/pdf\"\n];\n// Manual form schema with custom validation for options\nconst manualFormSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\n// PDF upload form schema\nconst pdfUploadSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional()\n});\nfunction AddQuestionForm() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Manual form states\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [manualTopics, setManualTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // PDF upload states\n    const [pdfTopics, setPdfTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPdfFile, setSelectedPdfFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useChemicalExtraction, setUseChemicalExtraction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"AddQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AddQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"AddQuestionForm.useEffect\"], []);\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize manual form\n    const manualForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(manualFormSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Initialize PDF upload form\n    const pdfForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(pdfUploadSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\"\n        }\n    });\n    // Handle subject change for manual form\n    const handleManualSubjectChange = (value)=>{\n        manualForm.setValue(\"subject\", value);\n        manualForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setManualTopics(selectedSubject.topics || []);\n        } else {\n            setManualTopics([]);\n        }\n    };\n    // Handle subject change for PDF form\n    const handlePdfSubjectChange = (value)=>{\n        pdfForm.setValue(\"subject\", value);\n        pdfForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setPdfTopics(selectedSubject.topics || []);\n        } else {\n            setPdfTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            manualForm.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function for manual form\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle manual form submission\n    const onManualSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Manual form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Get user ID from localStorage if available\n            const userData = localStorage.getItem(\"userData\");\n            let userId;\n            try {\n                if (userData) {\n                    const parsed = JSON.parse(userData);\n                    userId = parsed._id || parsed.id;\n                }\n            } catch (e) {\n                console.error(\"Error parsing user data:\", e);\n            }\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add createdBy if we have a valid user ID\n            if (userId) {\n                baseQuestionData.createdBy = userId;\n            }\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            // If question has an image, embed it in the question text as base64\n            let finalQuestionData = {\n                ...questionData\n            };\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.createQuestion)(finalQuestionData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                // Reset manual form\n                resetManualForm();\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error adding question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle PDF form submission\n    const onPdfSubmit = async (data)=>{\n        if (!selectedPdfFile) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"Please select a PDF file to upload.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"PDF form data:\", data, \"Chemical extraction:\", useChemicalExtraction);\n            // Submit to appropriate bulk upload API based on extraction type\n            const result = useChemicalExtraction ? await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadChemicalQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined) : await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined);\n            // Display success toast with extraction type info\n            const extractionType = useChemicalExtraction ? \"chemical questions with molecular structures\" : \"questions\";\n            const questionsCount = result.questionsAdded || result.questionsCreated || 'questions';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"PDF Upload Successful\",\n                description: \"Successfully uploaded \".concat(questionsCount, \" \").concat(extractionType, \" from PDF.\")\n            });\n            // Reset PDF form\n            resetPdfForm();\n        } catch (error) {\n            console.error(\"Error uploading PDF:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to upload PDF. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset manual form\n    const resetManualForm = ()=>{\n        manualForm.reset({\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        });\n        setQuestionImage(null);\n        setOptionImages({\n            A: null,\n            B: null,\n            C: null,\n            D: null\n        });\n        setManualTopics([]);\n        setOptionValidationErrors({\n            A: false,\n            B: false,\n            C: false,\n            D: false\n        });\n    };\n    // Reset PDF form\n    const resetPdfForm = ()=>{\n        pdfForm.reset({\n            subject: \"\",\n            topic: \"\"\n        });\n        setSelectedPdfFile(null);\n        setPdfTopics([]);\n    };\n    // Handle PDF file selection\n    const handlePdfFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (file.type !== \"application/pdf\") {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Invalid File Type\",\n                    description: \"Please select a PDF file.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"File Too Large\",\n                    description: \"File size must be less than 50MB.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setSelectedPdfFile(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    children: \"Add Questions\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"manual\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manual Entry\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"pdf\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upload PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"manual\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...manualForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: manualForm.handleSubmit(onManualSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handleManualSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: manualTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : manualTopics.length > 0 ? \"Select a topic\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: manualTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 548,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"questionText\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Question Text *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                        placeholder: \"Enter your question here...\",\n                                                                        className: \"min-h-[100px]\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Question Image (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Upload an image to accompany your question\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-9\",\n                                                                    onClick: ()=>{\n                                                                        var _questionImageRef_current;\n                                                                        return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Upload Image\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    ref: questionImageRef,\n                                                                    className: \"hidden\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: (e)=>handleImageUpload(e, \"question\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: questionImage || \"/placeholder.svg\",\n                                                                            alt: \"Question image\",\n                                                                            width: 100,\n                                                                            height: 100,\n                                                                            className: \"object-cover rounded-md border h-[100px] w-[100px]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"destructive\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 absolute -top-2 -right-2 rounded-full\",\n                                                                            onClick: ()=>removeImage(\"question\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: \"Answer Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, this),\n                                                [\n                                                    \"A\",\n                                                    \"B\",\n                                                    \"C\",\n                                                    \"D\"\n                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                                control: manualForm.control,\n                                                                name: \"option\".concat(option),\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-green-600 ml-2\",\n                                                                                        children: \"(Image uploaded)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                    placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                                    ...field\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-red-600\",\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    \" requires either text or an image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 31\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 27\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 mt-8 md:mt-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-9 text-xs\",\n                                                                            onClick: ()=>{\n                                                                                var _optionImageRefs_option_current;\n                                                                                return (_optionImageRefs_option_current = optionImageRefs[option].current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 685,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Image\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            ref: optionImageRefs[option],\n                                                                            className: \"hidden\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>handleImageUpload(e, option)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                    src: optionImages[option] || \"/placeholder.svg\",\n                                                                                    alt: \"Option \".concat(option, \" image\"),\n                                                                                    width: 60,\n                                                                                    height: 60,\n                                                                                    className: \"object-cover rounded-md border h-[60px] w-[60px]\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 698,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: \"destructive\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-5 w-5 absolute -top-2 -right-2 rounded-full\",\n                                                                                    onClick: ()=>removeImage(option),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 712,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 705,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, option, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"correctAnswer\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Correct Answer *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"A\",\n                                                                            \"B\",\n                                                                            \"C\",\n                                                                            \"D\"\n                                                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: option,\n                                                                                            id: \"manual-option-\".concat(option)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 739,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-option-\".concat(option),\n                                                                                        children: option\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 741,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, option, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"difficulty\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Difficulty Level *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"Easy\",\n                                                                            \"Medium\",\n                                                                            \"Hard\"\n                                                                        ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: level,\n                                                                                            id: \"manual-level-\".concat(level)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 768,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 767,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-level-\".concat(level),\n                                                                                        children: level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 770,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, level, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                            control: manualForm.control,\n                                            name: \"explanation\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Explanation (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                placeholder: \"Explain why the correct answer is right...\",\n                                                                className: \"min-h-[80px]\",\n                                                                ...field\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormDescription, {\n                                                            children: \"This will be shown to students after they answer the question.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding Question...\"\n                                                        ]\n                                                    }, void 0, true) : \"Add Question\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetManualForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"pdf\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...pdfForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: pdfForm.handleSubmit(onPdfSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handlePdfSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 856,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 855,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: pdfTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : pdfTopics.length > 0 ? \"Select a topic (optional)\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 887,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 885,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: pdfTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 900,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                            children: \"PDF File *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 918,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Upload a PDF file containing questions to be extracted and added to the question bank\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"pdf-upload\",\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                                                children: selectedPdfFile ? selectedPdfFile.name : \"Choose PDF file or drag and drop\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 933,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-1 block text-xs text-gray-500\",\n                                                                                children: \"PDF up to 50MB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 936,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"pdf-upload\",\n                                                                        type: \"file\",\n                                                                        className: \"sr-only\",\n                                                                        accept: \".pdf\",\n                                                                        onChange: handlePdfFileChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById('pdf-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: \"Select PDF File\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedPdfFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: selectedPdfFile.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (selectedPdfFile.size / 1024 / 1024).toFixed(2),\n                                                                        \" MB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setSelectedPdfFile(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting || !selectedPdfFile,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Uploading PDF...\"\n                                                        ]\n                                                    }, void 0, true) : \"Upload PDF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetPdfForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_s(AddQuestionForm, \"KWBNHzcTQIFF+oVUttgNWRp9/No=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AddQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"AddQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/add-question-form.tsx\n"));

/***/ })

});