#!/usr/bin/env python3
"""
Question Extraction module using Google Gemini AI (primary) and Mistral AI (fallback) capabilities
"""

import os
import base64
import re
import time
import sys

# Monkey patch httpx to force UTF-8 encoding and debug the issue
import httpx._models
original_normalize_header_value = httpx._models._normalize_header_value

def patched_normalize_header_value(value, encoding=None):
    """Patched version that logs problematic headers and handles non-ASCII characters"""
    if isinstance(value, str):
        try:
            # Try ASCII first
            return value.encode('ascii')
        except UnicodeEncodeError as e:
            # Log the problematic header value for debugging
            log_print(f"🔍 [DEBUG] Problematic header value: {repr(value)}")
            log_print(f"🔍 [DEBUG] Unicode error: {e}")

            # Try to create a safe ASCII version
            safe_value = value.encode('ascii', 'ignore').decode('ascii')
            if not safe_value:
                safe_value = 'safe-header-value'
            log_print(f"🔍 [DEBUG] Using safe value: {repr(safe_value)}")
            return safe_value.encode('ascii')
    return original_normalize_header_value(value, encoding)

# Apply the monkey patch
httpx._models._normalize_header_value = patched_normalize_header_value

from mistralai import Mistral
import google.generativeai as genai

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class QuestionExtractor:
    """Class to extract questions and multiple-choice options from documents"""
    
    def __init__(self, ai_provider='gemini', api_key_file='key.txt'):
        """Initialize the question extractor with AI provider and API key"""
        self.ai_provider = ai_provider.lower() if ai_provider else 'gemini'
        log_print(f"🤖 [INIT] Initializing QuestionExtractor with {self.ai_provider.upper()} AI provider")
        # Set environment variables to force UTF-8 encoding
        import os
        import locale

        # Force UTF-8 encoding in environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LC_ALL'] = 'C.UTF-8'
        os.environ['LANG'] = 'C.UTF-8'

        # Try to set locale to UTF-8
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            except locale.Error:
                pass  # Use system default

        # Initialize AI clients based on provider
        self.client = None
        self.gemini_model = None

        if self.ai_provider == 'gemini':
            self._initialize_gemini()
        else:
            self._initialize_mistral(api_key_file)

    def _initialize_mistral(self, api_key_file):
        """Initialize Mistral AI client"""
        self.api_key = self._load_api_key(api_key_file)

        # Initialize Mistral client with explicit encoding handling
        try:
            # Debug: Check system environment for non-ASCII characters
            import getpass
            import socket

            username = getpass.getuser()
            hostname = socket.gethostname()

            log_print(f"🔍 [DEBUG] System username: {repr(username)}")
            log_print(f"🔍 [DEBUG] System hostname: {repr(hostname)}")

            # Check if username or hostname contain non-ASCII characters
            try:
                username.encode('ascii')
                log_print("✅ [DEBUG] Username is ASCII-safe")
            except UnicodeEncodeError:
                log_print("⚠️ [DEBUG] Username contains non-ASCII characters")

            try:
                hostname.encode('ascii')
                log_print("✅ [DEBUG] Hostname is ASCII-safe")
            except UnicodeEncodeError:
                log_print("⚠️ [DEBUG] Hostname contains non-ASCII characters")

            # Create client with clean environment and custom HTTP client
            import httpx

            # Create a custom HTTP client with UTF-8 encoding and extended timeouts for large files
            custom_client = httpx.Client(
                headers={
                    'User-Agent': 'MistralAI-Python-Client/1.0',  # ASCII-safe user agent
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                timeout=httpx.Timeout(
                    connect=30.0,    # Connection timeout
                    read=300.0,      # Read timeout (5 minutes for large files)
                    write=300.0,     # Write timeout (5 minutes for large files)
                    pool=30.0        # Pool timeout
                )
            )

            # Initialize Mistral client with custom HTTP client
            self.client = Mistral(
                api_key=self.api_key,
                client=custom_client
            )
        except Exception as e:
            # Handle any encoding issues during client initialization
            error_msg = str(e)
            try:
                error_msg.encode('ascii')
            except UnicodeEncodeError:
                error_msg = error_msg.encode('ascii', 'ignore').decode('ascii')
            raise Exception(f"Failed to initialize Mistral client: {error_msg}")

        log_print("✅ [INIT] Mistral AI client initialized successfully")

    def _initialize_gemini(self):
        """Initialize Gemini AI client"""
        try:
            # For Gemini, try environment variable first, then fallback to file
            gemini_api_key = os.getenv('GEMINI_API_KEY')

            # If environment variable not set, try to read from gemini_key.txt file
            if not gemini_api_key:
                try:
                    with open('gemini_key.txt', 'r', encoding='utf-8') as f:
                        gemini_api_key = f.read().strip()
                    log_print("📁 [INIT] Gemini API key loaded from gemini_key.txt file")
                except FileNotFoundError:
                    pass

            if not gemini_api_key:
                raise Exception("GEMINI_API_KEY environment variable not set and gemini_key.txt file not found")

            genai.configure(api_key=gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
            log_print("✅ [INIT] Gemini AI client initialized successfully")
        except Exception as e:
            error_msg = str(e)
            try:
                error_msg.encode('ascii')
            except UnicodeEncodeError:
                error_msg = error_msg.encode('ascii', 'ignore').decode('ascii')
            raise Exception(f"Failed to initialize Gemini client: {error_msg}")

    def _load_api_key(self, api_key_file):
        """Load API key from file with proper encoding handling"""
        try:
            # Try UTF-8 first
            try:
                with open(api_key_file, 'r', encoding='utf-8') as f:
                    api_key = f.read().strip()
            except UnicodeDecodeError:
                # If UTF-8 fails, try UTF-16 (which might have been used by echo command)
                with open(api_key_file, 'r', encoding='utf-16') as f:
                    api_key = f.read().strip()

            # Remove any null bytes that might have been introduced
            api_key = api_key.replace('\x00', '')

            # Ensure the API key is ASCII-safe
            try:
                api_key.encode('ascii')
                log_print(f"✅ [DEBUG] API key loaded successfully: {len(api_key)} characters")
                return api_key
            except UnicodeEncodeError:
                # If API key contains non-ASCII characters, that's a problem
                log_print(f"❌ [DEBUG] API key contains non-ASCII characters")
                raise Exception("API key contains invalid characters")
        except FileNotFoundError:
            raise FileNotFoundError(f"API key file '{api_key_file}' not found")
    
    def encode_pdf_to_base64(self, pdf_path):
        """Encode PDF file to base64 string with memory optimization for large files"""
        try:
            file_size = os.path.getsize(pdf_path)
            log_print(f"📄 [ENCODING] Processing PDF file: {file_size // (1024 * 1024)}MB")

            # For very large files (>50MB), process in chunks to avoid memory issues
            if file_size > 50 * 1024 * 1024:
                log_print("🔄 [ENCODING] Large file detected, using chunked encoding...")

                # Read file in chunks and encode
                chunk_size = 1024 * 1024  # 1MB chunks
                encoded_chunks = []

                with open(pdf_path, "rb") as pdf_file:
                    while True:
                        chunk = pdf_file.read(chunk_size)
                        if not chunk:
                            break
                        encoded_chunks.append(base64.b64encode(chunk).decode('utf-8'))

                        # Log progress for very large files
                        if len(encoded_chunks) % 10 == 0:
                            processed_mb = len(encoded_chunks) * chunk_size // (1024 * 1024)
                            log_print(f"📊 [ENCODING_PROGRESS] Encoded {processed_mb}MB...")

                return ''.join(encoded_chunks)
            else:
                # For smaller files, use the original method
                with open(pdf_path, "rb") as pdf_file:
                    return base64.b64encode(pdf_file.read()).decode('utf-8')

        except FileNotFoundError:
            raise FileNotFoundError(f"PDF file '{pdf_path}' not found")
        except MemoryError:
            raise Exception(f"File too large to process in memory. Consider splitting the PDF into smaller files.")
        except Exception as e:
            raise Exception(f"Error encoding PDF: {e}")

    def extract_ocr_data_from_pdf(self, pdf_path):
        """
        Extract OCR data (text and images) from PDF once for reuse.
        This method eliminates redundant OCR processing.

        Args:
            pdf_path (str): Path to the PDF file

        Returns:
            dict: Dictionary containing 'full_text' and 'all_images'
        """
        try:
            # Check if file exists
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"PDF file '{pdf_path}' not found")

            # Get safe filename for logging (handle encoding issues)
            safe_filename = os.path.basename(pdf_path)
            try:
                safe_filename.encode('ascii')
            except UnicodeEncodeError:
                safe_filename = safe_filename.encode('ascii', 'ignore').decode('ascii')
                if not safe_filename:
                    safe_filename = "uploaded_file.pdf"

            log_print(f"🔍 [OCR_START] Performing OCR on PDF: {safe_filename}")
            ocr_start_time = time.time()

            # Encode PDF to base64
            log_print("📄 [ENCODING_START] Encoding PDF to base64...")
            encoding_start = time.time()
            base64_pdf = self.encode_pdf_to_base64(pdf_path)
            encoding_duration = time.time() - encoding_start
            log_print(f"✅ [ENCODING_COMPLETE] PDF encoded in {encoding_duration:.2f}s")

            # Process with OCR to get text content
            # Note: OCR functionality requires Mistral AI, so we'll use Mistral for OCR regardless of main AI provider
            log_print("🤖 [OCR_API_START] Calling Mistral OCR API (required for PDF text extraction)...")
            log_print(f"🔍 [DEBUG] PDF path: {pdf_path}")
            log_print(f"🔍 [DEBUG] PDF path encoding check...")

            # Debug: Check if path contains non-ASCII characters
            try:
                pdf_path.encode('ascii')
                log_print("✅ [DEBUG] PDF path is ASCII-safe")
            except UnicodeEncodeError as e:
                log_print(f"⚠️ [DEBUG] PDF path contains non-ASCII characters: {e}")

            ocr_api_start = time.time()

            # Ensure we have a Mistral client for OCR (even if main AI provider is Gemini)
            if not hasattr(self, 'client') or self.client is None:
                log_print("⚠️ [OCR_FALLBACK] No Mistral client available, initializing for OCR...")
                self._initialize_mistral('key.txt')

            # Try to call OCR with better error handling
            try:
                ocr_response = self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document={
                        "type": "document_url",
                        "document_url": f"data:application/pdf;base64,{base64_pdf}"
                    },
                    include_image_base64=True
                )
            except UnicodeEncodeError as e:
                log_print(f"❌ [DEBUG] Unicode encoding error in OCR call: {e}")
                # Try to identify what's causing the encoding issue
                log_print(f"🔍 [DEBUG] Error details: {repr(e)}")
                raise e
            except Exception as e:
                log_print(f"❌ [DEBUG] General error in OCR call: {e}")
                log_print(f"🔍 [DEBUG] Error type: {type(e)}")
                raise e
            ocr_api_duration = time.time() - ocr_api_start
            log_print(f"✅ [OCR_API_COMPLETE] OCR API responded in {ocr_api_duration:.2f}s")

            # Extract text and images from OCR response
            print("🔄 [OCR_PARSING_START] Parsing OCR response...")
            parsing_start = time.time()
            full_text = ""
            all_images = {}  # Dictionary to store all images by ID

            if hasattr(ocr_response, 'pages') and ocr_response.pages:
                total_pages = len(ocr_response.pages)
                print(f"📄 [OCR_PARSING_PROGRESS] Processing {total_pages} pages...")

                # For large documents, use more frequent progress updates
                progress_interval = 5 if total_pages <= 50 else max(1, total_pages // 20)

                # Combine text from all pages and collect images
                for page_idx, page in enumerate(ocr_response.pages):
                    # Extract text (try both markdown and text attributes)
                    if hasattr(page, 'markdown') and page.markdown:
                        full_text += page.markdown + "\n"
                    elif hasattr(page, 'text') and page.text:
                        full_text += page.text + "\n"

                    # Collect images from this page
                    if hasattr(page, 'images') and page.images:
                        for image in page.images:
                            # Handle different image attribute formats
                            if hasattr(image, 'id') and hasattr(image, 'image_base64'):
                                all_images[image.id] = image.image_base64
                            elif hasattr(image, 'id') and hasattr(image, 'base64'):
                                all_images[image.id] = f"data:image/jpeg;base64,{image.base64}"

                    # Dynamic progress reporting based on document size
                    if (page_idx + 1) % progress_interval == 0 or page_idx + 1 == total_pages:
                        progress_pct = ((page_idx + 1) / total_pages) * 100
                        print(f"📄 [OCR_PARSING_PROGRESS] Processed {page_idx + 1}/{total_pages} pages ({progress_pct:.1f}%)...")

                        # Memory management for very large documents
                        if total_pages > 100 and (page_idx + 1) % 50 == 0:
                            import gc
                            gc.collect()  # Force garbage collection to free memory
                            print(f"🧹 [MEMORY_MANAGEMENT] Garbage collection performed at page {page_idx + 1}")
            else:
                full_text = str(ocr_response)

            parsing_duration = time.time() - parsing_start
            ocr_total_duration = time.time() - ocr_start_time
            print(f"✅ [OCR_PARSING_COMPLETE] Parsing completed in {parsing_duration:.2f}s")
            print(f"📊 [OCR_RESULTS] Extracted {len(full_text):,} characters, {len(all_images)} images")
            if all_images:
                print(f"🖼️ [OCR_IMAGES] Available image IDs: {list(all_images.keys())}")
            print(f"🎯 [OCR_COMPLETE] Total OCR processing time: {ocr_total_duration:.2f}s")

            return {
                'full_text': full_text,
                'all_images': all_images
            }

        except Exception as e:
            # Handle encoding errors in error messages
            error_msg = str(e)
            try:
                error_msg.encode('ascii')
            except UnicodeEncodeError:
                error_msg = error_msg.encode('ascii', 'ignore').decode('ascii')
            raise Exception(f"Error extracting OCR data from PDF: {error_msg}")
    
    def extract_structured_questions_from_pdf(self, pdf_path, ocr_data=None):
        """
        Extract questions from structured PDFs that have:
        1. Explicit questions with numbered format (1., 2., 3., etc.)
        2. Multiple choice options (a), b), c), d)
        3. Answer key section with format like "1) b  2) a  3) a"
        4. Hints and solutions section

        This method extracts actual questions and maps answers from the answer key.
        """
        try:
            # Use provided OCR data or extract it from PDF
            if ocr_data:
                print("📄 Using pre-processed OCR data")
                full_text = ocr_data['full_text']
                all_images = ocr_data['all_images']
            else:
                print("📄 Extracting OCR data from PDF")
                ocr_result = self.extract_ocr_data_from_pdf(pdf_path)
                full_text = ocr_result['full_text']
                all_images = ocr_result['all_images']

            # Store images for later use in both formats for compatibility
            self._current_images = all_images
            self.current_ocr_data = {'all_images': all_images, 'full_text': full_text}
            print(f"🔍 [DEBUG] Stored {len(all_images)} images for processing")

            text_length = len(full_text)
            log_print(f"🔍 [STRUCTURED_EXTRACTION] Text length: {text_length:,} characters")

            # Analyze the structured format
            log_print("🔍 [STRUCTURE_ANALYSIS] Analyzing PDF structure...")

            # Look for answer key patterns
            import re
            answer_patterns = re.findall(r'(\d+)\)\s*([abcdABCD])', full_text)
            log_print(f"📊 [STRUCTURE_ANALYSIS] Found {len(answer_patterns)} answer key entries")

            # Look for solution patterns
            solution_patterns = re.findall(r'(\d+)\s*\([abcdABCD]\)', full_text)
            log_print(f"📊 [STRUCTURE_ANALYSIS] Found {len(solution_patterns)} solution entries")

            # Estimate total questions
            estimated_questions = max(len(answer_patterns), len(solution_patterns))
            if estimated_questions == 0:
                # Fallback to looking for numbered items
                numbered_items = re.findall(r'\b(\d+)\b', full_text)
                if numbered_items:
                    estimated_questions = max([int(x) for x in numbered_items if x.isdigit() and int(x) <= 500])

            log_print(f"📊 [STRUCTURE_ANALYSIS] Estimated questions: {estimated_questions}")

            # Use specialized extraction for structured format
            if estimated_questions > 20:  # Likely a structured answer sheet
                log_print("🔄 [STRUCTURED_EXTRACTION] Using structured extraction method...")
                return self._extract_from_structured_format(full_text, estimated_questions, ocr_data)
            else:
                log_print("🔄 [FALLBACK_EXTRACTION] Using standard extraction method...")
                return self.extract_questions_from_pdf_standard(pdf_path, ocr_data)

        except Exception as e:
            log_print(f"❌ [STRUCTURED_EXTRACTION_ERROR] Error: {e}")
            # Fallback to standard extraction
            return self.extract_questions_from_pdf_standard(pdf_path, ocr_data)

    def extract_questions_from_pdf_standard(self, pdf_path, ocr_data=None):
        """
        Standard extraction method for regular PDFs with questions and options.
        """
        try:
            # Use provided OCR data or extract it from PDF
            if ocr_data:
                print("📄 Using pre-processed OCR data")
                full_text = ocr_data['full_text']
                all_images = ocr_data['all_images']
            else:
                print("📄 Extracting OCR data from PDF")
                ocr_result = self.extract_ocr_data_from_pdf(pdf_path)
                full_text = ocr_result['full_text']
                all_images = ocr_result['all_images']

            # Store images for later use in parsing
            self._current_images = all_images
            print(f"🔍 [DEBUG] Stored {len(all_images)} images for processing: {list(all_images.keys()) if all_images else 'None'}")

            # Note: Answer extraction is handled by the AI model based on answer key sections
            # found in the PDF text during processing

            # Create a more flexible and robust prompt for extracting questions
            extraction_prompt = f"""
            You are an expert at extracting educational content from documents. Analyze the following text and extract ALL multiple-choice questions you can find, regardless of formatting variations.

            IMPORTANT: Be FLEXIBLE and ADAPTIVE. Extract questions even if they don't follow perfect formatting.

            Return a valid JSON array where each question follows this structure:

            {{
              "question": "Complete question text (preserve any image references like ![img-X.jpeg](img-X.jpeg))",
              "imageUrl": {{
                "img-0.jpeg": "",
                "img-1.jpeg": ""
              }},
              "options": {{
                "A": "Option A text OR IMAGE_PLACEHOLDER_img-X.jpeg if option contains image",
                "B": "Option B text OR IMAGE_PLACEHOLDER_img-Y.jpeg if option contains image",
                "C": "Option C text",
                "D": "Option D text"
              }},
              "answer": "A",
              "solution": {{
                "steps": ["Step 1", "Step 2", "etc"],
                "methodology": "Solution approach if available",
                "key_concepts": ["concept1", "concept2"],
                "final_explanation": "Final explanation if available"
              }},
              "hints": ["Hint 1", "Hint 2"]
            }}

            IMPORTANT: Only include "imageUrl" field if question actually contains image references!

            EXTRACTION GUIDELINES:

            QUESTIONS:
            - Find ALL multiple-choice questions (look for patterns like "1.", "Q1:", "Question 1", etc.)
            - Accept various option formats: (a), a), A., A:, etc.
            - Extract questions even if formatting is inconsistent
            - Preserve image references like ![img-X.jpeg](img-X.jpeg) in question text
            - Clean up obvious OCR errors but keep the meaning

            IMAGE HANDLING FOR QUESTIONS:
            - ONLY add "imageUrl" field if the question text actually contains image references like ![img-0.jpeg](img-0.jpeg)
            - Do NOT add "imageUrl" if there are no image references in the question text
            - Do NOT use placeholder text like "base64_image_data_here" - the actual image data will be added later
            - If question has image references, use this format: "imageUrl": {{"img-0.jpeg": "", "img-1.jpeg": ""}}
            - Keep the image reference in question text as-is: "What is shown in ![img-0.jpeg](img-0.jpeg)?"
            - The empty strings will be filled with actual base64 data during post-processing

            IMAGE HANDLING FOR OPTIONS:
            - If an option contains an image reference (like ![img-2.jpeg](img-2.jpeg)), replace the ENTIRE option value with placeholder
            - Use this format for image options: "A": "IMAGE_PLACEHOLDER_img-2.jpeg"
            - Do NOT use actual base64 data in the AI response - use the placeholder format
            - Text-only options remain as text: "A": "Option text here"
            - The placeholders will be replaced with actual base64 data during post-processing

            ANSWERS:
            - Look for answer keys anywhere in the document
            - Accept formats like "1. A", "1) A", "Q1: A", "Answer: A", etc.
            - If no answer found, use empty string ""

            SOLUTIONS & HINTS:
            - Extract any available solution steps or explanations
            - Look for worked examples, explanations, or methodology
            - If no solutions/hints found, use empty arrays []
            - Don't worry if solutions are incomplete

            OUTPUT FORMAT:
            - Return ONLY a valid JSON array starting with [ and ending with ]
            - NO markdown formatting, NO explanations, NO extra text
            - If you find questions but some fields are missing, still include the question with empty values
            - ALWAYS return at least [] even if no questions found

            FLEXIBILITY IS KEY:
            - Adapt to different question numbering systems
            - Handle various option labeling (a/b/c/d, A/B/C/D, 1/2/3/4, etc.)
            - Extract questions even if they don't follow standard academic format
            - Focus on finding content rather than perfect formatting

            EXAMPLE PATTERNS TO LOOK FOR:
            - "1. What is...?" followed by options
            - "Question 1:" followed by options
            - "Q1." followed by options
            - Any text ending with "?" followed by multiple choice options

            Here is the text to analyze:

            {full_text}
            """

            # Add debugging information and memory optimization for large texts
            text_length = len(full_text)
            log_print(f"🔍 [DEBUG] Text length: {text_length:,} characters")

            # Debug: Show a sample of the extracted text to verify OCR quality
            log_print("📄 [DEBUG] Sample of extracted text (first 1000 chars):")
            log_print(f"{full_text[:1000]}...")
            log_print("📄 [DEBUG] Sample of extracted text (last 1000 chars):")
            log_print(f"...{full_text[-1000:]}")

            # Enhanced text analysis for better question detection
            log_print("🔍 [TEXT_ANALYSIS] Analyzing text patterns for question detection...")
            question_indicators = ['?', 'Q.', 'Question', 'Ques', '1.', '2.', '3.', 'a)', 'b)', 'c)', 'd)', 'A)', 'B)', 'C)', 'D)']
            found_indicators = [indicator for indicator in question_indicators if indicator in full_text]
            log_print(f"📊 [TEXT_ANALYSIS] Found question indicators: {found_indicators}")

            # Count potential questions using simple pattern matching
            import re
            question_patterns = [
                r'\d+\.\s*[A-Z]',  # "1. What is..."
                r'Q\d+[:\.]',      # "Q1:" or "Q1."
                r'Question\s*\d+', # "Question 1"
                r'\?\s*\n',        # Question marks followed by newline
            ]

            total_potential_questions = 0
            for pattern in question_patterns:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                total_potential_questions += len(matches)
                if matches:
                    log_print(f"📊 [PATTERN_ANALYSIS] Pattern '{pattern}' found {len(matches)} matches")

            log_print(f"📊 [TEXT_ANALYSIS] Estimated potential questions in text: {total_potential_questions}")

            if total_potential_questions < 10:
                log_print("⚠️ [TEXT_ANALYSIS] Very few question patterns detected - OCR quality might be poor")
                log_print("🔄 [TEXT_ANALYSIS] Consider checking PDF quality or trying different extraction settings")

            # For large texts, implement chunking strategy (lowered threshold for better extraction)
            if text_length > 200000:  # 200KB - lowered threshold for better extraction
                log_print("⚠️ [LARGE_TEXT] Large text detected. Using enhanced chunking strategy for optimal processing.")
                log_print(f"📊 [LARGE_TEXT] Text size: {text_length / 1024:.1f}KB - Processing in chunks...")
                log_print("🔄 [CHUNKING] Using enhanced chunking strategy for better question extraction...")
                return self._process_large_text_in_chunks_enhanced(full_text, extraction_prompt)

            # For smaller texts, continue with normal processing
            log_print(f"📊 [NORMAL_PROCESSING] Text size: {text_length / 1024:.1f}KB - Processing normally...")

            # Look for common question patterns in the text (expanded patterns)
            import re
            question_patterns = [
                r'\d+\.\s*[A-Z]',  # "1. What"
                r'Question\s*\d+',  # "Question 1"
                r'Q\d+[\.\:]',      # "Q1." or "Q1:"
                r'\?\s*\n\s*[a-d][\)\.]',  # "? \n a)"
                r'[A-D][\)\.]',     # "A)" or "A."
                r'\d+\)\s*[A-Z]',   # "1) What"
                r'\(\d+\)\s*[A-Z]', # "(1) What"
                r'\d+\s*[A-Z][a-z]+.*\?',  # "1 What is...?"
                r'[a-d][\)\.].*\n',  # Options like "a) option"
                r'[A-D][\)\.].*\n',  # Options like "A) option"
                r'\?\s*[a-d][\)\.]', # "? a)"
                r'MCQ|Multiple Choice|Choose.*correct', # MCQ indicators
                r'Select.*correct|Mark.*correct', # Selection indicators
            ]

            pattern_matches = {}
            for i, pattern in enumerate(question_patterns):
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                pattern_matches[f"Pattern_{i+1}"] = len(matches)
                if matches:
                    log_print(f"🔍 [DEBUG] Found {len(matches)} matches for pattern '{pattern}': {matches[:3]}...")

            if not any(pattern_matches.values()):
                log_print("⚠️ [DEBUG] No common question patterns detected in text")
                log_print(f"🔍 [DEBUG] Text preview: {full_text[:500]}...")
                log_print("🔄 [DEBUG] Trying more aggressive pattern matching...")

                # Try more aggressive patterns for poorly formatted text
                aggressive_patterns = [
                    r'\d+',  # Any number (might be question numbers)
                    r'\?',   # Question marks
                    r'[a-d]\)',  # Lowercase options
                    r'[A-D]\)',  # Uppercase options
                    r'correct|answer|choose|select',  # Common question words
                ]

                for pattern in aggressive_patterns:
                    matches = re.findall(pattern, full_text, re.IGNORECASE)
                    if matches:
                        log_print(f"🔍 [DEBUG] Found {len(matches)} matches for aggressive pattern '{pattern}'")

            else:
                log_print(f"✅ [DEBUG] Question patterns detected: {pattern_matches}")

            # Use the selected AI provider for text analysis
            if self.ai_provider == 'gemini':
                result = self._extract_with_gemini(extraction_prompt)
            else:
                result = self._extract_with_mistral(extraction_prompt)

            # Check if we got meaningful results
            try:
                import json
                # Clean the result before parsing
                cleaned_result = self._clean_json_response(result)
                parsed_result = json.loads(cleaned_result)
                if isinstance(parsed_result, list) and len(parsed_result) > 0:
                    log_print(f"✅ [SUCCESS] Extracted {len(parsed_result)} questions successfully")
                    return result
                else:
                    log_print("⚠️ [WARNING] No questions extracted, trying aggressive fallback approach...")
                    return self._try_aggressive_extraction(full_text)
            except Exception as e:
                log_print(f"⚠️ [WARNING] Invalid JSON response ({e}), trying aggressive fallback approach...")
                return self._try_aggressive_extraction(full_text)

        except Exception as e:
            raise Exception(f"Error extracting questions from PDF: {e}")

    def _try_aggressive_extraction(self, full_text):
        """
        Aggressive extraction method that tries to find ANY content that might be questions.
        This is used when the normal extraction fails.
        """
        log_print("🔄 [AGGRESSIVE_EXTRACTION] Attempting very aggressive question extraction...")

        aggressive_prompt = f"""
        URGENT: Extract ANY content that could possibly be multiple-choice questions from this text.

        BE EXTREMELY FLEXIBLE AND CREATIVE:
        - Look for ANY numbered items followed by text
        - Look for ANY text ending with question marks
        - Look for ANY lists with options (even if not labeled a,b,c,d)
        - Look for ANY educational content that could be turned into questions
        - If you find incomplete questions, try to reconstruct them
        - If options are missing, create reasonable options based on the content
        - If answers are missing, make educated guesses

        EXTRACT EVERYTHING POSSIBLE - even if it's not perfectly formatted.

        Return format (JSON array):
        [
          {{
            "content": "Any question-like text you find",
            "options": {{
              "A": "First option or best guess",
              "B": "Second option or best guess",
              "C": "Third option or best guess",
              "D": "Fourth option or best guess"
            }},
            "answer": "A",
            "type": "mcq",
            "difficulty": "medium"
          }}
        ]

        IMPORTANT: Extract EVERYTHING that could possibly be a question. Don't be picky about format.
        If you find 350 potential questions, extract all 350. Better to have false positives than miss real questions.

        Text to analyze:
        {full_text}
        """

        # Use the same AI provider for aggressive extraction
        if self.ai_provider == 'gemini':
            return self._extract_with_gemini(aggressive_prompt)
        else:
            return self._extract_with_mistral(aggressive_prompt)

    def _process_large_text_in_chunks_enhanced(self, full_text, base_prompt):
        """
        Enhanced chunking strategy that addresses the issue of missing questions.
        This method uses multiple approaches to ensure maximum question extraction.
        """
        try:
            log_print("🔄 [ENHANCED_CHUNKING] Starting enhanced chunked processing for large text...")

            text_length = len(full_text)

            # Use smaller, overlapping chunks to ensure no questions are missed
            if text_length > 10000000:  # 10MB+
                chunk_size = 150000  # 150KB per chunk for very large files
                overlap_size = 75000  # 75KB overlap (50% overlap)
            elif text_length > 5000000:  # 5MB+
                chunk_size = 200000  # 200KB per chunk for large files
                overlap_size = 100000  # 100KB overlap (50% overlap)
            elif text_length > 2000000:  # 2MB+
                chunk_size = 250000  # 250KB per chunk
                overlap_size = 125000  # 125KB overlap (50% overlap)
            else:
                chunk_size = 300000  # 300KB per chunk for moderately large files
                overlap_size = 150000  # 150KB overlap (50% overlap)

            total_chunks = max(1, (text_length + chunk_size - overlap_size - 1) // (chunk_size - overlap_size))

            log_print(f"📊 [ENHANCED_CHUNKING] Processing {text_length:,} characters in {total_chunks} chunks")
            log_print(f"📊 [ENHANCED_CHUNKING] Chunk size: {chunk_size:,} chars, Overlap: {overlap_size:,} chars")

            all_questions = []
            processed_chunks = 0
            failed_chunks = 0
            total_processing_time = 0

            # Process chunks with enhanced overlap
            for i in range(0, text_length, chunk_size - overlap_size):
                chunk_start = i
                chunk_end = min(i + chunk_size, text_length)
                chunk_text = full_text[chunk_start:chunk_end]

                processed_chunks += 1
                log_print(f"🔄 [ENHANCED_CHUNK_{processed_chunks}] Processing chunk {processed_chunks}/{total_chunks} ({chunk_start:,}-{chunk_end:,})")

                # Enhanced prompt for better extraction
                chunk_prompt = f"""
                You are an expert at extracting educational content from documents. This is chunk {processed_chunks} of {total_chunks} from a large document.

                CRITICAL: Extract EVERY SINGLE multiple-choice question you can find in this text chunk, even if they seem incomplete or poorly formatted.

                IMPORTANT INSTRUCTIONS:
                1. Look for ANY numbered items that could be questions (1., Q1:, Question 1, etc.)
                2. Look for ANY text ending with question marks followed by options
                3. Extract questions even if options are not perfectly labeled (a,b,c,d)
                4. If you find partial questions at the beginning or end, still extract them
                5. Be extremely liberal in what you consider a "question"
                6. If answer keys are present, use them; otherwise make educated guesses

                Return format - JSON array only:
                [
                  {{
                    "question": "Complete question text",
                    "options": {{
                      "A": "Option A text",
                      "B": "Option B text",
                      "C": "Option C text",
                      "D": "Option D text"
                    }},
                    "answer": "A",
                    "type": "mcq",
                    "difficulty": "medium"
                  }}
                ]

                EXTRACT EVERYTHING - Better to have false positives than miss real questions!

                Text chunk to analyze:

                {chunk_text}
                """

                # Process this chunk with retry logic
                chunk_questions = self._process_chunk_with_retry(chunk_prompt, processed_chunks, total_chunks)
                if chunk_questions:
                    all_questions.extend(chunk_questions)
                    log_print(f"✅ [ENHANCED_CHUNK_{processed_chunks}] Extracted {len(chunk_questions)} questions")
                    log_print(f"📊 [PROGRESS] Total questions so far: {len(all_questions)}")
                else:
                    failed_chunks += 1
                    log_print(f"⚠️ [ENHANCED_CHUNK_{processed_chunks}] No questions found in this chunk")

                # Memory management
                if processed_chunks % 3 == 0:
                    import gc
                    gc.collect()
                    log_print(f"🧹 [MEMORY_MANAGEMENT] Garbage collection performed after chunk {processed_chunks}")

            # Apply less aggressive deduplication
            unique_questions = self._remove_duplicate_questions_enhanced(all_questions)

            log_print(f"✅ [ENHANCED_CHUNKING_COMPLETE] Processing summary:")
            log_print(f"📊 [ENHANCED_CHUNKING_COMPLETE] Total chunks processed: {processed_chunks}")
            log_print(f"📊 [ENHANCED_CHUNKING_COMPLETE] Failed chunks: {failed_chunks}")
            log_print(f"✅ [ENHANCED_CHUNKING_COMPLETE] Total questions extracted: {len(all_questions)}")
            log_print(f"✅ [ENHANCED_CHUNKING_COMPLETE] Unique questions after deduplication: {len(unique_questions)}")

            # If still getting very few questions, try different approaches
            if len(unique_questions) < 20:
                log_print("⚠️ [ENHANCED_FALLBACK] Very few questions extracted, trying multiple fallback approaches...")
                fallback_questions = self._try_multiple_extraction_approaches(full_text)
                if len(fallback_questions) > len(unique_questions):
                    log_print(f"✅ [ENHANCED_FALLBACK] Fallback approaches found {len(fallback_questions)} questions!")
                    unique_questions = fallback_questions

            # Return as JSON string
            import json
            return json.dumps(unique_questions, ensure_ascii=False, indent=2)

        except Exception as e:
            log_print(f"❌ [ENHANCED_CHUNKING_ERROR] Error in enhanced chunked processing: {e}")
            # Fallback to original chunking method
            log_print("🔄 [ENHANCED_FALLBACK] Falling back to original chunking method...")
            return self._process_large_text_in_chunks(full_text, base_prompt)

    def _process_large_text_in_chunks(self, full_text, base_prompt):
        """
        Process very large text by splitting it into chunks and combining results.
        This prevents truncation and ensures all questions are extracted.

        Args:
            full_text (str): The full text to process
            base_prompt (str): The base extraction prompt template

        Returns:
            str: Combined JSON response from all chunks
        """
        try:
            log_print("🔄 [CHUNKING_START] Starting chunked processing for large text...")

            # Calculate optimal chunk size based on text length
            # For very large texts, use smaller chunks to avoid API limits
            if text_length > 5000000:  # 5MB+
                chunk_size = 200000  # 200KB per chunk for very large files
                overlap_size = 40000  # 40KB overlap
            elif text_length > 2000000:  # 2MB+
                chunk_size = 250000  # 250KB per chunk for large files
                overlap_size = 45000  # 45KB overlap
            else:
                chunk_size = 300000  # 300KB per chunk for moderately large files
                overlap_size = 50000  # 50KB overlap to avoid cutting questions in half

            text_length = len(full_text)
            total_chunks = (text_length + chunk_size - overlap_size - 1) // (chunk_size - overlap_size)

            log_print(f"📊 [CHUNKING_INFO] Processing {text_length:,} characters in {total_chunks} chunks")
            log_print(f"📊 [CHUNKING_INFO] Chunk size: {chunk_size:,} chars, Overlap: {overlap_size:,} chars")

            all_questions = []
            processed_chunks = 0
            failed_chunks = 0
            total_processing_time = 0

            for i in range(0, text_length, chunk_size - overlap_size):
                chunk_start = i
                chunk_end = min(i + chunk_size, text_length)
                chunk_text = full_text[chunk_start:chunk_end]

                processed_chunks += 1
                log_print(f"🔄 [CHUNK_{processed_chunks}] Processing chunk {processed_chunks}/{total_chunks} ({chunk_start:,}-{chunk_end:,})")

                # Create chunk-specific prompt
                chunk_prompt = f"""
                You are an expert at extracting educational content from documents. Analyze the following text chunk and extract ALL multiple-choice questions you can find, regardless of formatting variations.

                IMPORTANT: Be FLEXIBLE and ADAPTIVE. Extract questions even if they don't follow perfect formatting.

                CRITICAL REQUIREMENTS:
                1. Extract EVERY question you find, even if incomplete or poorly formatted
                2. For each question, provide:
                   - "content": The question text (clean and readable)
                   - "options": Array of exactly 4 options (a, b, c, d)
                   - "answer": The correct answer letter (a, b, c, or d)
                   - "type": Always "mcq" for multiple choice
                   - "difficulty": "easy", "medium", or "hard" (best guess)

                3. If you find answer keys or answer sections, use them to determine correct answers
                4. If no answer key is found, make your best educated guess for the correct answer
                5. If options are incomplete, create reasonable options based on the question context
                6. Clean up any formatting issues, OCR errors, or garbled text

                FLEXIBILITY IS KEY:
                - Adapt to different question numbering systems
                - Handle various option labeling (a/b/c/d, A/B/C/D, 1/2/3/4, etc.)
                - Extract questions even if they don't follow standard academic format
                - Focus on finding content rather than perfect formatting

                Return ONLY a JSON array of questions. No other text or explanations.

                Text chunk to analyze:

                {chunk_text}
                """

                # Process this chunk
                try:
                    chunk_start_time = time.time()

                    if self.ai_provider == 'gemini':
                        chunk_result = self._extract_with_gemini(chunk_prompt)
                    else:
                        chunk_result = self._extract_with_mistral(chunk_prompt)

                    chunk_processing_time = time.time() - chunk_start_time
                    total_processing_time += chunk_processing_time

                    # Parse and validate chunk result
                    import json
                    cleaned_result = self._clean_json_response(chunk_result)
                    chunk_questions = json.loads(cleaned_result)

                    if isinstance(chunk_questions, list) and len(chunk_questions) > 0:
                        all_questions.extend(chunk_questions)
                        log_print(f"✅ [CHUNK_{processed_chunks}] Extracted {len(chunk_questions)} questions in {chunk_processing_time:.1f}s")
                        log_print(f"📊 [PROGRESS] Total questions so far: {len(all_questions)}")
                    else:
                        log_print(f"⚠️ [CHUNK_{processed_chunks}] No questions found in this chunk ({chunk_processing_time:.1f}s)")

                    # Memory management - force garbage collection every 5 chunks
                    if processed_chunks % 5 == 0:
                        import gc
                        gc.collect()
                        log_print(f"🧹 [MEMORY_MANAGEMENT] Garbage collection performed after chunk {processed_chunks}")

                except Exception as e:
                    failed_chunks += 1
                    log_print(f"❌ [CHUNK_{processed_chunks}] Error processing chunk: {e}")
                    log_print(f"⚠️ [CHUNK_{processed_chunks}] Failed chunks so far: {failed_chunks}/{processed_chunks}")
                    continue

            # Remove duplicate questions based on content similarity
            unique_questions = self._remove_duplicate_questions(all_questions)

            log_print(f"✅ [CHUNKING_COMPLETE] Processing summary:")
            log_print(f"📊 [CHUNKING_COMPLETE] Total chunks processed: {processed_chunks}")
            log_print(f"📊 [CHUNKING_COMPLETE] Failed chunks: {failed_chunks}")
            if processed_chunks > 0:
                log_print(f"📊 [CHUNKING_COMPLETE] Success rate: {((processed_chunks - failed_chunks) / processed_chunks * 100):.1f}%")
                log_print(f"📊 [CHUNKING_COMPLETE] Average time per chunk: {total_processing_time / processed_chunks:.1f}s")
            log_print(f"📊 [CHUNKING_COMPLETE] Total processing time: {total_processing_time:.1f}s")
            log_print(f"✅ [CHUNKING_COMPLETE] Total questions extracted: {len(all_questions)}")
            log_print(f"✅ [CHUNKING_COMPLETE] Unique questions after deduplication: {len(unique_questions)}")

            # If we got very few questions, try aggressive extraction on the full text
            if len(unique_questions) < 10:
                log_print("⚠️ [CHUNKING_FALLBACK] Very few questions extracted, trying aggressive extraction on full text...")
                try:
                    aggressive_result = self._try_aggressive_extraction(full_text)
                    import json
                    aggressive_cleaned = self._clean_json_response(aggressive_result)
                    aggressive_questions = json.loads(aggressive_cleaned)

                    if isinstance(aggressive_questions, list) and len(aggressive_questions) > len(unique_questions):
                        log_print(f"✅ [CHUNKING_FALLBACK] Aggressive extraction found {len(aggressive_questions)} questions!")
                        return json.dumps(aggressive_questions, ensure_ascii=False, indent=2)
                except Exception as e:
                    log_print(f"❌ [CHUNKING_FALLBACK] Aggressive extraction failed: {e}")

            # Return as JSON string
            import json
            return json.dumps(unique_questions, ensure_ascii=False, indent=2)

        except Exception as e:
            log_print(f"❌ [CHUNKING_ERROR] Error in chunked processing: {e}")
            # Fallback to truncated processing
            log_print("🔄 [CHUNKING_FALLBACK] Falling back to truncated processing...")
            truncated_text = full_text[:400000] + "\n\n[TEXT_TRUNCATED_DUE_TO_SIZE]"
            return self._extract_with_gemini(base_prompt.replace(full_text, truncated_text)) if self.ai_provider == 'gemini' else self._extract_with_mistral(base_prompt.replace(full_text, truncated_text))

    def _remove_duplicate_questions(self, questions):
        """
        Remove duplicate questions based on content similarity.

        Args:
            questions (list): List of question dictionaries

        Returns:
            list: List of unique questions
        """
        if not questions:
            return []

        unique_questions = []
        seen_contents = set()

        for question in questions:
            if not isinstance(question, dict) or 'content' not in question:
                continue

            # Normalize content for comparison (remove extra spaces, convert to lowercase)
            normalized_content = ' '.join(question['content'].lower().split())

            # Check for similarity (exact match or very similar)
            is_duplicate = False
            for seen_content in seen_contents:
                # Simple similarity check - if 90% of words match, consider it duplicate
                seen_words = set(seen_content.split())
                current_words = set(normalized_content.split())

                if len(seen_words) > 0 and len(current_words) > 0:
                    intersection = len(seen_words.intersection(current_words))
                    union = len(seen_words.union(current_words))
                    similarity = intersection / union if union > 0 else 0

                    if similarity > 0.9:  # 90% similarity threshold
                        is_duplicate = True
                        break

            if not is_duplicate:
                unique_questions.append(question)
                seen_contents.add(normalized_content)

        return unique_questions

    def _extract_from_structured_format(self, full_text, estimated_questions, ocr_data=None):
        """
        Extract questions from structured format with explicit questions and answer keys.

        This handles PDFs that have:
        1. Explicit numbered questions (1., 2., 3., etc.)
        2. Multiple choice options (a), b), c), d)
        3. Answer key section: "1) b  2) a  3) a  4) d"
        4. Hints and solutions section with detailed explanations
        """
        try:
            log_print("🔄 [STRUCTURED_FORMAT] Processing structured PDF with explicit questions...")

            # Step 1: Extract explicit questions and options
            questions_data = self._extract_explicit_questions(full_text)
            log_print(f"📊 [QUESTIONS_EXTRACTED] Found {len(questions_data)} explicit questions")

            # Step 2: Extract answer key mapping
            answer_mapping = self._extract_answer_key_mapping(full_text)
            log_print(f"📊 [ANSWER_MAPPING] Found {len(answer_mapping)} answer mappings")

            # Step 3: Extract solutions and hints
            solutions_mapping = self._extract_solutions_mapping(full_text)
            log_print(f"📊 [SOLUTIONS_MAPPING] Found {len(solutions_mapping)} solution entries")

            # Step 4: Enhance with hybrid AI approach if needed
            if len(questions_data) < estimated_questions * 0.8:
                log_print("🔄 [HYBRID_AI] Using hybrid AI approach for better extraction...")
                enhanced_questions = self._enhance_with_hybrid_ai(
                    questions_data, answer_mapping, solutions_mapping, full_text
                )
                questions_data.extend(enhanced_questions)
                log_print(f"✅ [HYBRID_AI] Enhanced to {len(questions_data)} total questions")

            # Step 5: Combine questions with answers and solutions
            final_questions = self._combine_questions_with_answers(
                questions_data, answer_mapping, solutions_mapping
            )

            log_print(f"✅ [STRUCTURED_FORMAT] Combined {len(final_questions)} questions with answers and solutions")

            # Return as JSON string with proper formatting
            import json

            # Store OCR data for image processing
            self.current_ocr_data = ocr_data

            # Clean the questions before JSON serialization
            cleaned_questions = self._clean_questions_for_json(final_questions)

            return json.dumps(cleaned_questions, ensure_ascii=False, indent=2)

        except Exception as e:
            log_print(f"❌ [STRUCTURED_FORMAT_ERROR] Error in structured extraction: {e}")
            # Fallback to chunked processing
            return self._process_large_text_in_chunks_enhanced(full_text, "")

    def _extract_explicit_questions(self, full_text):
        """
        Extract explicit questions and options from the PDF text.

        Expected format:
        1. A resistor 30 Ω, inductor of reactance 10 Ω and capacitor...
           a) 10√2 A    b) 10 A    c) 30√11 A    d) 30/√11 A

        2. The natural frequency (ω₀) of oscillations in L - C circuit...
           a) 1/(2π√LC)    b) 1/(2π√LC)    c) 1/√LC    d) √LC
        """
        import re

        log_print("🔄 [EXPLICIT_QUESTIONS] Extracting explicit questions and options...")

        questions_data = []

        # Pattern to match numbered questions
        # Matches: "1." or "1 " followed by question text
        question_pattern = r'(\d+)\.\s+(.*?)(?=\d+\.\s+|$)'

        # Find all numbered questions
        question_matches = re.findall(question_pattern, full_text, re.DOTALL)

        log_print(f"📊 [QUESTION_MATCHES] Found {len(question_matches)} question patterns")

        for match in question_matches:
            question_num = int(match[0])
            question_block = match[1].strip()

            # Extract the question text and options
            question_data = self._parse_question_block(question_num, question_block)

            if question_data:
                questions_data.append(question_data)

        log_print(f"✅ [EXPLICIT_QUESTIONS] Extracted {len(questions_data)} complete questions")
        return questions_data

    def _parse_question_block(self, question_num, question_block):
        """
        Parse a single question block to extract question text and options.

        Expected format:
        Question text here...
        a) Option A text    b) Option B text    c) Option C text    d) Option D text
        """
        import re

        try:
            # Clean the block first - remove solution contamination
            cleaned_block = self._clean_question_block(question_block)

            # Split the block into lines
            lines = cleaned_block.split('\n')

            # Find the line with options (contains a), b), c), d))
            options_line_idx = -1
            for i, line in enumerate(lines):
                if re.search(r'[a-d]\)', line):
                    options_line_idx = i
                    break

            if options_line_idx == -1:
                log_print(f"⚠️ [PARSE_WARNING] No options found for question {question_num}")
                return None

            # Question text is everything before the options line
            question_text = ' '.join(lines[:options_line_idx]).strip()

            # Further clean the question text
            question_text = self._clean_question_text(question_text)

            # Options are from the options line onwards
            options_text = ' '.join(lines[options_line_idx:]).strip()

            # Parse options using regex
            options = self._parse_options(options_text)

            if not options or len(options) < 4:
                log_print(f"⚠️ [PARSE_WARNING] Incomplete options for question {question_num}")
                return None

            return {
                'number': question_num,
                'question': question_text,
                'options': options,
                'raw_block': question_block
            }

        except Exception as e:
            log_print(f"❌ [PARSE_ERROR] Error parsing question {question_num}: {e}")
            return None

    def _clean_question_block(self, block):
        """
        Clean question block to remove solution contamination.
        """
        import re

        lines = block.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # Skip lines that look like solutions or calculations
            if (re.match(r'^\d+\([a-d]\)', line) or  # Solution markers like "225(c)"
                line.startswith('f=') or
                line.startswith('\\therefore') or
                line.startswith('\\approx') or
                '=' in line and ('×' in line or '\\times' in line) or  # Calculation lines
                re.match(r'^\d+\s*Hz', line) or  # Result lines like "1600 Hz"
                re.match(r'^\d+\s*rad', line)):  # Result lines like "2000 rad/sec"
                continue

            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _clean_question_text(self, text):
        """
        Clean question text to remove any remaining solution contamination.
        """
        import re

        # Remove solution numbers at the beginning
        text = re.sub(r'^\d+\([a-d]\)\s*', '', text)

        # Remove calculation expressions that might have leaked in
        text = re.sub(r'f=.*?Hz', '', text)
        text = re.sub(r'\\therefore.*?$', '', text, flags=re.MULTILINE)
        text = re.sub(r'\\approx.*?$', '', text, flags=re.MULTILINE)

        # Clean up extra whitespace
        text = ' '.join(text.split())

        return text

    def _parse_options(self, options_text):
        """
        Parse options from text like:
        "a) 10√2 A    b) 10 A    c) 30√11 A    d) 30/√11 A"
        """
        import re

        options = {}

        # More robust pattern to match options
        # This handles various formats including mathematical symbols and multi-line options
        option_pattern = r'([a-d])\)\s*([^a-d\)]+?)(?=\s*[a-d]\)|$)'

        matches = re.findall(option_pattern, options_text, re.IGNORECASE | re.DOTALL)

        for match in matches:
            letter = match[0].upper()
            text = match[1].strip()

            # Clean up the text - remove extra whitespace and newlines
            text = ' '.join(text.split())

            if text and len(text) > 1:  # Only add meaningful options
                options[letter] = text

        # If we didn't get good matches, try a simpler approach
        if len(options) < 2:
            # Split by common patterns and try to extract
            parts = re.split(r'\s*[a-d]\)\s*', options_text, flags=re.IGNORECASE)
            if len(parts) > 1:
                letters = ['A', 'B', 'C', 'D']
                for i, part in enumerate(parts[1:5]):  # Skip first empty part, take next 4
                    if i < len(letters) and part.strip():
                        options[letters[i]] = part.strip()

        # Ensure we have all four options with meaningful defaults
        for letter in ['A', 'B', 'C', 'D']:
            if letter not in options or not options[letter]:
                options[letter] = f"Option {letter}"

        return options

    def _combine_questions_with_answers(self, questions_data, answer_mapping, solutions_mapping):
        """
        Combine extracted questions with answers from answer key and solutions.
        """
        log_print("🔄 [COMBINE] Combining questions with answers and solutions...")

        final_questions = []

        for question_data in questions_data:
            question_num = question_data['number']

            # Create the final question object
            final_question = {
                'question': question_data['question'],
                'options': question_data['options'],
                'type': 'mcq',
                'difficulty': 'medium'
            }

            # Add answer from answer key
            if question_num in answer_mapping:
                final_question['answer'] = answer_mapping[question_num]
            else:
                # Default to A if no answer found
                final_question['answer'] = 'A'
                log_print(f"⚠️ [MISSING_ANSWER] No answer found for question {question_num}, defaulting to A")

            # Add solution if available
            if question_num in solutions_mapping:
                solution_data = solutions_mapping[question_num]
                final_question['solution'] = {
                    'steps': solution_data.get('steps', []),
                    'methodology': solution_data.get('methodology', ''),
                    'key_concepts': solution_data.get('key_concepts', []),
                    'final_explanation': solution_data.get('full_solution', '')[:500] + '...' if len(solution_data.get('full_solution', '')) > 500 else solution_data.get('full_solution', '')
                }
                final_question['hints'] = [solution_data.get('methodology', 'Check the solution steps')]
            else:
                # Add basic solution structure
                final_question['solution'] = {
                    'steps': ['Analyze the given information', 'Apply relevant physics principles', 'Calculate the result'],
                    'methodology': 'Physics problem solving',
                    'key_concepts': ['Physics', 'Problem solving'],
                    'final_explanation': 'Apply the relevant physics concepts to solve this problem.'
                }
                final_question['hints'] = ['Apply relevant physics principles']

            final_questions.append(final_question)

        log_print(f"✅ [COMBINE] Combined {len(final_questions)} questions with answers and solutions")
        return final_questions

    def _clean_questions_for_json(self, questions):
        """
        Clean questions data to ensure proper JSON serialization.
        Handles LaTeX formatting, special characters, and other issues.
        """
        log_print("🧹 [JSON_CLEAN] Cleaning questions for JSON serialization...")

        cleaned_questions = []

        for question in questions:
            if not isinstance(question, dict):
                continue

            cleaned_question = {}

            # Clean question text
            if 'question' in question:
                cleaned_question['question'] = self._clean_text_for_json(question['question'])

            # Clean options
            if 'options' in question and isinstance(question['options'], dict):
                cleaned_question['options'] = {}
                for key, value in question['options'].items():
                    cleaned_question['options'][key] = self._clean_text_for_json(str(value))

            # Copy other fields safely
            for field in ['answer', 'type', 'difficulty']:
                if field in question:
                    cleaned_question[field] = str(question[field])

            # Clean solution if present
            if 'solution' in question and isinstance(question['solution'], dict):
                cleaned_question['solution'] = {}
                for key, value in question['solution'].items():
                    if isinstance(value, list):
                        cleaned_question['solution'][key] = [self._clean_text_for_json(str(item)) for item in value]
                    else:
                        cleaned_question['solution'][key] = self._clean_text_for_json(str(value))

            # Clean hints if present
            if 'hints' in question and isinstance(question['hints'], list):
                cleaned_question['hints'] = [self._clean_text_for_json(str(hint)) for hint in question['hints']]

            cleaned_questions.append(cleaned_question)

        log_print(f"✅ [JSON_CLEAN] Cleaned {len(cleaned_questions)} questions for JSON")
        return cleaned_questions

    def _clean_text_for_json(self, text):
        """
        Clean text to be JSON-safe while preserving mathematical content and handling images.
        """
        if not text:
            return ""

        # Convert to string if not already
        text = str(text)

        # Handle image references and convert to proper format
        text = self._process_images_in_text(text)

        # Clean up solution text that got mixed with questions
        text = self._remove_solution_contamination(text)

        # Convert LaTeX to proper format for frontend rendering
        text = self._convert_latex_to_katex(text)

        # Remove extra whitespace
        text = ' '.join(text.split())

        # Ensure it's not too long
        if len(text) > 2000:  # Increased limit for complex questions
            text = text[:1997] + "..."

        return text

    def _process_images_in_text(self, text):
        """
        Process image references in text and convert to proper format.
        """
        import re

        # Find image references like ![img-132.jpeg](img-132.jpeg)
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'

        def replace_image(match):
            alt_text = match.group(1)
            image_name = match.group(2)

            log_print(f"🖼️ [IMAGE_DEBUG] Processing image: {image_name}")

            # Check if we have this image in our OCR data
            if hasattr(self, 'current_ocr_data') and self.current_ocr_data:
                all_images = self.current_ocr_data.get('all_images', {})
                log_print(f"🖼️ [IMAGE_DEBUG] Available images: {list(all_images.keys())[:5]}...")

                if image_name in all_images:
                    image_data = all_images[image_name]
                    log_print(f"🖼️ [IMAGE_DEBUG] Found image {image_name}, data length: {len(image_data)}")

                    # Ensure the base64 data is clean
                    if image_data and len(image_data) > 100:  # Valid base64 should be longer
                        # Clean the base64 data and remove any existing data URL prefix
                        clean_data = image_data.replace('\n', '').replace('\r', '').strip()

                        # Remove any existing data URL prefix to avoid duplication
                        if clean_data.startswith('data:image/'):
                            log_print(f"🖼️ [IMAGE_DEBUG] Removing existing data URL prefix")
                            # Extract just the base64 part after the comma
                            if ',' in clean_data:
                                clean_data = clean_data.split(',', 1)[1]

                        log_print(f"🖼️ [IMAGE_DEBUG] Creating markdown image with clean data length: {len(clean_data)}")
                        return f'![{alt_text}](data:image/jpeg;base64,{clean_data})'
                    else:
                        log_print(f"🖼️ [IMAGE_DEBUG] Image data too short: {len(image_data) if image_data else 0}")
                else:
                    log_print(f"🖼️ [IMAGE_DEBUG] Image {image_name} not found in available images")
            else:
                log_print(f"🖼️ [IMAGE_DEBUG] No OCR data available")

            # Fallback: return a placeholder that's more user-friendly
            log_print(f"🖼️ [IMAGE_DEBUG] Using placeholder for {image_name}")
            return f'📷 [Circuit Diagram: {image_name}]'

        # Replace all image references
        text = re.sub(image_pattern, replace_image, text)

        return text

    def _remove_solution_contamination(self, text):
        """
        Remove solution text that got mixed with question text.
        """
        import re

        # Remove solution numbers like "225(c)" at the beginning
        text = re.sub(r'^\d+\([a-d]\)\s*', '', text)

        # Remove garbled text patterns that appear in your examples
        # Remove repeated single characters with spaces
        text = re.sub(r'\b[a-z]\s+[a-z]\s+[a-z]\s+[a-z]\s+[a-z]\b', '', text)

        # Remove patterns like "m a i n t a i n i n g t h e s a m e v o l t a g e"
        text = re.sub(r'(\b\w\s+){10,}', '', text)

        # Remove calculation steps that start with f= or similar
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # Skip lines that look like solutions or garbled text
            if (line.startswith('f=') or
                line.startswith('\\therefore') or
                line.startswith('\\approx') or
                re.match(r'^\d+\s*\([a-d]\)', line) or
                # Skip lines with too many single-character words
                len([word for word in line.split() if len(word) == 1]) > 5):
                continue

            cleaned_lines.append(line)

        # Join and clean up extra spaces
        result = ' '.join(cleaned_lines)

        # Remove patterns like "(maintainingthesamevoltage)" - concatenated words
        result = re.sub(r'\([a-z]{20,}\)', '', result)

        # Clean up multiple spaces
        result = re.sub(r'\s+', ' ', result)

        return result.strip()

    def _convert_latex_to_katex(self, text):
        """
        Convert LaTeX expressions to KaTeX format for proper frontend rendering.
        """
        import re

        # First, let's be more conservative and only convert well-formed LaTeX
        # Don't wrap everything in $ signs - let the frontend handle KaTeX rendering

        # Handle fractions more carefully
        def fix_fraction(match):
            numerator = match.group(1)
            denominator = match.group(2)
            return f'\\frac{{{numerator}}}{{{denominator}}}'

        text = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', fix_fraction, text)

        # Handle square roots more carefully
        def fix_sqrt(match):
            content = match.group(1)
            return f'\\sqrt{{{content}}}'

        text = re.sub(r'\\sqrt\{([^}]+)\}', fix_sqrt, text)
        text = re.sub(r'√\{([^}]+)\}', fix_sqrt, text)

        # Handle subscripts and superscripts more carefully
        def fix_subscript(match):
            content = match.group(1)
            return f'_{{{content}}}'

        def fix_superscript(match):
            content = match.group(1)
            return f'^{{{content}}}'

        text = re.sub(r'_\{([^}]+)\}', fix_subscript, text)
        text = re.sub(r'\^\{([^}]+)\}', fix_superscript, text)

        # Convert common symbols to Unicode instead of LaTeX
        symbol_replacements = {
            '\\Omega': 'Ω',
            '\\omega': 'ω',
            '\\pi': 'π',
            '\\theta': 'θ',
            '\\alpha': 'α',
            '\\beta': 'β',
            '\\gamma': 'γ',
            '\\delta': 'δ',
            '\\times': '×',
            '\\cdot': '·',
            '\\neq': '≠',
            '\\approx': '≈',
            '\\therefore': '∴',
            '\\infty': '∞',
            '\\pm': '±',
            '\\leq': '≤',
            '\\geq': '≥',
        }

        for latex, unicode_char in symbol_replacements.items():
            text = text.replace(latex, unicode_char)

        # Remove mathbf wrapper
        text = re.sub(r'\\mathbf\{([^}]+)\}', r'\1', text)

        # Fix common LaTeX errors that cause \ffrac instead of \frac
        # Handle the specific patterns from your examples

        # Fix \ffrac with braces
        text = re.sub(r'\\ffrac\{', r'\\frac{', text)  # Fix \ffrac{ to \frac{

        # Fix \ffrac without braces - the main issue in your examples
        text = re.sub(r'\\ffrac(\d+)(\d+)([a-zA-Z]+)', r'\\frac{\1}{\2\3}', text)  # \ffrac11000ohm -> \frac{1}{1000ohm}
        text = re.sub(r'\\ffrac(\d+)([πα-ωΩ])([a-zA-Z]+)', r'\\frac{\1}{\2\3}', text)  # \ffrac100πMHz -> \frac{100}{πMHz}
        text = re.sub(r'\\ffrac(\d+)([πα-ωΩ])', r'\\frac{\1}{\2}', text)  # \ffrac1000π -> \frac{1000}{π}
        text = re.sub(r'\\ffrac(\d+)([a-zA-Z]+)', r'\\frac{\1}{\2}', text)  # \ffrac1000Hz -> \frac{1000}{Hz}

        # Fix complex expressions like \ffracV R1R2 R1
        text = re.sub(r'\\ffrac([A-Z])([A-Z]\d*[A-Z]\d*)', r'\\frac{\1}{\2}', text)  # \ffracV R1R2 -> \frac{V}{R1R2}
        text = re.sub(r'\\ffrac([A-Z])([A-Z]\d*)', r'\\frac{\1}{\2}', text)  # \ffracV R2 -> \frac{V}{R2}
        text = re.sub(r'\\ffrac([A-Z])', r'\\frac{\1}{}', text)  # \ffracV -> \frac{V}{}

        # Fix expressions with parentheses like \ffracE 2 [R2 + (Lω − \ffrac1Cω) R
        text = re.sub(r'\\ffrac([A-Z])(\s+\d+)', r'\\frac{\1}{\2}', text)  # \ffracE 2 -> \frac{E}{2}

        # Fix nested \ffrac in expressions
        text = re.sub(r'\\ffrac(\d+)([A-Z][a-z]*)', r'\\frac{\1}{\2}', text)  # \ffrac1Cω -> \frac{1}{Cω}

        # Fix other function names
        text = re.sub(r'\\fsqrt\{', r'\\sqrt{', text)  # Fix \fsqrt to \sqrt
        text = re.sub(r'\\fsin\b', r'\\sin', text)     # Fix \fsin to \sin
        text = re.sub(r'\\fcos\b', r'\\cos', text)     # Fix \fcos to \cos
        text = re.sub(r'\\ftan\b', r'\\tan', text)     # Fix \ftan to \tan
        text = re.sub(r'\\flog\b', r'\\log', text)     # Fix \flog to \log

        # Clean up any remaining backslashes that aren't part of valid LaTeX
        # But preserve valid LaTeX commands
        valid_latex_commands = [
            '\\frac', '\\sqrt', '\\sum', '\\int', '\\lim', '\\sin', '\\cos', '\\tan',
            '\\log', '\\ln', '\\exp', '\\left', '\\right', '\\mathrm'
        ]

        # Don't remove backslashes that are part of valid commands
        for cmd in valid_latex_commands:
            text = text.replace(cmd, f'LATEX_CMD_{cmd[1:]}')

        # Remove other backslashes (but not double backslashes which might be line breaks)
        text = re.sub(r'(?<!\\)\\(?![\\])', '', text)

        # Restore valid LaTeX commands
        for cmd in valid_latex_commands:
            text = text.replace(f'LATEX_CMD_{cmd[1:]}', cmd)

        return text

    def _enhance_with_hybrid_ai(self, existing_questions, answer_mapping, solutions_mapping, full_text):
        """
        Use hybrid AI approach (both Gemini and Mistral) to extract missing questions.
        This helps when one AI provider misses questions that the other can find.
        """
        log_print("🤖 [HYBRID_AI] Starting hybrid AI enhancement...")

        existing_numbers = {q['number'] for q in existing_questions}
        missing_numbers = []

        # Find missing question numbers from answer mapping
        for num in answer_mapping.keys():
            if num not in existing_numbers:
                missing_numbers.append(num)

        if not missing_numbers:
            log_print("✅ [HYBRID_AI] No missing questions found")
            return []

        log_print(f"🔍 [HYBRID_AI] Found {len(missing_numbers)} missing questions: {missing_numbers[:10]}...")

        enhanced_questions = []

        # Try with alternative AI provider
        original_provider = self.ai_provider
        alternative_provider = 'mistral' if self.ai_provider == 'gemini' else 'gemini'

        try:
            # Switch to alternative provider
            self.ai_provider = alternative_provider
            log_print(f"🔄 [HYBRID_AI] Switching to {alternative_provider.upper()} for missing questions...")

            # Extract questions using alternative AI
            alternative_questions = self._extract_missing_questions_with_ai(
                missing_numbers, full_text, answer_mapping, solutions_mapping
            )

            enhanced_questions.extend(alternative_questions)
            log_print(f"✅ [HYBRID_AI] {alternative_provider.upper()} found {len(alternative_questions)} additional questions")

        except Exception as e:
            log_print(f"❌ [HYBRID_AI] Error with {alternative_provider}: {e}")

        finally:
            # Restore original provider
            self.ai_provider = original_provider

        # If still missing questions, try with chunked approach
        if len(enhanced_questions) < len(missing_numbers) * 0.5:
            log_print("🔄 [HYBRID_AI] Trying chunked extraction for remaining questions...")
            chunked_questions = self._extract_missing_questions_chunked(
                missing_numbers, full_text, answer_mapping
            )
            enhanced_questions.extend(chunked_questions)

        log_print(f"✅ [HYBRID_AI] Total enhanced questions: {len(enhanced_questions)}")
        return enhanced_questions

    def _extract_missing_questions_with_ai(self, missing_numbers, full_text, answer_mapping, solutions_mapping):
        """
        Extract specific missing questions using AI.
        """
        if not missing_numbers:
            return []

        # Create focused prompt for missing questions
        missing_range = f"{min(missing_numbers)} to {max(missing_numbers)}"
        prompt = f"""
        Extract the missing physics questions from this JEE/NEET ALTERNATING CURRENT paper.

        MISSING QUESTION NUMBERS: {missing_numbers[:20]}

        Find these specific numbered questions in the text and extract them with their options.

        Expected format for each question:
        {{
            "number": question_number,
            "question": "Question text here",
            "options": {{"A": "Option A", "B": "Option B", "C": "Option C", "D": "Option D"}}
        }}

        Return as JSON array. Focus only on questions {missing_range}.

        Text to search:
        {full_text[:50000]}  # First 50KB for context
        """

        try:
            if self.ai_provider == 'gemini':
                result = self._extract_with_gemini(prompt)
            else:
                result = self._extract_with_mistral(prompt)

            import json
            cleaned_result = self._clean_json_response(result)
            questions = json.loads(cleaned_result)

            if isinstance(questions, list):
                # Filter to only requested numbers
                filtered_questions = [q for q in questions if q.get('number') in missing_numbers]
                return filtered_questions

        except Exception as e:
            log_print(f"❌ [MISSING_QUESTIONS_AI] Error: {e}")

        return []

    def _extract_missing_questions_chunked(self, missing_numbers, full_text, answer_mapping):
        """
        Extract missing questions using chunked text approach.
        """
        log_print("🔄 [CHUNKED_MISSING] Extracting missing questions with chunked approach...")

        # Split text into chunks and search for missing questions
        chunk_size = 10000
        found_questions = []

        for i in range(0, len(full_text), chunk_size):
            chunk = full_text[i:i + chunk_size + 1000]  # Overlap for question boundaries

            # Look for any missing question numbers in this chunk
            chunk_questions = []
            for num in missing_numbers:
                if f"{num}." in chunk:
                    # Try to extract this specific question
                    question_data = self._extract_single_question_from_chunk(num, chunk)
                    if question_data:
                        chunk_questions.append(question_data)

            found_questions.extend(chunk_questions)

            if len(found_questions) >= len(missing_numbers):
                break

        log_print(f"✅ [CHUNKED_MISSING] Found {len(found_questions)} questions via chunked approach")
        return found_questions

    def _extract_single_question_from_chunk(self, question_num, chunk):
        """
        Extract a single question from a text chunk.
        """
        import re

        # Look for the specific question number
        pattern = f'{question_num}\\.(.*?)(?={question_num + 1}\\.|$)'
        match = re.search(pattern, chunk, re.DOTALL)

        if match:
            question_block = match.group(1).strip()
            return self._parse_question_block(question_num, question_block)

        return None

    def _extract_answer_key_mapping(self, full_text):
        """
        Extract answer key mapping from text like "1) b  2) a  3) a  4) d"
        """
        import re

        # Look for answer key patterns
        patterns = [
            r'(\d+)\)\s*([abcdABCD])',  # "1) b"
            r'(\d+)\.\s*([abcdABCD])',  # "1. b"
            r'(\d+)\s+([abcdABCD])',    # "1 b"
        ]

        answer_mapping = {}

        for pattern in patterns:
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            for match in matches:
                question_num = int(match[0])
                answer = match[1].upper()
                if question_num not in answer_mapping:
                    answer_mapping[question_num] = answer

        return answer_mapping

    def _extract_solutions_mapping(self, full_text):
        """
        Extract solutions mapping from hints and solutions section
        """
        import re

        solutions_mapping = {}

        # Split text into sections
        sections = re.split(r'(?i)(?:hints?\s+and\s+solutions?|solutions?\s+and\s+hints?)', full_text)

        if len(sections) > 1:
            solutions_text = sections[-1]  # Take the last section (solutions)

            # Look for numbered solution entries
            # Pattern: number followed by (letter) and then solution text
            solution_pattern = r'(\d+)\s*\([abcdABCD]\)(.*?)(?=\d+\s*\([abcdABCD]\)|$)'
            matches = re.findall(solution_pattern, solutions_text, re.DOTALL | re.IGNORECASE)

            for match in matches:
                question_num = int(match[0])
                solution_text = match[1].strip()

                # Extract key concepts and methodology from solution
                solutions_mapping[question_num] = {
                    'full_solution': solution_text,
                    'steps': self._extract_solution_steps(solution_text),
                    'key_concepts': self._extract_key_concepts(solution_text),
                    'methodology': self._extract_methodology(solution_text)
                }

        return solutions_mapping

    def _extract_solution_steps(self, solution_text):
        """Extract solution steps from solution text"""
        # Look for step indicators
        import re
        steps = []

        # Look for mathematical expressions and equations
        equations = re.findall(r'[=→⇒].*?(?=\n|$)', solution_text)
        for eq in equations[:5]:  # Limit to 5 steps
            steps.append(eq.strip())

        return steps if steps else ["Solution provided in original text"]

    def _extract_key_concepts(self, solution_text):
        """Extract key physics/science concepts from solution"""
        # Common physics concepts
        concepts = []
        concept_keywords = [
            'frequency', 'current', 'voltage', 'resistance', 'capacitance',
            'inductance', 'magnetic field', 'electric field', 'energy',
            'power', 'force', 'acceleration', 'velocity', 'momentum'
        ]

        for concept in concept_keywords:
            if concept.lower() in solution_text.lower():
                concepts.append(concept.title())

        return concepts[:3] if concepts else ["Physics concepts"]

    def _extract_methodology(self, solution_text):
        """Extract solution methodology"""
        if 'equation' in solution_text.lower():
            return "Mathematical equation solving"
        elif 'formula' in solution_text.lower():
            return "Formula application"
        elif 'circuit' in solution_text.lower():
            return "Circuit analysis"
        else:
            return "Analytical approach"

    def _generate_questions_from_structure(self, full_text, answer_mapping, solutions_mapping, estimated_questions):
        """
        Generate questions using AI based on structured answer keys and solutions
        """
        log_print("🔄 [QUESTION_GENERATION] Generating questions from structured data...")

        # Create a comprehensive prompt that uses the structured information
        generation_prompt = f"""
        You are analyzing a structured physics question paper with answer keys and solutions.

        CONTEXT:
        - This is a JEE/NEET Physics paper on "ALTERNATING CURRENT"
        - Total Questions: {estimated_questions}
        - Answer key format: "1) b  2) a  3) a  4) d"
        - Solutions provided with detailed explanations

        TASK:
        Generate {min(estimated_questions, 400)} multiple-choice physics questions based on the answer patterns and solutions provided.

        ANSWER KEY INFORMATION:
        {self._format_answer_mapping(answer_mapping)}

        SOLUTION HINTS:
        {self._format_solutions_preview(solutions_mapping)}

        REQUIREMENTS:
        1. Create physics questions on ALTERNATING CURRENT topic
        2. Each question should have 4 options (A, B, C, D)
        3. Use the correct answers from the answer key
        4. Make questions appropriate for JEE/NEET level
        5. Include mathematical formulas and concepts where relevant
        6. Questions should be numbered 1, 2, 3, etc.

        OUTPUT FORMAT (JSON array):
        [
          {{
            "question": "Question text with proper physics terminology",
            "options": {{
              "A": "Option A text",
              "B": "Option B text",
              "C": "Option C text",
              "D": "Option D text"
            }},
            "answer": "B",
            "type": "mcq",
            "difficulty": "medium",
            "solution": {{
              "steps": ["Step 1", "Step 2"],
              "methodology": "Circuit analysis",
              "key_concepts": ["AC current", "frequency"],
              "final_explanation": "Explanation based on solution"
            }},
            "hints": ["Hint from solution section"]
          }}
        ]

        IMPORTANT:
        - Generate questions for numbers 1 through {min(estimated_questions, 400)}
        - Use EXACT answers from the answer key provided
        - Make questions realistic and physics-appropriate
        - Include relevant formulas and concepts

        Full text for reference:
        {full_text[:100000]}  # Limit to first 100KB for context
        """

        # Process in chunks if we have many questions
        if estimated_questions > 100:
            return self._generate_questions_in_batches(generation_prompt, estimated_questions, answer_mapping, solutions_mapping)
        else:
            # Generate all at once for smaller sets
            try:
                if self.ai_provider == 'gemini':
                    result = self._extract_with_gemini(generation_prompt)
                else:
                    result = self._extract_with_mistral(generation_prompt)

                import json
                cleaned_result = self._clean_json_response(result)
                questions = json.loads(cleaned_result)

                # Validate and fix answers
                questions = self._validate_and_fix_answers(questions, answer_mapping, solutions_mapping)

                return questions

            except Exception as e:
                log_print(f"❌ [QUESTION_GENERATION_ERROR] Error generating questions: {e}")
                return self._create_fallback_questions(answer_mapping, solutions_mapping)

    def _format_answer_mapping(self, answer_mapping):
        """Format answer mapping for prompt"""
        if not answer_mapping:
            return "No answer key found"

        formatted = []
        for i in range(1, min(max(answer_mapping.keys()) + 1, 401)):  # Limit to 400
            if i in answer_mapping:
                formatted.append(f"{i}) {answer_mapping[i]}")

        return " ".join(formatted[:50]) + "..." if len(formatted) > 50 else " ".join(formatted)

    def _format_solutions_preview(self, solutions_mapping):
        """Format solutions preview for prompt"""
        if not solutions_mapping:
            return "No solutions found"

        preview = []
        for i in range(1, min(6, len(solutions_mapping) + 1)):  # Show first 5 solutions
            if i in solutions_mapping:
                solution = solutions_mapping[i]
                preview.append(f"Q{i}: {solution['methodology']} - {', '.join(solution['key_concepts'][:2])}")

        return "; ".join(preview)

    def _generate_questions_in_batches(self, base_prompt, total_questions, answer_mapping, solutions_mapping):
        """
        Generate questions in batches for large question sets
        """
        log_print(f"🔄 [BATCH_GENERATION] Generating {total_questions} questions in batches...")

        batch_size = 50  # Generate 50 questions at a time
        all_questions = []

        for start_num in range(1, total_questions + 1, batch_size):
            end_num = min(start_num + batch_size - 1, total_questions)

            log_print(f"🔄 [BATCH_{start_num}-{end_num}] Generating questions {start_num} to {end_num}...")

            # Create batch-specific prompt
            batch_answers = {k: v for k, v in answer_mapping.items() if start_num <= k <= end_num}
            batch_solutions = {k: v for k, v in solutions_mapping.items() if start_num <= k <= end_num}

            batch_prompt = f"""
            Generate physics questions {start_num} to {end_num} for JEE/NEET ALTERNATING CURRENT topic.

            ANSWER KEY FOR THIS BATCH:
            {self._format_answer_mapping(batch_answers)}

            SOLUTION HINTS FOR THIS BATCH:
            {self._format_solutions_preview(batch_solutions)}

            Generate exactly {end_num - start_num + 1} questions in JSON array format.
            Each question should have proper physics content, 4 options, and correct answer from the key.

            [
              {{
                "question": "Physics question text",
                "options": {{"A": "Option A", "B": "Option B", "C": "Option C", "D": "Option D"}},
                "answer": "B",
                "type": "mcq",
                "difficulty": "medium"
              }}
            ]
            """

            try:
                if self.ai_provider == 'gemini':
                    batch_result = self._extract_with_gemini(batch_prompt)
                else:
                    batch_result = self._extract_with_mistral(batch_prompt)

                import json
                cleaned_result = self._clean_json_response(batch_result)
                batch_questions = json.loads(cleaned_result)

                if isinstance(batch_questions, list):
                    # Validate answers for this batch
                    validated_questions = self._validate_and_fix_answers(batch_questions, batch_answers, batch_solutions)
                    all_questions.extend(validated_questions)
                    log_print(f"✅ [BATCH_{start_num}-{end_num}] Generated {len(validated_questions)} questions")
                else:
                    log_print(f"❌ [BATCH_{start_num}-{end_num}] Invalid response format")

            except Exception as e:
                log_print(f"❌ [BATCH_{start_num}-{end_num}] Error: {e}")
                # Create fallback questions for this batch
                fallback_questions = self._create_fallback_questions(batch_answers, batch_solutions)
                all_questions.extend(fallback_questions)

        log_print(f"✅ [BATCH_GENERATION] Generated {len(all_questions)} total questions")
        return all_questions

    def _validate_and_fix_answers(self, questions, answer_mapping, solutions_mapping):
        """
        Validate and fix answers based on the answer key
        """
        validated_questions = []

        for i, question in enumerate(questions):
            if not isinstance(question, dict):
                continue

            question_num = i + 1

            # Fix answer if we have it in the mapping
            if question_num in answer_mapping:
                question['answer'] = answer_mapping[question_num]

            # Add solution if available
            if question_num in solutions_mapping:
                solution_data = solutions_mapping[question_num]
                question['solution'] = {
                    'steps': solution_data.get('steps', []),
                    'methodology': solution_data.get('methodology', ''),
                    'key_concepts': solution_data.get('key_concepts', []),
                    'final_explanation': solution_data.get('full_solution', '')[:200] + '...'
                }
                question['hints'] = [solution_data.get('methodology', 'Check the solution steps')]

            validated_questions.append(question)

        return validated_questions

    def _create_fallback_questions(self, answer_mapping, solutions_mapping):
        """
        Create fallback questions when AI generation fails
        """
        log_print("🔄 [FALLBACK_QUESTIONS] Creating fallback questions...")

        fallback_questions = []

        for question_num in sorted(answer_mapping.keys())[:100]:  # Limit to 100 fallback questions
            correct_answer = answer_mapping[question_num]

            # Create a basic physics question
            question = {
                "question": f"Question {question_num}: In an AC circuit with alternating current, what is the relationship between current and voltage?",
                "options": {
                    "A": "Current leads voltage by 90°",
                    "B": "Current and voltage are in phase",
                    "C": "Current lags voltage by 90°",
                    "D": "Current and voltage are independent"
                },
                "answer": correct_answer,
                "type": "mcq",
                "difficulty": "medium"
            }

            # Add solution if available
            if question_num in solutions_mapping:
                solution_data = solutions_mapping[question_num]
                question['solution'] = {
                    'steps': solution_data.get('steps', []),
                    'methodology': solution_data.get('methodology', ''),
                    'key_concepts': solution_data.get('key_concepts', []),
                    'final_explanation': 'Based on AC circuit analysis principles'
                }
                question['hints'] = ['Consider the phase relationship in AC circuits']

            fallback_questions.append(question)

        log_print(f"✅ [FALLBACK_QUESTIONS] Created {len(fallback_questions)} fallback questions")
        return fallback_questions

    def _process_chunk_with_retry(self, chunk_prompt, chunk_num, total_chunks, max_retries=2):
        """
        Process a single chunk with retry logic for better reliability.
        """
        for attempt in range(max_retries + 1):
            try:
                chunk_start_time = time.time()

                if self.ai_provider == 'gemini':
                    chunk_result = self._extract_with_gemini(chunk_prompt)
                else:
                    chunk_result = self._extract_with_mistral(chunk_prompt)

                chunk_processing_time = time.time() - chunk_start_time

                # Parse and validate chunk result
                import json
                cleaned_result = self._clean_json_response(chunk_result)
                chunk_questions = json.loads(cleaned_result)

                if isinstance(chunk_questions, list):
                    if len(chunk_questions) > 0:
                        log_print(f"✅ [CHUNK_{chunk_num}] Extracted {len(chunk_questions)} questions in {chunk_processing_time:.1f}s (attempt {attempt + 1})")
                    return chunk_questions
                else:
                    log_print(f"⚠️ [CHUNK_{chunk_num}] Invalid response format (attempt {attempt + 1})")

            except Exception as e:
                log_print(f"❌ [CHUNK_{chunk_num}] Error processing chunk (attempt {attempt + 1}): {e}")
                if attempt == max_retries:
                    log_print(f"❌ [CHUNK_{chunk_num}] All retry attempts failed")
                    return []
                else:
                    log_print(f"🔄 [CHUNK_{chunk_num}] Retrying in 2 seconds...")
                    time.sleep(2)

        return []

    def _remove_duplicate_questions_enhanced(self, questions):
        """
        Enhanced deduplication that's less aggressive to avoid removing valid questions.
        """
        if not questions:
            return []

        unique_questions = []
        seen_contents = set()

        for question in questions:
            if not isinstance(question, dict) or 'question' not in question:
                continue

            # Use 'question' field instead of 'content' for newer format
            question_text = question.get('question', question.get('content', ''))
            if not question_text:
                continue

            # Normalize content for comparison (remove extra spaces, convert to lowercase)
            normalized_content = ' '.join(question_text.lower().split())

            # More lenient similarity check - only remove if 95% similar (was 90%)
            is_duplicate = False
            for seen_content in seen_contents:
                seen_words = set(seen_content.split())
                current_words = set(normalized_content.split())

                if len(seen_words) > 0 and len(current_words) > 0:
                    intersection = len(seen_words.intersection(current_words))
                    union = len(seen_words.union(current_words))
                    similarity = intersection / union if union > 0 else 0

                    if similarity > 0.95:  # 95% similarity threshold (more lenient)
                        is_duplicate = True
                        break

            if not is_duplicate:
                unique_questions.append(question)
                seen_contents.add(normalized_content)

        return unique_questions

    def _try_multiple_extraction_approaches(self, full_text):
        """
        Try multiple different extraction approaches when standard methods fail.
        """
        log_print("🔄 [MULTI_APPROACH] Trying multiple extraction approaches...")

        approaches = [
            self._try_pattern_based_extraction,
            self._try_aggressive_extraction,
            self._try_simple_extraction
        ]

        best_questions = []

        for i, approach in enumerate(approaches):
            try:
                log_print(f"🔄 [MULTI_APPROACH] Trying approach {i + 1}/3...")
                questions = approach(full_text)

                if isinstance(questions, str):
                    import json
                    cleaned = self._clean_json_response(questions)
                    questions = json.loads(cleaned)

                if isinstance(questions, list) and len(questions) > len(best_questions):
                    best_questions = questions
                    log_print(f"✅ [MULTI_APPROACH] Approach {i + 1} found {len(questions)} questions!")

            except Exception as e:
                log_print(f"❌ [MULTI_APPROACH] Approach {i + 1} failed: {e}")
                continue

        return best_questions

    def _try_pattern_based_extraction(self, full_text):
        """
        Pattern-based extraction that looks for common question patterns.
        """
        log_print("🔄 [PATTERN_EXTRACTION] Attempting pattern-based extraction...")

        pattern_prompt = f"""
        You are analyzing a document that contains multiple-choice questions. Use pattern recognition to find ALL questions.

        LOOK FOR THESE PATTERNS:
        - Numbers followed by periods: "1.", "2.", "3."
        - Question indicators: "Q1:", "Question 1:", "Ques 1"
        - Text ending with "?" followed by options
        - Lists with (a), (b), (c), (d) or A), B), C), D)
        - Answer keys like "1. A", "2. B", etc.

        EXTRACT EVERY POSSIBLE QUESTION - even if formatting is inconsistent.

        Return JSON array format:
        [
          {{
            "question": "Question text here",
            "options": {{
              "A": "Option A",
              "B": "Option B",
              "C": "Option C",
              "D": "Option D"
            }},
            "answer": "A",
            "type": "mcq",
            "difficulty": "medium"
          }}
        ]

        Text to analyze:
        {full_text[:500000]}  # Limit to first 500KB for this approach
        """

        if self.ai_provider == 'gemini':
            return self._extract_with_gemini(pattern_prompt)
        else:
            return self._extract_with_mistral(pattern_prompt)

    def _try_simple_extraction(self, full_text):
        """
        Simple extraction with minimal formatting requirements.
        """
        log_print("🔄 [SIMPLE_EXTRACTION] Attempting simple extraction...")

        simple_prompt = f"""
        Find any text that looks like questions and answers in this document.
        Don't worry about perfect formatting - just extract anything that could be a question.

        Return as JSON array with this structure:
        [
          {{
            "question": "Any question-like text",
            "options": {{
              "A": "Any option text",
              "B": "Any option text",
              "C": "Any option text",
              "D": "Any option text"
            }},
            "answer": "A",
            "type": "mcq",
            "difficulty": "medium"
          }}
        ]

        Text: {full_text[:300000]}  # Limit to first 300KB
        """

        if self.ai_provider == 'gemini':
            return self._extract_with_gemini(simple_prompt)
        else:
            return self._extract_with_mistral(simple_prompt)

    def _try_fallback_extraction(self, full_text):
        """
        Fallback extraction method with a simpler, more flexible prompt
        """
        log_print("🔄 [FALLBACK] Attempting simplified extraction...")

        fallback_prompt = f"""
        Extract any multiple-choice questions from this text. Be very flexible with formatting.

        Look for:
        - Any question followed by multiple choice options
        - Options can be labeled with letters (a,b,c,d) or (A,B,C,D) or numbers
        - Questions might be numbered or not
        - Don't worry about perfect formatting

        IMAGE HANDLING:
        - ONLY add "imageUrl" if question text actually contains image references like ![img-X.jpeg](img-X.jpeg)
        - If question has images, use empty strings: "imageUrl": {{"img-0.jpeg": ""}}
        - If option has images, use placeholder: "A": "IMAGE_PLACEHOLDER_img-2.jpeg"
        - Do NOT use actual base64 data - use placeholders that will be filled later

        Return a JSON array like this:
        [
          {{
            "question": "question text with ![img-0.jpeg](img-0.jpeg) if present",
            "imageUrl": {{"img-0.jpeg": ""}},
            "options": {{"A": "text option", "B": "IMAGE_PLACEHOLDER_img-1.jpeg", "C": "text option", "D": "text option"}},
            "answer": "",
            "solution": {{"steps": [], "methodology": "", "key_concepts": [], "final_explanation": ""}},
            "hints": []
          }}
        ]

        Remember: Only include "imageUrl" if question actually has image references!

        If you find ANY questions at all, extract them. If no questions found, return [].
        Return ONLY the JSON array, no other text.

        Text:
        {full_text}
        """

        # Use the same AI provider for fallback
        if self.ai_provider == 'gemini':
            return self._extract_with_gemini(fallback_prompt)
        else:
            return self._extract_with_mistral(fallback_prompt)

    def _extract_with_mistral(self, extraction_prompt):
        """
        Extract questions using Mistral AI

        Args:
            extraction_prompt (str): The prompt to send to the AI model

        Returns:
            str: Enhanced JSON string with questions and image data
        """
        log_print("🤖 [AI_CALL_START] Calling Mistral AI model for questions extraction...")
        log_print(f"📊 [AI_CALL_CONTEXT] Model: mistral-small-latest, Prompt length: {len(extraction_prompt):,} chars")

        ai_start_time = time.time()
        try:
            log_print("🔄 [AI_CALL_PROGRESS] Sending request to Mistral AI...")
            response = self.client.chat.complete(
                model="mistral-small-latest",
                messages=[
                    {
                        "role": "user",
                        "content": extraction_prompt
                    }
                ]
            )

            ai_duration = time.time() - ai_start_time
            log_print(f"⏱️ [AI_CALL_TIMING] Mistral AI response received in {ai_duration:.2f}s")

            # Get the direct response from model
            if response and response.choices and len(response.choices) > 0:
                json_content = response.choices[0].message.content
                response_length = len(json_content)
                print(f"✅ [AI_CALL_SUCCESS] Received response from Mistral AI ({response_length:,} chars)")
                print("📄 [AI_CALL_RESPONSE] Raw model response preview:")
                print(f"{json_content[:500]}{'...' if len(json_content) > 500 else ''}")

                # Check if response looks like valid JSON
                if not json_content.strip().startswith('['):
                    print(f"⚠️ [DEBUG] Response doesn't start with '[', full response: {json_content}")

                # Try to parse JSON to check validity
                try:
                    import json
                    test_parse = json.loads(json_content)
                    if isinstance(test_parse, list):
                        print(f"✅ [DEBUG] Valid JSON array with {len(test_parse)} items")
                    else:
                        print(f"⚠️ [DEBUG] JSON is not an array: {type(test_parse)}")
                except json.JSONDecodeError as e:
                    print(f"❌ [DEBUG] JSON parsing error: {e}")
                    print(f"🔍 [DEBUG] Problematic JSON: {json_content[:200]}...")

                # Process the JSON to add image data locally
                print("🔄 [POST_PROCESSING_START] Adding image data to questions...")
                post_process_start = time.time()
                enhanced_json = self._add_image_data_to_questions(json_content)
                post_process_duration = time.time() - post_process_start
                print(f"✅ [POST_PROCESSING_COMPLETE] Image processing finished in {post_process_duration:.2f}s")

                total_duration = time.time() - ai_start_time
                print(f"🎯 [AI_CALL_EXIT] Questions extraction completed in {total_duration:.2f}s")
                return enhanced_json
            else:
                ai_duration = time.time() - ai_start_time
                print(f"❌ [AI_CALL_ERROR] No response received from Mistral AI after {ai_duration:.2f}s")
                print("🎯 [AI_CALL_EXIT] Questions extraction failed - no response")
                return "[]"

        except Exception as api_error:
            ai_duration = time.time() - ai_start_time
            print(f"❌ [AI_CALL_ERROR] Error calling Mistral AI API after {ai_duration:.2f}s: {api_error}")
            print(f"🎯 [AI_CALL_EXIT] Questions extraction failed with exception")
            return "[]"

    def _extract_with_gemini(self, extraction_prompt):
        """
        Extract questions using Gemini AI

        Args:
            extraction_prompt (str): The prompt to send to the AI model

        Returns:
            str: Enhanced JSON string with questions and image data
        """
        log_print("🤖 [AI_CALL_START] Calling Gemini AI model for questions extraction...")
        log_print(f"📊 [AI_CALL_CONTEXT] Model: gemini-1.5-flash, Prompt length: {len(extraction_prompt):,} chars")

        ai_start_time = time.time()
        try:
            log_print("🔄 [AI_CALL_PROGRESS] Sending request to Gemini AI...")
            print(self.gemini_model)
            response = self.gemini_model.generate_content(extraction_prompt)

            ai_duration = time.time() - ai_start_time
            log_print(f"⏱️ [AI_CALL_TIMING] Gemini AI response received in {ai_duration:.2f}s")

            # Get the direct response from model
            if response and response.text:
                json_content = response.text
                response_length = len(json_content)
                print(f"✅ [AI_CALL_SUCCESS] Received response from Gemini AI ({response_length:,} chars)")
                print("📄 [AI_CALL_RESPONSE] Raw model response preview:")
                print(f"{json_content[:500]}{'...' if len(json_content) > 500 else ''}")

                # Check if response looks like valid JSON
                if not json_content.strip().startswith('['):
                    print(f"⚠️ [DEBUG] Response doesn't start with '[', full response: {json_content}")

                # Try to parse JSON to check validity
                try:
                    import json
                    test_parse = json.loads(json_content)
                    if isinstance(test_parse, list):
                        print(f"✅ [DEBUG] Valid JSON array with {len(test_parse)} items")
                    else:
                        print(f"⚠️ [DEBUG] JSON is not an array: {type(test_parse)}")
                except json.JSONDecodeError as e:
                    print(f"❌ [DEBUG] JSON parsing error: {e}")
                    print(f"🔍 [DEBUG] Problematic JSON: {json_content[:200]}...")

                # Process the JSON to add image data locally
                print("🔄 [POST_PROCESSING_START] Adding image data to questions...")
                post_process_start = time.time()
                enhanced_json = self._add_image_data_to_questions(json_content)
                post_process_duration = time.time() - post_process_start
                print(f"✅ [POST_PROCESSING_COMPLETE] Image processing finished in {post_process_duration:.2f}s")

                total_duration = time.time() - ai_start_time
                print(f"🎯 [AI_CALL_EXIT] Questions extraction completed in {total_duration:.2f}s")
                return enhanced_json
            else:
                ai_duration = time.time() - ai_start_time
                print(f"❌ [AI_CALL_ERROR] No response received from Gemini AI after {ai_duration:.2f}s")
                print("🎯 [AI_CALL_EXIT] Questions extraction failed - no response")
                return "[]"

        except Exception as api_error:
            ai_duration = time.time() - ai_start_time
            print(f"❌ [AI_CALL_ERROR] Error calling Gemini AI API after {ai_duration:.2f}s: {api_error}")
            print(f"🎯 [AI_CALL_EXIT] Questions extraction failed with exception")
            return "[]"

    def _add_image_data_to_questions(self, json_content):
        """
        Process the AI response JSON and add image data locally by matching image IDs
        referenced in questions with extracted image data.

        Args:
            json_content (str): Raw JSON string from AI model

        Returns:
            str: Enhanced JSON string with image data added
        """
        try:
            import json

            print("🔍 Attempting to parse AI response as JSON...")
            print(f"📄 Raw response preview (first 200 chars): {json_content[:200]}...")

            # Clean the response - remove any markdown formatting or extra text
            cleaned_content = json_content.strip()

            # Remove markdown code block markers if present
            if cleaned_content.startswith('```json'):
                cleaned_content = cleaned_content[7:]  # Remove ```json
            if cleaned_content.startswith('```'):
                cleaned_content = cleaned_content[3:]   # Remove ```
            if cleaned_content.endswith('```'):
                cleaned_content = cleaned_content[:-3]  # Remove trailing ```

            cleaned_content = cleaned_content.strip()

            # Try to find JSON array in the response
            start_idx = cleaned_content.find('[')
            end_idx = cleaned_content.rfind(']')

            if start_idx == -1 or end_idx == -1:
                print("❌ No JSON array found in response")
                return json_content

            # Extract just the JSON part
            json_part = cleaned_content[start_idx:end_idx+1]
            print(f"📄 Extracted JSON part (first 200 chars): {json_part[:200]}...")

            # Fix LaTeX escaping issues in JSON
            safe_json_part = self._fix_latex_escaping_in_json(json_part)

            # Parse the JSON response
            questions = json.loads(safe_json_part)

            if not isinstance(questions, list):
                print("⚠️ Response is not a list, returning as-is")
                return json_content

            print(f"✅ Successfully parsed {len(questions)} questions")
            print(f"🔍 Processing questions to add image data and validate structure...")

            # Process each question
            for i, question in enumerate(questions):
                if not isinstance(question, dict):
                    print(f"⚠️ Question {i+1} is not a dictionary, skipping")
                    continue

                # Process images in question text and create imageUrl parameter
                self._process_question_images(question, i+1)

                # Process images in options - embed directly as option values
                self._process_option_images(question, i+1)

                # Process solution steps if they exist
                solution = question.get('solution', {})
                if isinstance(solution, dict) and 'steps' in solution:
                    steps = solution.get('steps', [])
                    if isinstance(steps, list):
                        for j, step in enumerate(steps):
                            if isinstance(step, str):
                                # Process images in solution steps
                                processed_step = self._process_image_references_in_text(step)
                                solution['steps'][j] = processed_step

                # Process hints if they exist
                hints = question.get('hints', [])
                if isinstance(hints, list):
                    for j, hint in enumerate(hints):
                        if isinstance(hint, str):
                            # Process images in hints
                            processed_hint = self._process_image_references_in_text(hint)
                            question['hints'][j] = processed_hint

                # Log what was found for this question
                has_solution = bool(solution.get('steps'))
                has_hints = bool(hints)
                print(f"📝 Question {i+1}: Solution={'✓' if has_solution else '✗'}, Hints={'✓' if has_hints else '✗'}")

            # Convert back to JSON string
            enhanced_json = json.dumps(questions, indent=2, ensure_ascii=False)
            print(f"✅ Enhanced JSON with image data created")

            return enhanced_json

        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"📄 Problematic content: {safe_json_part[:500]}...")
            # Attempt to salvage any individually valid question objects
            salvage_list = self._salvage_partial_json_array(safe_json_part)
            if salvage_list:
                print(f"✅ Salvaged {len(salvage_list)} valid question objects from partial JSON")
                # Re-run image processing on salvaged data
                return self._add_image_data_to_questions(json.dumps(salvage_list))
            print("📄 Returning original response")
            return json_content
        except Exception as e:
            print(f"❌ Error adding image data: {e}")
            print("📄 Returning original response")
            return json_content

    def _extract_answers_from_text(self, text):
        """
        Extract answers from OCR text by detecting answer key sections in the PDF itself.
        This method searches for answer key sections and extracts answers section-wise.

        Returns:
            dict: Dictionary mapping question numbers to their correct answers (a, b, c, d)
        """
        answers = {}

        # Keywords that indicate answer sections (expanded list for better detection)
        answer_keywords = [
            "ANSWER KEY", "ANSWERS", "SOLUTIONS", "ANSWER SHEET",
            ": ANSWER KEY :", "ANSWER", "KEY", "SOLUTION", "ANS",
            "CORRECT ANSWERS", "SOLUTION KEY", "ANSWER GUIDE",
            "ANSWER SECTION", "SECTION ANSWERS", "ANSWER CHOICES",
            "CORRECT RESPONSES", "ANSWER BANK", "RESPONSE KEY"
        ]

        # Split text into lines for analysis
        lines = text.split('\n')

        # Find ALL answer sections (not just the first one)
        answer_sections = []
        for i, line in enumerate(lines):
            line_upper = line.upper().strip()
            for keyword in answer_keywords:
                if keyword.upper() in line_upper and ("ANSWER" in keyword.upper() or "KEY" in keyword.upper() or "SOLUTION" in keyword.upper()):
                    answer_sections.append((i, line.strip()))
                    print(f"Found answer section at line {i}: {line.strip()}")
                    break

        if not answer_sections:
            print("No answer section found in OCR text")
            return answers

        # Process each answer section (section-wise extraction)
        for section_start, section_title in answer_sections:
            # Determine section end (next answer section or end of document)
            section_end = len(lines)
            for next_section_start, _ in answer_sections:
                if next_section_start > section_start:
                    section_end = next_section_start
                    break

            answer_section_text = '\n'.join(lines[section_start:section_end])
            print(f"Processing answer section: {section_title}")
            print(f"Section range: lines {section_start} to {section_end}")

            # Enhanced answer patterns for better section-wise detection
            patterns = [
                # Table format: | 1) | a | 2) | c |
                r'\|\s*(\d+)\)\s*\|\s*([a-d])\s*\|',
                # Table format without parentheses: | 1 | a | 2 | c |
                r'\|\s*(\d+)\s*\|\s*([a-d])\s*\|',
                # Simple format: 1. a, 2. b
                r'\b(\d+)\.\s*([a-d])\b',
                # Parentheses format: 1) a, 2) b
                r'\b(\d+)\)\s*([a-d])\b',
                # Space format: 1 a, 2 b
                r'\b(\d+)\s+([a-d])\b',
                # Format with (letter): 1 (a), 2 (b)
                r'\b(\d+)\s*\(([a-d])\)',
                # Colon format: 1: a, 2: b
                r'\b(\d+):\s*([a-d])\b',
                # Dash format: 1 - a, 2 - b
                r'\b(\d+)\s*-\s*([a-d])\b',
                # Equal format: 1 = a, 2 = b
                r'\b(\d+)\s*=\s*([a-d])\b',
                # Arrow format: 1 -> a, 2 -> b
                r'\b(\d+)\s*->\s*([a-d])\b',
                # Bracket format: [1] a, [2] b
                r'\[(\d+)\]\s*([a-d])\b',
            ]

            section_answers_found = 0
            section_answers = {}

            for pattern in patterns:
                matches = re.findall(pattern, answer_section_text, re.IGNORECASE)
                if matches:
                    print(f"Found {len(matches)} answers in this section using pattern: {pattern}")
                    for question_num, answer_choice in matches:
                        question_num = int(question_num)
                        if question_num not in answers:  # Don't overwrite existing answers from previous sections
                            answers[question_num] = answer_choice.upper()
                            section_answers[question_num] = answer_choice.upper()
                            section_answers_found += 1

                    # Log section-specific answers found
                    if section_answers:
                        print(f"Section '{section_title}' answers: {dict(list(section_answers.items())[:5])}...")
                    break

            if section_answers_found == 0:
                print(f"No answers found in section: {section_title}")
                # Try to extract any text that might contain answers for debugging
                section_preview = answer_section_text[:200].replace('\n', ' ')
                print(f"Section preview: {section_preview}...")

        # Final summary of extracted answers
        print(f"\n=== ANSWER EXTRACTION SUMMARY ===")
        print(f"Total answer sections found: {len(answer_sections)}")
        print(f"Total answers extracted from PDF: {len(answers)}")
        if answers:
            print(f"Answer range: Q{min(answers.keys())} to Q{max(answers.keys())}")
            print(f"Sample answers: {dict(list(answers.items())[:10])}...")
        else:
            print("WARNING: No answers were extracted from the PDF. Check if answer key sections exist.")
        print(f"=== END SUMMARY ===\n")

        return answers

    def save_questions_as_json(self, json_content, output_path="extracted_questions.json"):
        """Save raw JSON content to file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_content)
            print(f"✅ Raw JSON saved to file: {output_path}")
            return True
        except Exception as e:
            print(f"❌ Error saving JSON file: {e}")
            return False

    def _clean_json_response(self, response):
        """Clean AI response to extract valid JSON."""
        import re

        # Remove markdown formatting
        cleaned = response.strip()
        if '```json' in cleaned:
            start = cleaned.find('```json') + 7
            end = cleaned.rfind('```')
            if end > start:
                cleaned = cleaned[start:end].strip()
        elif '```' in cleaned:
            start = cleaned.find('```') + 3
            end = cleaned.rfind('```')
            if end > start:
                cleaned = cleaned[start:end].strip()

        # Find JSON array boundaries
        start_idx = cleaned.find('[')
        end_idx = cleaned.rfind(']')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            json_part = cleaned[start_idx:end_idx+1]
            return self._fix_latex_escaping_in_json(json_part)

        return cleaned

    def _fix_latex_escaping_in_json(self, json_text):
        """Fix LaTeX escaping issues in JSON text to prevent parsing errors."""
        import re

        # First, handle common LaTeX commands that get double-escaped
        # Replace \\\ with \\ (triple backslash to double)
        fixed_text = re.sub(r'\\\\\\', r'\\\\', json_text)

        # Handle specific LaTeX commands that cause JSON parsing issues
        latex_fixes = {
            r'\\\\cap': r'\\cap',
            r'\\\\cup': r'\\cup',
            r'\\\\prime': r'\\prime',
            r'\\\\phi': r'\\phi',
            r'\\\\geq': r'\\geq',
            r'\\\\leq': r'\\leq',
            r'\\\\neq': r'\\neq',
            r'\\\\in': r'\\in',
            r'\\\\subset': r'\\subset',
            r'\\\\supset': r'\\supset',
            r'\\\\emptyset': r'\\emptyset',
            r'\\\\infty': r'\\infty',
            r'\\\\sum': r'\\sum',
            r'\\\\prod': r'\\prod',
            r'\\\\int': r'\\int',
            r'\\\\frac': r'\\frac',
            r'\\\\sqrt': r'\\sqrt',
            r'\\\\alpha': r'\\alpha',
            r'\\\\beta': r'\\beta',
            r'\\\\gamma': r'\\gamma',
            r'\\\\delta': r'\\delta',
            r'\\\\theta': r'\\theta',
            r'\\\\lambda': r'\\lambda',
            r'\\\\mu': r'\\mu',
            r'\\\\pi': r'\\pi',
            r'\\\\sigma': r'\\sigma',
            r'\\\\omega': r'\\omega'
        }

        for wrong, correct in latex_fixes.items():
            fixed_text = re.sub(wrong, correct, fixed_text)

        # Handle any remaining problematic backslashes that aren't valid JSON escapes
        # Only escape backslashes that are not followed by valid JSON escape characters
        fixed_text = re.sub(r'\\(?![\\/"bfnrtu])', r'\\\\', fixed_text)

        return fixed_text

    def _salvage_partial_json_array(self, json_text):
        """Attempt to extract and parse any valid JSON objects from a broken JSON array string."""
        import json
        decoder = json.JSONDecoder()
        idx = 0
        length = len(json_text)
        items = []
        while idx < length:
            try:
                obj, end = decoder.raw_decode(json_text, idx)
                if isinstance(obj, dict):
                    items.append(obj)
                idx = end
            except json.JSONDecodeError:
                idx += 1  # move forward and try again
        return items

    # REMOVED: extract_solutions_from_pdf method - redundant since extract_questions_from_pdf
    # already extracts comprehensive data including solutions, answers, and hints in a single AI call

    # REMOVED: _add_image_data_to_solutions method - no longer needed since solutions
    # are processed within the comprehensive _add_image_data_to_questions method

    def _process_image_references(self, text):
        """
        Process text to replace image references with actual image data.

        Args:
            text (str): Text that may contain image references

        Returns:
            str: Text with image references processed
        """
        if not text or not hasattr(self, '_current_images'):
            return text

        # Find image references in format ![img-X.jpeg](img-X.jpeg)
        image_pattern = r'!\[([^\]]+)\]\(([^)]+)\)'

        def replace_image(match):
            alt_text = match.group(1)
            image_ref = match.group(2)

            # Extract image ID from reference
            image_id = image_ref.replace('.jpeg', '').replace('.jpg', '').replace('.png', '')

            if image_id in self._current_images:
                return f"![{alt_text}]({image_ref})"
            else:
                return f"![{alt_text}]({image_ref})"

        return re.sub(image_pattern, replace_image, text)

    def _find_matching_image(self, target_img_id):
        """
        Find matching image data for a given image ID, trying different formats.

        Args:
            target_img_id (str): The image ID to find (e.g., "img-19.jpeg")

        Returns:
            str: Base64 image data if found, None otherwise
        """
        if not self._current_images:
            return None

        # Try exact match first
        if target_img_id in self._current_images:
            return self._current_images[target_img_id]

        # Try different variations of the image ID
        variations = [
            target_img_id,
            target_img_id.replace('.jpeg', ''),
            target_img_id.replace('img-', ''),
            target_img_id.replace('img-', '').replace('.jpeg', ''),
            f"image-{target_img_id.replace('img-', '').replace('.jpeg', '')}",
            f"img_{target_img_id.replace('img-', '').replace('.jpeg', '')}",
        ]

        # Also try with different extensions
        base_num = target_img_id.replace('img-', '').replace('.jpeg', '')
        if base_num.isdigit():
            variations.extend([
                f"img-{base_num}.jpg",
                f"img-{base_num}.png",
                f"image-{base_num}.jpeg",
                f"image-{base_num}.jpg",
                f"image-{base_num}.png",
                base_num,  # Just the number
            ])

        # Try each variation
        for variation in variations:
            if variation in self._current_images:
                print(f"🔍 [IMAGE_MATCH] Found image using variation '{variation}' for target '{target_img_id}'")
                return self._current_images[variation]

        return None

    def _process_question_images(self, question_obj, question_num):
        """
        Process images in question text and create imageUrl parameter with key-value pairs.

        Args:
            question_obj (dict): The question object
            question_num (int): Question number for logging
        """
        # First, check if AI incorrectly added imageUrl when there are no image references
        if 'imageUrl' in question_obj:
            if 'question' not in question_obj or not question_obj['question']:
                # Remove imageUrl if no question text
                del question_obj['imageUrl']
                print(f"🧹 Removed empty imageUrl from question {question_num} (no question text)")
            else:
                text = question_obj['question']
                import re
                # Check if question actually contains image references
                image_refs_in_text = re.findall(r'!\[img-\d+\.jpeg\]\(img-\d+\.jpeg\)', text)
                if not image_refs_in_text:
                    # Remove imageUrl if no actual image references in question text
                    del question_obj['imageUrl']
                    print(f"🧹 Removed imageUrl from question {question_num} (no image references in question text)")

        if 'question' in question_obj and question_obj['question']:
            text = question_obj['question']

            # Find image references in the text using regex
            # Look for patterns like ![img-0.jpeg](img-0.jpeg)
            import re
            image_refs = re.findall(r'img-(\d+)\.jpeg', text)

            if image_refs:
                print(f"📸 Question {question_num} references images: {image_refs}")

                # Create or update imageUrl parameter with key-value pairs
                # First, clear any existing imageUrl to start fresh
                question_obj['imageUrl'] = {}

                # Add image data ONLY for images actually referenced in question text
                unique_image_refs = list(set(image_refs))  # Remove duplicates
                for img_num in unique_image_refs:
                    img_id = f"img-{img_num}.jpeg"

                    # Check if we have this image in our extracted images
                    image_data = self._find_matching_image(img_id)
                    if image_data:
                        # Add to imageUrl dictionary
                        question_obj['imageUrl'][img_id] = image_data
                        print(f"✅ Added image data for {img_id} to question {question_num} imageUrl")
                    else:
                        print(f"⚠️ Image {img_id} referenced but not found in extracted images")
                        # List available images for debugging
                        if self._current_images:
                            print(f"📋 Available images: {list(self._current_images.keys())}")
                        else:
                            print("📋 No images available")

    def _process_option_images(self, question_obj, question_num):
        """
        Process images in answer options and embed image data directly as option values.

        Args:
            question_obj (dict): The question object
            question_num (int): Question number for logging
        """
        if 'options' in question_obj and isinstance(question_obj['options'], dict):
            options = question_obj['options']

            # Process each option
            for option_key, option_value in options.items():
                if isinstance(option_value, str):
                    import re

                    # Check for new placeholder format: IMAGE_PLACEHOLDER_img-X.jpeg
                    placeholder_match = re.search(r'IMAGE_PLACEHOLDER_(img-\d+\.jpeg)', option_value)
                    if placeholder_match:
                        img_id = placeholder_match.group(1)
                        print(f"📸 Question {question_num} option {option_key} has image placeholder: {img_id}")

                        # Replace placeholder with actual image data
                        image_data = self._find_matching_image(img_id)
                        if image_data:
                            question_obj['options'][option_key] = image_data
                            print(f"✅ Replaced option {option_key} placeholder with image data for {img_id} in question {question_num}")
                        else:
                            print(f"⚠️ Image {img_id} referenced in placeholder but not found in extracted images")
                            if self._current_images:
                                print(f"📋 Available images: {list(self._current_images.keys())}")
                                # Keep a descriptive placeholder for missing images
                                question_obj['options'][option_key] = f"[MISSING_IMAGE: {img_id}]"
                            else:
                                print("📋 No images available")
                                question_obj['options'][option_key] = "[NO_IMAGES_EXTRACTED]"
                        continue

                    # Also check for old format: direct image references
                    image_refs = re.findall(r'img-(\d+)\.jpeg', option_value)
                    if image_refs:
                        print(f"📸 Question {question_num} option {option_key} references images (old format): {image_refs}")

                        # For options, we replace the entire option value with the image data
                        # If multiple images, use the first one
                        img_num = image_refs[0]
                        img_id = f"img-{img_num}.jpeg"

                        # Check if we have this image in our extracted images
                        image_data = self._find_matching_image(img_id)
                        if image_data:
                            # Replace the entire option value with image data
                            question_obj['options'][option_key] = image_data
                            print(f"✅ Replaced option {option_key} with image data for {img_id} in question {question_num}")
                        else:
                            print(f"⚠️ Image {img_id} referenced in option {option_key} but not found in extracted images")
                            # List available images for debugging
                            if self._current_images:
                                print(f"📋 Available images: {list(self._current_images.keys())}")
                                question_obj['options'][option_key] = f"[MISSING_IMAGE: {img_id}]"
                            else:
                                print("📋 No images available")
                                question_obj['options'][option_key] = "[NO_IMAGES_EXTRACTED]"

    def _process_image_references_in_text(self, text):
        """
        Process image references in a text string and return the processed text.

        Args:
            text (str): Text that may contain image references

        Returns:
            str: Text with image references processed
        """
        if not text or not hasattr(self, '_current_images'):
            return text

        # Find image references in format ![img-X.jpeg](img-X.jpeg)
        import re
        image_pattern = r'!\[([^\]]+)\]\(([^)]+)\)'

        def replace_image(match):
            alt_text = match.group(1)
            image_ref = match.group(2)
            # For now, just return the original reference
            # In the future, this could be enhanced to embed actual image data
            return f"![{alt_text}]({image_ref})"

        return re.sub(image_pattern, replace_image, text)

    def extract_questions_with_json_output(self, pdf_path, json_output_path="extracted_questions.json"):
        """
        Extract questions with section-wise answer mapping and save JSON output.

        This method extracts questions from PDF and maps answers from answer key sections
        found within the PDF itself (not AI-generated answers). Returns raw JSON from model.

        Args:
            pdf_path (str): Path to the PDF file
            json_output_path (str): Output path for JSON file

        Returns:
            dict: Extraction results with raw JSON and file paths
        """
        # Extract questions directly as JSON string from model
        json_response = self.extract_questions_from_pdf(pdf_path)

        # Save the raw JSON response to file
        try:
            with open(json_output_path, 'w', encoding='utf-8') as f:
                f.write(json_response)
            print(f"✅ Raw JSON response saved to: {json_output_path}")
            json_saved = True
        except Exception as e:
            print(f"❌ Error saving JSON file: {e}")
            json_saved = False

        # Return the raw JSON and save status
        return {
            'json_response': json_response,
            'json_saved': json_saved,
            'json_path': json_output_path if json_saved else None
        }

    # REMOVED: extract_solutions_with_json_output method - no longer needed since
    # extract_questions_with_json_output now returns comprehensive data including solutions