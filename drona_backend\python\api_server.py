#!/usr/bin/env python3
"""
Simple Flask API Server for PDF Question Extraction using Google Gemini AI
Processes PDFs in real-time and returns JSON directly without saving to disk
"""

import os
import json
import tempfile
import re
import time
import sys
import logging
import uuid
# Removed ThreadPoolExecutor import - no longer needed due to optimization
from flask import Flask, request, jsonify
import traceback

from question_extractor import QuestionExtractor
from enhanced_question_extractor import EnhancedQuestionExtractor

# Configure logging to ensure output is visible
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

# Pre-compiled regex patterns for better performance
JSON_CLEANUP_PATTERNS = {
    'markdown_json': re.compile(r'^```json\s*', re.MULTILINE),
    'markdown_generic': re.compile(r'^```\s*', re.MULTILINE),
    'markdown_end': re.compile(r'\s*```$', re.MULTILINE),
    'json_array': re.compile(r'\[.*\]', re.DOTALL),
    'json_object': re.compile(r'\{.*\}', re.DOTALL)
}

app = Flask(__name__)

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 200 * 1024 * 1024  # 200MB max file size
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # Disable caching for development
ALLOWED_EXTENSIONS = {'pdf'}

# Initialize the extractor (will be created per request with specified AI provider)
def create_extractor(ai_provider='gemini', use_enhanced=True):
    """Create a QuestionExtractor instance with the specified AI provider"""
    try:
        if use_enhanced:
            # Try to create enhanced extractor first
            try:
                log_print(f"🔧 [EXTRACTOR] Attempting to create Enhanced extractor with {ai_provider}")
                return EnhancedQuestionExtractor(ai_provider=ai_provider)
            except Exception as enhanced_error:
                log_print(f"⚠️ [EXTRACTOR] Enhanced extractor failed: {enhanced_error}")
                log_print("🔄 [EXTRACTOR] Falling back to standard extractor...")

        # Fallback to standard extractor
        log_print(f"🔧 [EXTRACTOR] Creating standard extractor with {ai_provider}")
        return QuestionExtractor(ai_provider=ai_provider)
    except Exception as e:
        log_print(f"❌ Error initializing extraction service with {ai_provider}: {e}")
        return None

# Test initialization
try:
    test_extractor = create_extractor()
    log_print("✅ PDF extraction service initialized successfully")
    del test_extractor  # Clean up test instance
except Exception as e:
    log_print(f"❌ Error testing extraction service: {e}")

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def fast_json_validation(json_text):
    """
    Fast JSON validation using basic bracket counting.
    Fails fast on obviously malformed JSON without full parsing.
    """
    if not json_text:
        return False

    # Quick bracket balance check
    brackets = {'[': 0, '{': 0}
    for char in json_text:
        if char == '[':
            brackets['['] += 1
        elif char == ']':
            brackets['['] -= 1
            if brackets['['] < 0:
                return False
        elif char == '{':
            brackets['{'] += 1
        elif char == '}':
            brackets['{'] -= 1
            if brackets['{'] < 0:
                return False

    return brackets['['] == 0 and brackets['{'] == 0

def fix_json_formatting(json_text):
    """
    Attempt to fix common JSON formatting issues.
    """
    if not json_text:
        return None

    try:
        # First, try to parse as-is to see if it's already valid
        try:
            json.loads(json_text)
            print("✅ [JSON_FIX] JSON is already valid")
            return json_text
        except json.JSONDecodeError:
            pass  # Continue with fixing

        print(f"🔧 [JSON_FIX] Attempting to fix JSON (length: {len(json_text)})")
        print(f"📄 [JSON_FIX] First 200 chars: {json_text[:200]}")
        print(f"📄 [JSON_FIX] Last 200 chars: {json_text[-200:]}")

        # Remove LaTeX formatting that might break JSON
        fixed = json_text.replace('$', '')
        fixed = fixed.replace('\\Omega', 'Ω')
        fixed = fixed.replace('\\sqrt', '√')
        fixed = fixed.replace('\\pi', 'π')
        fixed = fixed.replace('\\omega', 'ω')

        # Fix common escape sequence issues
        fixed = re.sub(r'(?<!\\)\\(?![\\/"bfnrtu])', r'\\\\', fixed)

        # Remove any trailing commas before closing brackets
        fixed = re.sub(r',(\s*[}\]])', r'\1', fixed)

        # Try to ensure proper array structure
        fixed = fixed.strip()
        if not fixed.startswith('['):
            fixed = '[' + fixed
        if not fixed.endswith(']'):
            fixed = fixed + ']'

        # Test if the fix worked
        try:
            json.loads(fixed)
            print("✅ [JSON_FIX] JSON successfully fixed")
            return fixed
        except json.JSONDecodeError as e:
            print(f"❌ [JSON_FIX] Fix attempt failed: {e}")
            print(f"📍 [JSON_FIX] Error at position: {e.pos}")
            if e.pos < len(fixed):
                start = max(0, e.pos - 50)
                end = min(len(fixed), e.pos + 50)
                print(f"📄 [JSON_FIX] Error context: '{fixed[start:end]}'")
            return None

    except Exception as e:
        print(f"❌ [JSON_FIX_ERROR] Error fixing JSON: {e}")
        return None

def extract_json_from_response(response_text):
    """
    Extract JSON from response text that might contain extra text or formatting.
    Optimized with pre-compiled regex patterns and fast validation for better performance.
    """
    if not response_text:
        return []

    # Clean the response text using pre-compiled regex patterns
    cleaned_content = response_text.strip()

    # Remove markdown code block markers using regex
    cleaned_content = JSON_CLEANUP_PATTERNS['markdown_json'].sub('', cleaned_content)
    cleaned_content = JSON_CLEANUP_PATTERNS['markdown_generic'].sub('', cleaned_content)
    cleaned_content = JSON_CLEANUP_PATTERNS['markdown_end'].sub('', cleaned_content)
    cleaned_content = cleaned_content.strip()

    # Try to extract JSON using regex patterns (faster than string operations)
    json_match = JSON_CLEANUP_PATTERNS['json_array'].search(cleaned_content)
    if not json_match:
        json_match = JSON_CLEANUP_PATTERNS['json_object'].search(cleaned_content)

    if not json_match:
        print(f"❌ No JSON found in response: {cleaned_content[:200]}...")
        return []

    json_part = json_match.group(0)

    # Escape lone backslashes (e.g., "\\geq") that break JSON parsing
    # Only backslashes that are NOT part of a valid JSON escape sequence are doubled.
    safe_json_part = re.sub(r'(?<!\\)\\(?![\\/"bfnrtu])', r'\\\\', json_part)

    # Try to parse JSON directly first, use fast validation as backup
    try:
        # Test if JSON is already valid by trying to parse it
        test_parse = json.loads(safe_json_part)
        print(f"✅ [JSON_VALIDATION] JSON is already valid (type: {type(test_parse)})")
    except json.JSONDecodeError:
        # JSON is invalid, try fast validation and fixing
        print(f"❌ [JSON_VALIDATION] JSON is invalid, attempting to fix...")

        if not fast_json_validation(safe_json_part):
            print(f"❌ JSON failed fast validation: {safe_json_part[:200]}...")

        print("🔧 [JSON_FIX] Attempting to fix JSON formatting...")
        fixed_json = fix_json_formatting(safe_json_part)

        if fixed_json:
            try:
                # Test if the fix worked by parsing
                json.loads(fixed_json)
                print("✅ [JSON_FIX] JSON formatting fixed successfully")
                safe_json_part = fixed_json
            except json.JSONDecodeError:
                print("❌ [JSON_FIX] Could not fix JSON formatting")
                return []
        else:
            print("❌ [JSON_FIX] Could not fix JSON formatting")
            return []

    try:
        # Parse the JSON response
        print(f"🔄 [JSON_PARSE] Attempting to parse JSON (length: {len(safe_json_part)})")
        print(f"📄 [JSON_PARSE] First 300 chars: {safe_json_part[:300]}")
        print(f"📄 [JSON_PARSE] Last 300 chars: {safe_json_part[-300:]}")

        parsed_data = json.loads(safe_json_part)

        print(f"✅ [JSON_PARSE_SUCCESS] Successfully parsed JSON")
        print(f"📊 [JSON_PARSE_SUCCESS] Type: {type(parsed_data)}")

        # Ensure we return a list
        if isinstance(parsed_data, dict):
            print(f"🔄 [JSON_PARSE] Converting single dict to list")
            return [parsed_data]
        elif isinstance(parsed_data, list):
            print(f"✅ [JSON_PARSE] Got list with {len(parsed_data)} items")

            # Validate structure of first item
            if len(parsed_data) > 0:
                first_item = parsed_data[0]
                if isinstance(first_item, dict):
                    print(f"📊 [JSON_STRUCTURE] First item keys: {list(first_item.keys())}")
                    required_keys = ['question', 'options', 'answer']
                    missing_keys = [key for key in required_keys if key not in first_item]
                    if missing_keys:
                        print(f"⚠️ [JSON_STRUCTURE] Missing required keys: {missing_keys}")
                    else:
                        print(f"✅ [JSON_STRUCTURE] All required keys present")
                else:
                    print(f"⚠️ [JSON_STRUCTURE] First item is not a dict: {type(first_item)}")

            return parsed_data
        else:
            print(f"⚠️ [JSON_PARSE] Unexpected JSON type: {type(parsed_data)}")
            return []

    except json.JSONDecodeError as e:
        print(f"❌ [JSON_PARSE_ERROR] JSON parsing error: {e}")
        print(f"📍 [JSON_PARSE_ERROR] Error at position: {e.pos}")
        print(f"📄 [JSON_PARSE_ERROR] Problematic JSON part: {safe_json_part[:500]}...")

        # Show context around error position
        if hasattr(e, 'pos') and e.pos < len(safe_json_part):
            start = max(0, e.pos - 100)
            end = min(len(safe_json_part), e.pos + 100)
            print(f"📄 [JSON_PARSE_ERROR] Context around error:")
            print(f"'{safe_json_part[start:end]}'")

        raise
    except Exception as e:
        print(f"❌ [JSON_PARSE_ERROR] Unexpected parsing error: {e}")
        import traceback
        traceback.print_exc()
        raise

# REMOVED: combine_questions_and_solutions function - no longer needed since
# the comprehensive extraction already returns data in the correct format

def extract_comprehensive_data(extractor, temp_path, ocr_data):
    """
    Extract comprehensive questions data including solutions and hints in a single AI call.
    This eliminates redundant AI calls since extract_structured_questions_from_pdf already extracts
    questions, answers, solutions, and hints in one comprehensive request.
    """
    try:
        print("🚀 [COMPREHENSIVE_START] Starting optimized single-call extraction...")
        start_time = time.time()

        # Single comprehensive extraction call that gets everything
        print("📄 [COMPREHENSIVE_EXTRACTION] Extracting questions with solutions and hints...")
        comprehensive_response = extractor.extract_structured_questions_from_pdf(temp_path, ocr_data)

        extraction_duration = time.time() - start_time
        response_size = len(comprehensive_response) if comprehensive_response else 0
        print(f"✅ [COMPREHENSIVE_SUCCESS] Comprehensive extraction completed in {extraction_duration:.2f}s")
        print(f"📊 [COMPREHENSIVE_RESULT] Response size: {response_size:,} characters")
        print("🎯 [OPTIMIZATION_NOTE] Eliminated redundant AI call - solutions already included in questions response")

        # Return the comprehensive response as both questions and solutions
        # since the single call contains all the data needed
        return comprehensive_response, comprehensive_response

    except Exception as extraction_error:
        extraction_duration = time.time() - start_time
        print(f"❌ [COMPREHENSIVE_FAILED] Comprehensive extraction failed after {extraction_duration:.2f}s: {extraction_error}")
        raise extraction_error

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint with extraction capabilities"""
    # Check available extraction methods
    try:
        test_extractor = create_extractor('gemini', use_enhanced=True)
        if hasattr(test_extractor, 'get_extraction_capabilities'):
            capabilities = test_extractor.get_extraction_capabilities()
        else:
            capabilities = {'adobe_pdf_extract': False, 'mistral_ocr': True, 'gemini_ai': True}
        del test_extractor  # Clean up
    except:
        capabilities = {'adobe_pdf_extract': False, 'mistral_ocr': True, 'gemini_ai': True}

    return jsonify({
        'status': 'healthy',
        'service': 'Enhanced PDF Question Extraction API',
        'version': '3.0.0',
        'ai_providers': {
            'primary_analysis': 'Google Gemini AI',
            'ocr_fallback': 'Mistral AI',
            'image_extraction': 'Adobe PDF Extract API' if capabilities.get('adobe_pdf_extract') else 'Mistral AI OCR'
        },
        'extraction_capabilities': capabilities,
        'endpoints': {
            'extract': '/api/extract',
            'health': '/'
        },
        'features': {
            'enhanced_extraction': capabilities.get('adobe_pdf_extract', False),
            'multi_provider_fallback': True,
            'large_file_optimization': True,
            'image_processing': True,
            'structured_pdf_support': True
        }
    })

@app.route('/api/extract', methods=['POST'])
def extract_questions_and_solutions():
    """
    Extract questions and solutions from uploaded PDF file
    Returns combined JSON with questions, solutions, and hints in the specified format

    Form parameters:
    - file: PDF file to process
    - ai_provider: AI provider to use ('mistral' or 'gemini', default: 'gemini')
    """
    log_print("🚀 [API_ENTRY] PDF extraction API endpoint called")

    # Get AI provider from form data (default to gemini)
    ai_provider = request.form.get('ai_provider', 'gemini').lower()
    if ai_provider not in ['mistral', 'gemini']:
        log_print(f"❌ [API_ERROR] Invalid AI provider: {ai_provider}")
        return jsonify({'error': 'Invalid AI provider. Use "mistral" or "gemini"'}), 400

    # Get extraction method preference (enhanced vs standard)
    use_enhanced = request.form.get('use_enhanced', 'true').lower() == 'true'
    log_print(f"🤖 [AI_PROVIDER] Using {ai_provider.upper()} AI for text analysis")
    log_print(f"🔧 [EXTRACTION_METHOD] Enhanced extraction: {'enabled' if use_enhanced else 'disabled'}")

    # Create extractor instance with specified AI provider
    extractor = create_extractor(ai_provider, use_enhanced)
    if not extractor:
        log_print("❌ [API_ERROR] Extraction service not available")
        return jsonify({'error': 'Extraction service not available'}), 500

    # Log extraction capabilities
    if hasattr(extractor, 'get_extraction_capabilities'):
        capabilities = extractor.get_extraction_capabilities()
        log_print(f"📊 [CAPABILITIES] Available extraction methods: {capabilities}")
    else:
        log_print("📊 [CAPABILITIES] Using standard extraction methods")

    try:
        # Check if file is present in request
        log_print("🔍 [API_VALIDATION] Validating request...")
        if 'file' not in request.files:
            log_print("❌ [API_ERROR] No file provided in request")
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            log_print("❌ [API_ERROR] No file selected")
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            log_print(f"❌ [API_ERROR] Invalid file type: {file.filename}")
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        log_print(f"✅ [API_VALIDATION] Request validated for file: {file.filename}")

        # Create optimized temporary file with proper buffering and ASCII-safe naming
        log_print("📁 [FILE_HANDLING_START] Creating temporary file...")
        file_start = time.time()

        # Sanitize filename to prevent encoding issues
        safe_filename = file.filename
        try:
            # Try to encode filename as ASCII to check for problematic characters
            safe_filename.encode('ascii')
        except UnicodeEncodeError:
            # If filename contains non-ASCII characters, create a safe version
            import unicodedata
            import string

            # Remove accents and normalize Unicode characters
            safe_filename = unicodedata.normalize('NFKD', safe_filename)
            safe_filename = safe_filename.encode('ascii', 'ignore').decode('ascii')

            # Keep only alphanumeric characters, dots, hyphens, and underscores
            safe_chars = string.ascii_letters + string.digits + '.-_'
            safe_filename = ''.join(c if c in safe_chars else '_' for c in safe_filename)

            # Ensure it ends with .pdf
            if not safe_filename.lower().endswith('.pdf'):
                safe_filename += '.pdf'

            log_print(f"⚠️ [FILENAME_SANITIZED] Original: '{file.filename}' -> Safe: '{safe_filename}'")

        # Create a completely ASCII-safe temporary file path
        temp_dir = tempfile.gettempdir()
        # Use UUID to ensure completely ASCII-safe filename
        safe_temp_name = f"pdf_extract_{uuid.uuid4().hex}.pdf"
        temp_path = os.path.join(temp_dir, safe_temp_name)

        # Save file with optimized chunk size for large files
        log_print("💾 [FILE_SAVE_START] Saving uploaded file to temporary location...")
        chunk_size = 8192  # 8KB chunks for efficient memory usage
        total_size = 0

        with open(temp_path, 'wb') as temp_file:
            # Stream the file in chunks to handle large files efficiently
            while True:
                chunk = file.stream.read(chunk_size)
                if not chunk:
                    break
                temp_file.write(chunk)
                total_size += len(chunk)

                # Log progress for very large files (every 10MB)
                if total_size % (10 * 1024 * 1024) == 0:
                    log_print(f"📊 [FILE_SAVE_PROGRESS] Saved {total_size // (1024 * 1024)}MB...")

        file_save_duration = time.time() - file_start
        log_print(f"✅ [FILE_SAVE_COMPLETE] File saved in {file_save_duration:.2f}s ({total_size // (1024 * 1024)}MB) to {temp_path}")

        try:
            # Start comprehensive performance tracking
            log_print("🚀 [EXTRACTION_START] Starting comprehensive PDF extraction...")

            # Pre-extraction analysis
            log_print("🔍 [PRE_ANALYSIS] Analyzing PDF before extraction...")
            file_size = os.path.getsize(temp_path)
            file_size_mb = file_size / (1024 * 1024)
            log_print(f"📊 [PRE_ANALYSIS] File size: {file_size_mb:.1f}MB ({file_size:,} bytes)")

            # Estimate processing time based on file size
            if file_size_mb > 100:
                estimated_time = "15-30 minutes"
            elif file_size_mb > 50:
                estimated_time = "10-20 minutes"
            elif file_size_mb > 20:
                estimated_time = "5-15 minutes"
            else:
                estimated_time = "2-10 minutes"

            log_print(f"⏱️ [PRE_ANALYSIS] Estimated processing time: {estimated_time}")

            # Start comprehensive performance tracking
            start_time = time.time()
            file_size_bytes = os.path.getsize(temp_path)
            file_size_mb = file_size_bytes / (1024 * 1024)

            metrics = {
                'file_size': file_size_bytes,
                'file_size_mb': file_size_mb,
                'filename': safe_filename,  # Use safe filename to avoid encoding issues
                'original_filename': file.filename,
                'start_time': start_time,
                'is_large_file': file_size_mb > 50  # Flag for large file processing
            }

            log_print(f"📊 Processing PDF: {safe_filename} ({file_size_mb:.1f}MB)")

            # Set expectations for large files
            if metrics['is_large_file']:
                log_print(f"⏳ [LARGE_FILE] Large file detected ({file_size_mb:.1f}MB). Processing may take 5-15 minutes...")
                log_print("🔄 [LARGE_FILE] Using optimized processing with progress tracking...")
            else:
                log_print(f"⚡ [NORMAL_FILE] Standard file size ({file_size_mb:.1f}MB). Expected processing time: 1-3 minutes...")

            # Check if we're using enhanced extractor
            if hasattr(extractor, 'extract_comprehensive_data_enhanced'):
                # Use enhanced extraction method
                log_print("🚀 [ENHANCED] Using enhanced comprehensive extraction...")
                comprehensive_start = time.time()
                enhanced_result = extractor.extract_comprehensive_data_enhanced(temp_path)
                metrics['extraction_duration'] = time.time() - comprehensive_start

                # Extract data from enhanced result
                questions_response = enhanced_result.get('questions', '[]')
                solutions_response = questions_response  # Enhanced method returns comprehensive data

                # Update metrics with enhanced data
                extraction_metadata = enhanced_result.get('extraction_metadata', {})
                metrics['adobe_used'] = extraction_metadata.get('adobe_used', False)
                metrics['mistral_used'] = extraction_metadata.get('mistral_used', False)
                metrics['extraction_method'] = extraction_metadata.get('extraction_method', 'enhanced')

                log_print(f"📊 [ENHANCED] Enhanced extraction completed in {metrics['extraction_duration']:.2f}s")
                log_print(f"📊 [ENHANCED] Adobe used: {metrics['adobe_used']}, Mistral used: {metrics['mistral_used']}")

            else:
                # Use standard extraction method
                log_print("🔍 [STANDARD] Using standard extraction method...")

                # Extract OCR data once for both questions and solutions (MAJOR OPTIMIZATION)
                ocr_start = time.time()
                log_print("🔍 Performing OCR extraction (once for both questions and solutions)...")
                ocr_data = extractor.extract_ocr_data_from_pdf(temp_path)
                metrics['ocr_duration'] = time.time() - ocr_start

                # Monitor OCR data size
                metrics['ocr_text_size'] = len(ocr_data['full_text'])
                metrics['ocr_images_count'] = len(ocr_data['all_images'])
                log_print(f"📊 OCR completed in {metrics['ocr_duration']:.2f}s: {metrics['ocr_text_size']:,} chars, {metrics['ocr_images_count']} images")

                # Use optimized single-call extraction (eliminates redundant AI calls)
                ai_start = time.time()
                log_print("🚀 Starting optimized single-call AI extraction...")
                questions_response, solutions_response = extract_comprehensive_data(
                    extractor, temp_path, ocr_data
                )
                metrics['ai_duration'] = time.time() - ai_start

            # Monitor response sizes
            metrics['questions_size'] = len(questions_response) if questions_response else 0
            metrics['solutions_size'] = len(solutions_response) if solutions_response else 0

            # Log AI extraction completion (handle both enhanced and standard extraction)
            ai_duration = metrics.get('ai_duration', metrics.get('extraction_duration', 0))
            log_print(f"📊 AI extraction completed in {ai_duration:.2f}s: Q={metrics['questions_size']:,} chars, S={metrics['solutions_size']:,} chars")

            # Parse comprehensive response (contains questions, answers, solutions, and hints)
            log_print("🔄 [JSON_PROCESSING_START] Starting JSON parsing of comprehensive response...")
            json_start = time.time()
            try:
                log_print("📄 [JSON_PARSE_COMPREHENSIVE] Parsing comprehensive JSON response...")
                parse_start = time.time()
                # Since both responses are identical (comprehensive data), we only need to parse once
                combined_data = extract_json_from_response(questions_response) if questions_response else []
                parse_duration = time.time() - parse_start
                log_print(f"✅ [JSON_PARSE_COMPREHENSIVE] Comprehensive data parsed in {parse_duration:.2f}s ({len(combined_data)} items)")
                log_print("🎯 [OPTIMIZATION_RESULT] No data combination needed - comprehensive response already contains all data")
                metrics['json_duration'] = time.time() - json_start
                metrics['total_duration'] = time.time() - start_time
                metrics['questions_count'] = len(combined_data)

                # Log comprehensive performance metrics
                print(f"📊 [JSON_PROCESSING_COMPLETE] JSON processing completed in {metrics['json_duration']:.2f}s")
                print(f"🎯 [TOTAL_COMPLETE] TOTAL PROCESSING TIME: {metrics['total_duration']:.2f}s")

                # Handle performance breakdown for both extraction methods
                if 'ai_duration' in metrics:
                    # Standard extraction
                    print(f"📈 [PERFORMANCE_BREAKDOWN] OCR={metrics.get('ocr_duration', 0):.1f}s, AI={metrics['ai_duration']:.1f}s, JSON={metrics['json_duration']:.1f}s")
                elif 'extraction_duration' in metrics:
                    # Enhanced extraction
                    print(f"📈 [PERFORMANCE_BREAKDOWN] Enhanced={metrics['extraction_duration']:.1f}s, JSON={metrics['json_duration']:.1f}s")
                    if metrics.get('adobe_used'):
                        print(f"📊 [EXTRACTION_DETAILS] Adobe PDF Extract API used successfully")
                    if metrics.get('mistral_used'):
                        print(f"📊 [EXTRACTION_DETAILS] Mistral AI OCR used as {'supplement' if metrics.get('adobe_used') else 'primary'}")
                else:
                    # Fallback
                    print(f"📈 [PERFORMANCE_BREAKDOWN] Total={metrics['total_duration']:.1f}s, JSON={metrics['json_duration']:.1f}s")

                print(f"📋 [FINAL_RESULT] {metrics['questions_count']} questions processed successfully")

                # Create final response
                print("🔄 [RESPONSE_CREATE_START] Creating final JSON response...")
                response_create_start = time.time()
                # Create performance metrics based on extraction method
                performance_metrics = {
                    'total_duration': round(metrics['total_duration'], 2),
                    'json_duration': round(metrics['json_duration'], 2),
                    'file_size_bytes': metrics['file_size']
                }

                # Add method-specific metrics
                if 'ai_duration' in metrics:
                    # Standard extraction metrics
                    performance_metrics.update({
                        'ocr_duration': round(metrics.get('ocr_duration', 0), 2),
                        'ai_duration': round(metrics['ai_duration'], 2),
                        'ocr_text_chars': metrics.get('ocr_text_size', 0),
                        'ocr_images': metrics.get('ocr_images_count', 0),
                        'extraction_method': 'standard'
                    })
                elif 'extraction_duration' in metrics:
                    # Enhanced extraction metrics
                    performance_metrics.update({
                        'extraction_duration': round(metrics['extraction_duration'], 2),
                        'adobe_used': metrics.get('adobe_used', False),
                        'mistral_used': metrics.get('mistral_used', False),
                        'extraction_method': metrics.get('extraction_method', 'enhanced')
                    })

                final_response = {
                    'status': 'success',
                    'filename': safe_filename,
                    'original_filename': metrics['original_filename'],
                    'questions_count': len(combined_data),
                    'data': combined_data,
                    'performance_metrics': performance_metrics
                }
                response_create_duration = time.time() - response_create_start
                print(f"✅ [RESPONSE_CREATE_COMPLETE] Response created in {response_create_duration:.3f}s")
                print(f"🚀 [REQUEST_COMPLETE] Request processing completed successfully!")

                return jsonify(final_response)

            except json.JSONDecodeError as e:
                # JSON parsing error - structured error response
                error_metrics = {
                    'error_type': 'JSON_PARSING_ERROR',
                    'error_phase': 'json_processing',
                    'processing_time': time.time() - start_time,
                    'file_size': metrics.get('file_size', 0),
                    'extraction_method': metrics.get('extraction_method', 'unknown'),
                    'ai_response_received': bool(questions_response)
                }

                # Add method-specific error context
                if 'ocr_duration' in metrics:
                    error_metrics['ocr_success'] = bool(metrics.get('ocr_text_size', 0) > 0)
                if 'adobe_used' in metrics:
                    error_metrics['adobe_used'] = metrics['adobe_used']
                print(f"❌ JSON parsing error after {error_metrics['processing_time']:.2f}s: {str(e)}")

                return jsonify({
                    'status': 'json_parsing_error',
                    'filename': safe_filename,
                    'original_filename': metrics['original_filename'],
                    'error': f'JSON parsing failed: {str(e)}',
                    'error_metrics': error_metrics,
                    'raw_response': questions_response[:500] + "..." if len(questions_response) > 500 else questions_response
                })
            except Exception as e:
                # General processing error - structured error response
                error_metrics = {
                    'error_type': 'PROCESSING_ERROR',
                    'error_phase': 'data_combination',
                    'processing_time': time.time() - start_time,
                    'file_size': metrics.get('file_size', 0),
                    'extraction_method': metrics.get('extraction_method', 'unknown'),
                    'comprehensive_response_available': bool(questions_response)
                }
                print(f"❌ Processing error after {error_metrics['processing_time']:.2f}s: {str(e)}")

                return jsonify({
                    'status': 'processing_error',
                    'filename': safe_filename,
                    'original_filename': metrics['original_filename'],
                    'error': f'Processing failed: {str(e)}',
                    'error_metrics': error_metrics
                })

        finally:
            # Clean up temporary file
            print("🧹 [CLEANUP_START] Cleaning up temporary files...")
            cleanup_start = time.time()
            if os.path.exists(temp_path):
                os.remove(temp_path)
                cleanup_duration = time.time() - cleanup_start
                print(f"✅ [CLEANUP_COMPLETE] Temporary file removed in {cleanup_duration:.3f}s")
            else:
                print("⚠️ [CLEANUP_WARNING] Temporary file not found for cleanup")

    except Exception as e:
        # Top-level error handling with comprehensive logging
        error_duration = time.time() - start_time if 'start_time' in locals() else 0
        # Get safe filename for error reporting
        safe_error_filename = 'unknown'
        original_error_filename = 'unknown'
        if 'file' in locals() and file:
            try:
                safe_error_filename = file.filename
                safe_error_filename.encode('ascii')
            except (UnicodeEncodeError, AttributeError):
                original_error_filename = file.filename if hasattr(file, 'filename') else 'unknown'
                safe_error_filename = original_error_filename.encode('ascii', 'ignore').decode('ascii') if original_error_filename != 'unknown' else 'unknown'
                if not safe_error_filename:
                    safe_error_filename = 'uploaded_file.pdf'

        error_metrics = {
            'error_type': 'SYSTEM_ERROR',
            'error_phase': 'request_processing',
            'processing_time': error_duration,
            'file_size': os.path.getsize(temp_path) if 'temp_path' in locals() and os.path.exists(temp_path) else 0,
            'filename': safe_error_filename,
            'original_filename': original_error_filename
        }

        # Handle encoding in error messages
        error_str = str(e)
        try:
            error_str.encode('ascii')
        except UnicodeEncodeError:
            error_str = error_str.encode('ascii', 'ignore').decode('ascii')

        print(f"❌ System error after {error_duration:.2f}s: {error_str}")
        print(f"📊 Error context: {error_metrics}")
        traceback.print_exc()

        return jsonify({
            'status': 'system_error',
            'error': f'Processing failed: {error_str}',
            'error_metrics': error_metrics
        }), 500





@app.errorhandler(413)
def too_large(_):
    """Handle file too large error"""
    return jsonify({
        'error': 'File too large. Maximum size is 200MB',
        'max_size_mb': 200,
        'suggestion': 'Please split large PDFs into smaller files or compress the PDF before uploading'
    }), 413

@app.errorhandler(404)
def not_found(_):
    """Handle 404 errors"""
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(_):
    """Handle internal server errors"""
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Ensure output is not buffered and set UTF-8 encoding
    os.environ['PYTHONUNBUFFERED'] = '1'
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    # Set locale to UTF-8 if possible
    try:
        import locale
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass  # Use system default

    log_print("🚀 Starting PDF Question Extraction API Server with Google Gemini AI...")
    log_print("📋 Available endpoints:")
    log_print("  GET  /                     - Health check")
    log_print("  POST /api/extract          - Extract questions and solutions from PDF")
    log_print("\n🔧 Server configuration:")
    log_print(f"  Max file size: {app.config['MAX_CONTENT_LENGTH'] // (1024*1024)}MB")
    log_print(f"  Allowed extensions: {ALLOWED_EXTENSIONS}")
    log_print("  Default AI provider: Google Gemini AI")
    log_print("  Fallback AI provider: Mistral AI (for OCR)")
    log_print("  Large file optimization: Enabled (chunked processing)")
    log_print("  Memory management: Automatic garbage collection for large documents")
    log_print("  Timeout settings: Extended for large file processing")
    log_print("  Note: Combined JSON with questions, solutions, and hints returned directly")
    log_print("  Logging: Real-time with immediate flush enabled")

    # Run the Flask development server with threaded mode for better logging
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True, use_reloader=True)
