#!/usr/bin/env python3
"""
Image-Enhanced Question Extractor
Combines superior image extraction with question extraction for optimal results
"""

import os
import sys
import json
import time
from typing import Dict, List, Optional

# Import existing extractors
from question_extractor import QuestionExtractor
from superior_image_extractor import SuperiorImageExtractor

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class ImageEnhancedExtractor:
    """
    Enhanced question extractor with superior image processing capabilities
    """
    
    def __init__(self, ai_provider='gemini', adobe_credentials=None):
        """
        Initialize Image-Enhanced Extractor
        
        Args:
            ai_provider (str): AI provider for text analysis
            adobe_credentials (str): Path to Adobe credentials file
        """
        self.ai_provider = ai_provider
        self.adobe_credentials = adobe_credentials
        
        # Initialize extractors
        self.question_extractor = QuestionExtractor(ai_provider=ai_provider)
        self.image_extractor = SuperiorImageExtractor(adobe_credentials=adobe_credentials)
        
        # Storage for extracted data
        self.extracted_images = {}
        self.image_mapping = {}
        
        log_print("✅ [INIT] Image-Enhanced Extractor initialized")
    
    def extract_questions_with_superior_images(self, pdf_path: str) -> Dict:
        """
        Extract questions with superior image processing
        
        Args:
            pdf_path (str): Path to PDF file
            
        Returns:
            Dict: Enhanced extraction results with superior images
        """
        try:
            log_print("🚀 [IMAGE_ENHANCED] Starting image-enhanced extraction...")
            start_time = time.time()
            
            # Step 1: Extract images using superior method
            log_print("🖼️ [STEP_1] Extracting images with superior method...")
            image_result = self.image_extractor.extract_all_images(pdf_path)
            
            self.extracted_images = image_result.get('images', {})
            self.image_mapping = image_result.get('image_mapping', {})
            
            log_print(f"✅ [STEP_1] Extracted {len(self.extracted_images)} high-quality images")
            
            # Step 2: Extract questions using standard method
            log_print("📝 [STEP_2] Extracting questions...")
            questions_json = self.question_extractor.extract_structured_questions_from_pdf(pdf_path)
            
            try:
                questions = json.loads(questions_json) if questions_json else []
            except json.JSONDecodeError:
                log_print("⚠️ [STEP_2] JSON parsing failed, trying to clean...")
                questions = self._parse_and_clean_questions(questions_json)
            
            log_print(f"✅ [STEP_2] Extracted {len(questions)} questions")
            
            # Step 3: Enhanced image-question matching
            log_print("🔗 [STEP_3] Performing enhanced image-question matching...")
            enhanced_questions = self._enhance_questions_with_images(questions)
            
            log_print(f"✅ [STEP_3] Enhanced {len(enhanced_questions)} questions with images")
            
            # Step 4: Quality validation and improvement
            log_print("✨ [STEP_4] Validating and improving question quality...")
            validated_questions = self._validate_and_improve_questions(enhanced_questions)
            
            total_duration = time.time() - start_time
            log_print(f"🎯 [IMAGE_ENHANCED] Image-enhanced extraction completed in {total_duration:.2f}s")
            
            return {
                'questions': json.dumps(validated_questions, indent=2),
                'extraction_metadata': {
                    'total_duration': total_duration,
                    'extraction_method': 'image_enhanced',
                    'total_questions': len(validated_questions),
                    'total_images': len(self.extracted_images),
                    'questions_with_images': sum(1 for q in validated_questions if q.get('imageUrl')),
                    'image_sources': list(set(img.get('source', 'unknown') for img in self.extracted_images.values()))
                }
            }
            
        except Exception as e:
            log_print(f"❌ [IMAGE_ENHANCED] Image-enhanced extraction failed: {e}")
            # Fallback to standard extraction
            return self._fallback_extraction(pdf_path)
    
    def _enhance_questions_with_images(self, questions: List[Dict]) -> List[Dict]:
        """Enhance questions with superior image matching"""
        try:
            enhanced_questions = []
            
            for i, question in enumerate(questions):
                try:
                    enhanced_q = question.copy()
                    question_num = i + 1
                    
                    # Get question text for context
                    question_text = question.get('content', '') or question.get('question', '')
                    
                    # Find best images for this question
                    matching_images = self.image_extractor.find_images_for_question(
                        question_num, question_text
                    )
                    
                    if matching_images:
                        # Add images to question
                        enhanced_q['imageUrl'] = {}
                        
                        for j, img_match in enumerate(matching_images):
                            img_key = f"img_{question_num}_{j+1}"
                            enhanced_q['imageUrl'][img_key] = img_match['data']
                            
                            log_print(f"🖼️ [MATCH] Added image to question {question_num} "
                                    f"(confidence: {img_match['confidence']:.2f}, "
                                    f"source: {img_match['source']})")
                    
                    # Also check for images in options
                    enhanced_q = self._enhance_options_with_images(enhanced_q, question_num)
                    
                    enhanced_questions.append(enhanced_q)
                    
                except Exception as e:
                    log_print(f"⚠️ [ENHANCE] Error enhancing question {i+1}: {e}")
                    enhanced_questions.append(question)
            
            return enhanced_questions
            
        except Exception as e:
            log_print(f"❌ [ENHANCE] Question enhancement failed: {e}")
            return questions
    
    def _enhance_options_with_images(self, question: Dict, question_num: int) -> Dict:
        """Enhance question options with images"""
        try:
            if 'options' not in question:
                return question
            
            options = question['options']
            if not isinstance(options, dict):
                return question
            
            enhanced_options = {}
            
            for option_key, option_value in options.items():
                if isinstance(option_value, str):
                    # Check if option references an image
                    if 'img' in option_value.lower() or 'image' in option_value.lower():
                        # Try to find matching image
                        matching_images = self.image_extractor.find_images_for_question(
                            question_num, option_value
                        )
                        
                        if matching_images:
                            # Replace option text with image
                            enhanced_options[option_key] = matching_images[0]['data']
                            log_print(f"🖼️ [OPTION] Replaced option {option_key} in question {question_num} with image")
                        else:
                            enhanced_options[option_key] = option_value
                    else:
                        enhanced_options[option_key] = option_value
                else:
                    enhanced_options[option_key] = option_value
            
            question['options'] = enhanced_options
            return question
            
        except Exception as e:
            log_print(f"⚠️ [OPTIONS] Error enhancing options for question {question_num}: {e}")
            return question
    
    def _validate_and_improve_questions(self, questions: List[Dict]) -> List[Dict]:
        """Validate and improve question quality"""
        try:
            validated_questions = []
            
            for i, question in enumerate(questions):
                try:
                    # Normalize question structure
                    normalized_q = self._normalize_question_structure(question)
                    
                    # Validate question
                    if self._is_valid_question(normalized_q):
                        # Improve question formatting
                        improved_q = self._improve_question_formatting(normalized_q)
                        validated_questions.append(improved_q)
                    else:
                        log_print(f"⚠️ [VALIDATE] Question {i+1} failed validation")
                        
                except Exception as e:
                    log_print(f"⚠️ [VALIDATE] Error validating question {i+1}: {e}")
            
            log_print(f"✅ [VALIDATE] Validated {len(validated_questions)} questions")
            return validated_questions
            
        except Exception as e:
            log_print(f"❌ [VALIDATE] Validation failed: {e}")
            return questions
    
    def _normalize_question_structure(self, question: Dict) -> Dict:
        """Normalize question structure"""
        normalized = {}
        
        # Handle content field
        content = question.get('content') or question.get('question') or question.get('text', '')
        normalized['content'] = content.strip()
        
        # Handle options
        options = question.get('options', {})
        if isinstance(options, dict):
            normalized['options'] = options
        elif isinstance(options, list):
            # Convert list to dict
            option_dict = {}
            for i, opt in enumerate(options[:4]):
                option_dict[chr(65 + i)] = str(opt)  # A, B, C, D
            normalized['options'] = option_dict
        else:
            normalized['options'] = {'A': '', 'B': '', 'C': '', 'D': ''}
        
        # Handle other fields
        normalized['answer'] = question.get('answer', 'A')
        normalized['type'] = question.get('type', 'mcq')
        normalized['difficulty'] = question.get('difficulty', 'medium')
        
        # Preserve image data
        if 'imageUrl' in question:
            normalized['imageUrl'] = question['imageUrl']
        
        return normalized
    
    def _is_valid_question(self, question: Dict) -> bool:
        """Check if question is valid"""
        # Check content
        if not question.get('content') or len(question['content'].strip()) < 10:
            return False
        
        # Check options
        options = question.get('options', {})
        if not options or len(options) < 2:
            return False
        
        # Check if at least 2 options have content
        valid_options = sum(1 for opt in options.values() 
                          if opt and (len(str(opt).strip()) > 0))
        
        return valid_options >= 2
    
    def _improve_question_formatting(self, question: Dict) -> Dict:
        """Improve question formatting"""
        improved = question.copy()
        
        # Clean content
        improved['content'] = self._clean_text(improved['content'])
        
        # Clean options
        if isinstance(improved['options'], dict):
            cleaned_options = {}
            for key, value in improved['options'].items():
                if isinstance(value, str):
                    cleaned_options[key] = self._clean_text(value)
                else:
                    cleaned_options[key] = value  # Keep image data as-is
            improved['options'] = cleaned_options
        
        # Add metadata
        improved['id'] = f"q_{hash(improved['content'])}"
        improved['extraction_method'] = 'image_enhanced'
        
        return improved
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ''
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Remove common OCR artifacts
        import re
        text = re.sub(r'[^\w\s\.\?\!\,\;\:\(\)\[\]\-\+\=\*\/\%\$\#\@\&]', '', text)
        
        return text.strip()
    
    def _parse_and_clean_questions(self, questions_json: str) -> List[Dict]:
        """Parse and clean malformed JSON"""
        try:
            # Try to extract valid JSON objects
            import re
            
            # Find all JSON-like objects
            json_objects = re.findall(r'\{[^{}]*\}', questions_json)
            
            questions = []
            for obj_str in json_objects:
                try:
                    obj = json.loads(obj_str)
                    if isinstance(obj, dict) and ('content' in obj or 'question' in obj):
                        questions.append(obj)
                except:
                    continue
            
            return questions
            
        except Exception as e:
            log_print(f"❌ [PARSE] JSON parsing failed: {e}")
            return []
    
    def _fallback_extraction(self, pdf_path: str) -> Dict:
        """Fallback extraction method"""
        try:
            log_print("🔄 [FALLBACK] Using fallback extraction...")
            
            questions_json = self.question_extractor.extract_questions_from_pdf_standard(pdf_path)
            questions = json.loads(questions_json) if questions_json else []
            
            return {
                'questions': json.dumps(questions, indent=2),
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'fallback',
                    'total_questions': len(questions),
                    'total_images': 0,
                    'questions_with_images': 0
                }
            }
            
        except Exception as e:
            log_print(f"❌ [FALLBACK] Fallback extraction failed: {e}")
            return {
                'questions': '[]',
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'failed',
                    'total_questions': 0,
                    'total_images': 0,
                    'questions_with_images': 0,
                    'error': str(e)
                }
            }
