#!/usr/bin/env python3
"""
Enhanced Question Extractor with Adobe PDF Extract API Integration
Combines Adobe PDF Extract API, Mistral AI OCR, and Gemini AI for optimal extraction
"""

import os
import json
import time
import sys
from typing import Dict, List, Optional, Tuple

from question_extractor import QuestionExtractor
from adobe_pdf_extractor import AdobePDFExtractor

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class EnhancedQuestionExtractor(QuestionExtractor):
    """
    Enhanced Question Extractor that combines multiple extraction methods:
    1. Adobe PDF Extract API (primary for images and structured text)
    2. Mistral AI OCR (fallback for text extraction)
    3. Gemini AI (primary for question analysis)
    """
    
    def __init__(self, ai_provider='gemini', api_key_file='key.txt', adobe_credentials_file=None):
        """
        Initialize Enhanced Question Extractor
        
        Args:
            ai_provider (str): AI provider for text analysis ('gemini' or 'mistral')
            api_key_file (str): Path to Mistral API key file
            adobe_credentials_file (str): Path to Adobe credentials JSON file
        """
        # Initialize parent class
        super().__init__(ai_provider, api_key_file)
        
        # Initialize Adobe PDF Extractor
        self.adobe_extractor = None
        self.adobe_credentials_file = adobe_credentials_file or 'pdfservices-api-credentials.json'
        
        # Try to initialize Adobe extractor
        self._initialize_adobe_extractor()
    
    def _initialize_adobe_extractor(self):
        """Initialize Adobe PDF Extractor if credentials are available"""
        try:
            if os.path.exists(self.adobe_credentials_file):
                log_print("🔧 [ENHANCED_INIT] Initializing Adobe PDF Extractor...")
                self.adobe_extractor = AdobePDFExtractor(self.adobe_credentials_file)
                log_print("✅ [ENHANCED_INIT] Adobe PDF Extractor initialized successfully")
            else:
                log_print(f"⚠️ [ENHANCED_INIT] Adobe credentials file not found: {self.adobe_credentials_file}")
                log_print("🔄 [ENHANCED_INIT] Will use Mistral OCR as fallback for image extraction")
        except Exception as e:
            log_print(f"❌ [ENHANCED_INIT] Failed to initialize Adobe PDF Extractor: {e}")
            log_print("🔄 [ENHANCED_INIT] Will use Mistral OCR as fallback")
            self.adobe_extractor = None
    
    def extract_comprehensive_data_enhanced(self, pdf_path: str) -> Dict:
        """
        Extract comprehensive data using the best available methods
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            Dict: Comprehensive extraction results
        """
        try:
            log_print("🚀 [ENHANCED_EXTRACT] Starting enhanced comprehensive extraction...")
            start_time = time.time()
            
            # Step 1: Try Adobe PDF Extract API first (best for images and structured text)
            adobe_data = None
            if self.adobe_extractor:
                try:
                    log_print("📄 [ENHANCED_EXTRACT] Attempting Adobe PDF extraction...")
                    adobe_start = time.time()
                    adobe_data = self.adobe_extractor.extract_images_and_text(pdf_path)
                    adobe_duration = time.time() - adobe_start
                    log_print(f"✅ [ENHANCED_EXTRACT] Adobe extraction completed in {adobe_duration:.2f}s")
                except Exception as e:
                    log_print(f"⚠️ [ENHANCED_EXTRACT] Adobe extraction failed: {e}")
                    log_print("🔄 [ENHANCED_EXTRACT] Falling back to Mistral OCR...")
            
            # Step 2: Use Mistral OCR as fallback or supplement
            mistral_data = None
            if not adobe_data or self._should_use_mistral_supplement(adobe_data):
                try:
                    log_print("📄 [ENHANCED_EXTRACT] Using Mistral OCR extraction...")
                    mistral_start = time.time()
                    mistral_data = self.extract_ocr_data_from_pdf(pdf_path)
                    mistral_duration = time.time() - mistral_start
                    log_print(f"✅ [ENHANCED_EXTRACT] Mistral OCR completed in {mistral_duration:.2f}s")
                except Exception as e:
                    log_print(f"❌ [ENHANCED_EXTRACT] Mistral OCR failed: {e}")
            
            # Step 3: Combine and optimize extraction data
            log_print("🔄 [ENHANCED_EXTRACT] Combining extraction results...")
            combined_data = self._combine_extraction_results(adobe_data, mistral_data)
            
            # Step 4: Extract questions using combined data
            log_print("🤖 [ENHANCED_EXTRACT] Analyzing combined data with AI...")
            questions_result = self._extract_questions_from_combined_data(pdf_path, combined_data)
            
            total_duration = time.time() - start_time
            log_print(f"🎯 [ENHANCED_EXTRACT] Enhanced extraction completed in {total_duration:.2f}s")
            
            return {
                'questions': questions_result,
                'extraction_metadata': {
                    'adobe_used': adobe_data is not None,
                    'mistral_used': mistral_data is not None,
                    'total_duration': total_duration,
                    'extraction_method': 'enhanced_multi_provider'
                },
                'raw_data': {
                    'adobe_data': adobe_data,
                    'mistral_data': mistral_data,
                    'combined_data': combined_data
                }
            }
            
        except Exception as e:
            log_print(f"❌ [ENHANCED_EXTRACT] Enhanced extraction failed: {e}")
            # Fallback to original extraction method
            log_print("🔄 [ENHANCED_EXTRACT] Falling back to original extraction method...")
            return self._fallback_extraction(pdf_path)
    
    def _should_use_mistral_supplement(self, adobe_data: Dict) -> bool:
        """
        Determine if Mistral OCR should be used to supplement Adobe extraction
        
        Args:
            adobe_data (Dict): Adobe extraction results
            
        Returns:
            bool: True if Mistral supplement is needed
        """
        if not adobe_data:
            return True
        
        # Check if Adobe extracted sufficient text
        text_length = len(adobe_data.get('text_content', ''))
        if text_length < 1000:  # Less than 1KB of text
            log_print("⚠️ [ENHANCED_EXTRACT] Adobe extracted minimal text, using Mistral supplement")
            return True
        
        # Check if Adobe extracted images
        images_count = len(adobe_data.get('images', {}))
        if images_count == 0:
            log_print("⚠️ [ENHANCED_EXTRACT] Adobe extracted no images, using Mistral supplement")
            return True
        
        return False
    
    def _combine_extraction_results(self, adobe_data: Dict, mistral_data: Dict) -> Dict:
        """
        Combine Adobe and Mistral extraction results optimally
        
        Args:
            adobe_data (Dict): Adobe extraction results
            mistral_data (Dict): Mistral extraction results
            
        Returns:
            Dict: Combined extraction results
        """
        combined = {
            'full_text': '',
            'all_images': {},
            'extraction_sources': []
        }
        
        # Combine text content
        if adobe_data and adobe_data.get('text_content'):
            combined['full_text'] = adobe_data['text_content']
            combined['extraction_sources'].append('adobe_text')
            log_print(f"📝 [COMBINE] Using Adobe text: {len(combined['full_text'])} characters")
        elif mistral_data and mistral_data.get('full_text'):
            combined['full_text'] = mistral_data['full_text']
            combined['extraction_sources'].append('mistral_text')
            log_print(f"📝 [COMBINE] Using Mistral text: {len(combined['full_text'])} characters")
        
        # Combine images - prefer Adobe images, supplement with Mistral
        if adobe_data and adobe_data.get('images'):
            combined['all_images'].update(adobe_data['images'])
            combined['extraction_sources'].append('adobe_images')
            log_print(f"🖼️ [COMBINE] Added {len(adobe_data['images'])} Adobe images")
        
        if mistral_data and mistral_data.get('all_images'):
            # Add Mistral images that don't conflict with Adobe images
            for img_id, img_data in mistral_data['all_images'].items():
                if img_id not in combined['all_images']:
                    combined['all_images'][img_id] = img_data
            combined['extraction_sources'].append('mistral_images')
            log_print(f"🖼️ [COMBINE] Added {len(mistral_data['all_images'])} Mistral images")
        
        log_print(f"✅ [COMBINE] Combined data: {len(combined['full_text'])} chars, {len(combined['all_images'])} images")
        log_print(f"📊 [COMBINE] Sources used: {', '.join(combined['extraction_sources'])}")
        
        return combined
    
    def _extract_questions_from_combined_data(self, pdf_path: str, combined_data: Dict) -> str:
        """
        Extract questions using the combined data from multiple sources
        
        Args:
            pdf_path (str): Path to the PDF file
            combined_data (Dict): Combined extraction data
            
        Returns:
            str: JSON string with extracted questions
        """
        try:
            # Store combined data for use in question extraction
            self.current_ocr_data = combined_data
            self._current_images = combined_data.get('all_images', {})
            
            # Use the enhanced structured extraction method
            log_print("🔄 [ENHANCED_QUESTIONS] Using structured extraction with combined data...")
            return self.extract_structured_questions_from_pdf(pdf_path, combined_data)
            
        except Exception as e:
            log_print(f"❌ [ENHANCED_QUESTIONS] Error extracting questions from combined data: {e}")
            # Fallback to standard extraction
            log_print("🔄 [ENHANCED_QUESTIONS] Falling back to standard extraction...")
            return self.extract_questions_from_pdf_standard(pdf_path, combined_data)
    
    def _fallback_extraction(self, pdf_path: str) -> Dict:
        """
        Fallback to original extraction method if enhanced extraction fails
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            Dict: Fallback extraction results
        """
        try:
            log_print("🔄 [FALLBACK] Using original extraction method...")
            start_time = time.time()
            
            # Use original structured extraction
            questions_result = self.extract_structured_questions_from_pdf(pdf_path)
            
            duration = time.time() - start_time
            log_print(f"✅ [FALLBACK] Fallback extraction completed in {duration:.2f}s")
            
            return {
                'questions': questions_result,
                'extraction_metadata': {
                    'adobe_used': False,
                    'mistral_used': True,
                    'total_duration': duration,
                    'extraction_method': 'fallback_original'
                },
                'raw_data': None
            }
            
        except Exception as e:
            log_print(f"❌ [FALLBACK] Fallback extraction also failed: {e}")
            raise e
    
    def get_extraction_capabilities(self) -> Dict:
        """
        Get information about available extraction capabilities
        
        Returns:
            Dict: Available extraction methods and their status
        """
        return {
            'adobe_pdf_extract': self.adobe_extractor is not None,
            'mistral_ocr': hasattr(self, 'client') and self.client is not None,
            'gemini_ai': hasattr(self, 'gemini_model') and self.gemini_model is not None,
            'ai_provider': self.ai_provider,
            'extraction_methods': [
                'adobe_pdf_extract' if self.adobe_extractor else None,
                'mistral_ocr',
                f'{self.ai_provider}_ai_analysis'
            ]
        }
