"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/ui/chemical-image-display.tsx":
/*!******************************************************!*\
  !*** ./src/components/ui/chemical-image-display.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChemicalImageDisplay: () => (/* binding */ ChemicalImageDisplay),\n/* harmony export */   ChemicalOptionDisplay: () => (/* binding */ ChemicalOptionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _base64_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _math_text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./math-text */ \"(app-pages-browser)/./src/components/ui/math-text.tsx\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/beaker.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChemicalImageDisplay(param) {\n    let { text, images = {}, className, imageClassName, maxImageWidth = 400, maxImageHeight = 300, showImageToggle = true } = param;\n    _s();\n    const [showImages, setShowImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedImages, setExpandedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Process text to handle chemical image placeholders\n    const processChemicalText = (inputText)=>{\n        if (!inputText) return {\n            cleanText: '',\n            imageRefs: []\n        };\n        let cleanText = inputText;\n        const imageRefs = [];\n        // Find chemical image placeholders like [CHEMICAL_IMAGE_1_question]\n        const chemicalImagePattern = /\\[CHEMICAL_IMAGE_(\\d+)_([^\\]]+)\\]/g;\n        let match;\n        while((match = chemicalImagePattern.exec(inputText)) !== null){\n            const [fullMatch, questionNum, context] = match;\n            const imageKey = \"chemical_img_\".concat(questionNum, \"_\").concat(context);\n            if (images[imageKey]) {\n                imageRefs.push(imageKey);\n                // Replace placeholder with a marker for rendering\n                cleanText = cleanText.replace(fullMatch, \"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"));\n            } else {\n                // Remove placeholder if no image found\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        }\n        // Also handle markdown image references that weren't converted\n        const markdownPattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n        cleanText = cleanText.replace(markdownPattern, (match, alt, src)=>{\n            // Try to find matching image\n            const matchingKey = Object.keys(images).find((key)=>key.includes(src) || src.includes(key.replace('chemical_img_', '')));\n            if (matchingKey) {\n                imageRefs.push(matchingKey);\n                return \"[IMAGE_PLACEHOLDER_\".concat(matchingKey, \"]\");\n            }\n            return \"[Missing Image: \".concat(src, \"]\");\n        });\n        return {\n            cleanText,\n            imageRefs: [\n                ...new Set(imageRefs)\n            ]\n        };\n    };\n    const { cleanText, imageRefs } = processChemicalText(text);\n    const hasImages = imageRefs.length > 0;\n    const toggleImageExpansion = (imageKey)=>{\n        setExpandedImages((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(imageKey)) {\n                newSet.delete(imageKey);\n            } else {\n                newSet.add(imageKey);\n            }\n            return newSet;\n        });\n    };\n    const renderTextWithImagePlaceholders = (text)=>{\n        if (!text) return null;\n        const parts = text.split(/(\\[IMAGE_PLACEHOLDER_[^\\]]+\\])/);\n        return parts.map((part, index)=>{\n            const placeholderMatch = part.match(/\\[IMAGE_PLACEHOLDER_([^\\]]+)\\]/);\n            if (placeholderMatch) {\n                const imageKey = placeholderMatch[1];\n                const imageData = images[imageKey];\n                if (imageData && showImages) {\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Chemical Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 p-2 bg-gray-100 border border-gray-200 rounded text-sm text-gray-600\",\n                    children: [\n                        \"[Chemical Structure - \",\n                        imageKey,\n                        \"]\"\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this);\n            }\n            return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_math_text__WEBPACK_IMPORTED_MODULE_4__.MathText, {\n                    text: part\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this) : null;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-3\", className),\n        children: [\n            hasImages && showImageToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            imageRefs.length,\n                            \" Chemical Structure\",\n                            imageRefs.length !== 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>setShowImages(!showImages),\n                        className: \"h-7 px-3 text-xs\",\n                        children: showImages ? 'Hide Images' : 'Show Images'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-base leading-relaxed\",\n                children: renderTextWithImagePlaceholders(cleanText)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            showImages && imageRefs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: imageRefs.map((imageKey)=>{\n                    // Skip if this image was already rendered inline\n                    if (cleanText.includes(\"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"))) {\n                        return null;\n                    }\n                    const imageData = images[imageKey];\n                    if (!imageData) return null;\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chemical Structure - \",\n                                            imageKey.replace('chemical_img_', '').replace(/_/g, ' ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, imageKey, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(ChemicalImageDisplay, \"WkjgmkVJW42NseduZzN6jEUfq8M=\");\n_c = ChemicalImageDisplay;\nfunction ChemicalOptionDisplay(param) {\n    let { option, images = {}, isCorrect = false, className } = param;\n    // If option has imageUrl as string, it might be a key to the images object\n    const imageData = option.imageUrl && typeof option.imageUrl === 'string' ? images[option.imageUrl] || option.imageUrl : option.imageUrl;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-start p-3 rounded-md border transition-colors\", isCorrect ? \"border-green-500 bg-green-50\" : \"border-gray-200 hover:border-blue-300\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: option.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChemicalImageDisplay, {\n                            text: option.text,\n                            images: images,\n                            maxImageWidth: 200,\n                            maxImageHeight: 150,\n                            showImageToggle: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: option.isImageOption ? \"\" : \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-50 border border-blue-200 rounded-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2 text-xs text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Chemical Structure Option\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                    src: imageData,\n                                    alt: \"Option \".concat(option.label, \" - Chemical Structure\"),\n                                    maxWidth: 200,\n                                    maxHeight: 150,\n                                    className: \"border border-blue-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this),\n                    option.isImageOption && !imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 italic text-sm\",\n                        children: \"Chemical structure option (image not available)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChemicalOptionDisplay;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChemicalImageDisplay\");\n$RefreshReg$(_c1, \"ChemicalOptionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\n"));

/***/ })

});