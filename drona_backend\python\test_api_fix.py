#!/usr/bin/env python3
"""
Test script to verify the API server fix for the ai_duration KeyError
"""

import os
import sys
import requests
import time

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_api_server_health():
    """Test if API server is running"""
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            log_print("✅ [HEALTH] API server is running")
            log_print(f"📊 [HEALTH] Service: {data.get('service', 'Unknown')}")
            log_print(f"📊 [HEALTH] Version: {data.get('version', 'Unknown')}")
            
            # Check extraction capabilities
            capabilities = data.get('extraction_capabilities', {})
            log_print(f"📊 [CAPABILITIES] Adobe PDF Extract: {capabilities.get('adobe_pdf_extract', False)}")
            log_print(f"📊 [CAPABILITIES] Mistral OCR: {capabilities.get('mistral_ocr', False)}")
            log_print(f"📊 [CAPABILITIES] Gemini AI: {capabilities.get('gemini_ai', False)}")
            
            return True
        else:
            log_print(f"❌ [HEALTH] API server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        log_print("❌ [HEALTH] Cannot connect to API server. Is it running?")
        log_print("💡 [HEALTH] Start with: python api_server.py")
        return False
    except Exception as e:
        log_print(f"❌ [HEALTH] Error checking API server: {e}")
        return False

def test_pdf_extraction(pdf_path=None):
    """Test PDF extraction with the API"""
    if not pdf_path:
        # Look for any PDF file in current directory
        pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
        if not pdf_files:
            log_print("⚠️ [TEST] No PDF files found for testing")
            log_print("💡 [TEST] Place a test PDF file in the current directory")
            return True  # Not a failure, just no test file
        
        pdf_path = pdf_files[0]
        log_print(f"📄 [TEST] Using test PDF: {pdf_path}")
    
    try:
        log_print("🚀 [TEST] Testing PDF extraction API...")
        
        # Test with enhanced extraction (default)
        log_print("🔄 [TEST] Testing enhanced extraction...")
        with open(pdf_path, 'rb') as f:
            files = {'file': f}
            data = {'use_enhanced': 'true'}
            
            start_time = time.time()
            response = requests.post('http://localhost:5000/api/extract', files=files, data=data, timeout=300)
            duration = time.time() - start_time
        
        log_print(f"📥 [TEST] Response received in {duration:.2f}s")
        log_print(f"📊 [TEST] Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            log_print("✅ [TEST] Enhanced extraction successful!")
            
            # Check response structure
            status = result.get('status')
            questions_count = result.get('questions_count', 0)
            performance_metrics = result.get('performance_metrics', {})
            
            log_print(f"📊 [RESULT] Status: {status}")
            log_print(f"📊 [RESULT] Questions extracted: {questions_count}")
            log_print(f"📊 [RESULT] Total duration: {performance_metrics.get('total_duration', 0):.2f}s")
            log_print(f"📊 [RESULT] Extraction method: {performance_metrics.get('extraction_method', 'unknown')}")
            
            # Check for Adobe usage
            if performance_metrics.get('adobe_used'):
                log_print("🎯 [RESULT] Adobe PDF Extract API was used")
            if performance_metrics.get('mistral_used'):
                log_print("🎯 [RESULT] Mistral AI OCR was used")
            
            return True
        else:
            log_print(f"❌ [TEST] Extraction failed with status {response.status_code}")
            try:
                error_data = response.json()
                log_print(f"📄 [ERROR] Error details: {error_data}")
            except:
                log_print(f"📄 [ERROR] Response text: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        log_print("❌ [TEST] Request timed out (>5 minutes)")
        return False
    except Exception as e:
        log_print(f"❌ [TEST] Error during extraction test: {e}")
        return False

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test API server fix for ai_duration KeyError")
    parser.add_argument("--pdf", help="Path to test PDF file", default=None)
    parser.add_argument("--health-only", action="store_true", help="Only test health endpoint")
    
    args = parser.parse_args()
    
    log_print("🧪 [TEST] Testing API server fix for ai_duration KeyError...")
    log_print("=" * 60)
    
    # Test 1: Health check
    log_print("\n🔍 [TEST] Step 1: API Server Health Check")
    health_ok = test_api_server_health()
    
    if not health_ok:
        log_print("❌ [TEST] Cannot proceed without running API server")
        return False
    
    if args.health_only:
        log_print("✅ [TEST] Health check passed. API server is ready.")
        return True
    
    # Test 2: PDF extraction
    log_print("\n🔍 [TEST] Step 2: PDF Extraction Test")
    extraction_ok = test_pdf_extraction(args.pdf)
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 [TEST] Test Results Summary:")
    log_print(f"  Health Check: {'✅ PASS' if health_ok else '❌ FAIL'}")
    log_print(f"  PDF Extraction: {'✅ PASS' if extraction_ok else '❌ FAIL'}")
    
    if health_ok and extraction_ok:
        log_print("\n🎉 [SUCCESS] All tests passed! The ai_duration KeyError has been fixed.")
        log_print("💡 [SUCCESS] Your API server is working correctly with both extraction methods.")
    else:
        log_print("\n⚠️ [PARTIAL] Some tests failed. Check the logs above for details.")
    
    return health_ok and extraction_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
