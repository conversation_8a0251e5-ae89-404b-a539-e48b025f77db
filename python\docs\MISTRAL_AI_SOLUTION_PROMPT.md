# Mistral AI Solution Extraction Prompt

## Complete Prompt Text

The following is the exact Mistral AI prompt used for solution extraction:

```
Please analyze the following text extracted from a PDF document and extract all detailed solution steps, procedures, and methodologies for questions.

Return the output as a valid JSON array where each solution is an object with this EXACT structure:

{
  "question_number": "1",
  "question_text": "Brief question text or reference - preserve any image references like ![img-X.jpeg](img-X.jpeg)",
  "solution_steps": [
    "Step 1: Detailed explanation of the first step",
    "Step 2: Detailed explanation of the second step",
    "Step 3: Continue with all solution steps..."
  ],
  "final_answer": "The final answer or conclusion",
  "methodology": "Brief description of the solution approach/method used",
  "key_concepts": ["concept1", "concept2", "concept3"]
}

IMPORTANT INSTRUCTIONS:
1. Look for solution sections, worked examples, detailed explanations, step-by-step procedures
2. Extract complete solution methodologies, not just final answers
3. Break down solutions into clear, sequential steps
4. Include mathematical derivations, chemical reactions, or procedural steps as appropriate
5. Preserve any image references in the format ![img-X.jpeg](img-X.jpeg)
6. Map solutions to their corresponding question numbers when possible
7. Include key concepts or principles used in each solution
8. If no solutions are found, return an empty array []
9. Ensure the JSON is valid and properly formatted
10. Focus on educational content that shows HOW to solve problems, not just answers

TEXT TO ANALYZE:
{full_text}
```

## Prompt Design Principles

### 1. Clear Structure Definition
- Specifies exact JSON schema expected
- Provides concrete examples of each field
- Ensures consistent output format

### 2. Educational Focus
- Emphasizes "HOW to solve" over just answers
- Requests step-by-step breakdowns
- Includes methodology and concept identification

### 3. Content Type Targeting
- Looks for solution sections and worked examples
- Targets detailed explanations and procedures
- Includes mathematical/scientific derivations

### 4. Technical Requirements
- Preserves image references
- Maintains question-to-solution mapping
- Ensures valid JSON output
- Handles edge cases (no solutions found)

### 5. Quality Assurance
- Requests complete methodologies
- Emphasizes clear, sequential steps
- Includes key concepts for context

## Integration with Existing System

### Code Location
File: `question_extractor.py`
Method: `extract_solutions_from_pdf()`
Lines: ~390-500

### API Call Configuration
```python
response = self.client.chat.complete(
    model="mistral-small-latest",
    messages=[
        {
            "role": "user",
            "content": solution_prompt
        }
    ]
)
```

### Data Flow
1. **PDF Input** → OCR Processing (same as questions)
2. **Text Extraction** → Full text from all pages
3. **Prompt Application** → Solution-specific prompt
4. **AI Processing** → Mistral AI analysis
5. **JSON Response** → Structured solution data
6. **Image Enhancement** → Add image data to solutions
7. **Output** → Enhanced JSON with solutions

## Comparison with Question Extraction Prompt

### Question Extraction Prompt (Existing)
- Focuses on multiple-choice questions and options
- Extracts answer choices (A, B, C, D)
- Maps answers from PDF answer keys
- Structured for quiz/test format

### Solution Extraction Prompt (New)
- Focuses on detailed solution procedures
- Extracts step-by-step methodologies
- Identifies key concepts and approaches
- Structured for educational/learning format

## Output Schema Comparison

### Question Schema
```json
{
  "question": "Question text",
  "options": {
    "A": "option a",
    "B": "option b", 
    "C": "option c",
    "D": "option d"
  },
  "answer": "A"
}
```

### Solution Schema
```json
{
  "question_number": "1",
  "question_text": "Question reference",
  "solution_steps": ["Step 1...", "Step 2..."],
  "final_answer": "Final result",
  "methodology": "Approach used",
  "key_concepts": ["concept1", "concept2"]
}
```

## Error Handling

The solution extraction includes robust error handling:

```python
try:
    response = self.client.chat.complete(...)
    if response and response.choices and len(response.choices) > 0:
        json_content = response.choices[0].message.content
        enhanced_json = self._add_image_data_to_solutions(json_content)
        return enhanced_json
    else:
        return "[]"
except Exception as api_error:
    print(f"❌ Error calling Mistral AI API: {api_error}")
    return "[]"
```

## Image Processing

Solutions maintain image references through:

1. **OCR Image Extraction** - Same as questions
2. **Reference Preservation** - Maintains `![img-X.jpeg](img-X.jpeg)` format
3. **Image Data Addition** - Adds base64 image data to JSON
4. **Cross-Reference Mapping** - Links image IDs to actual image data

## Usage in Main Parser

The solution extraction integrates seamlessly:

```python
# In PDFQuestionParser.process_pdf()
if extract_solutions:
    json_response = self.extractor.extract_solutions_from_pdf(pdf_path)
    content_type = "solutions"
    default_json_name = 'extracted_solutions.json'
else:
    json_response = self.extractor.extract_questions_from_pdf(pdf_path)
    content_type = "questions"
    default_json_name = 'extracted_questions.json'
```

## Command Line Integration

New CLI option added:
```bash
parser.add_argument('-s', '--solutions', action='store_true', 
                   help='Extract detailed solution steps and procedures instead of questions')
```

Usage:
```bash
python pdf_question_parser.py document.pdf -s -o solutions.json
```

## Quality Assurance Features

1. **JSON Validation** - Ensures valid JSON output
2. **Content Verification** - Checks for meaningful solution content
3. **Image Consistency** - Maintains image reference integrity
4. **Error Recovery** - Graceful handling of API failures
5. **Format Compliance** - Enforces consistent data structure

## Performance Considerations

- **Same OCR Cost** - Reuses existing OCR processing
- **Additional AI Call** - One extra Mistral AI chat completion
- **Memory Efficient** - Processes solutions in same memory footprint
- **Parallel Capable** - Can run alongside question extraction
- **Caching Friendly** - OCR results can be cached for both operations
