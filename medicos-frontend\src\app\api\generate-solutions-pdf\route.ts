import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

interface SolutionsPdfPayload {
  title: string;
  description: string;
  duration: number;
  totalMarks: number;
  questions: Array<{
    question: string;
    options: string[];
    answer: string;
    subject?: string;
    solution?: {
      final_explanation?: string;
      methodology?: string;
      steps?: string[];
    };
    hints?: string[];
  }>;
  filename?: string;
  collegeName?: string;
  collegeLogoUrl?: string;
}

// Function to process text for PDF generation - handles tables and images
function processTextForPDF(text: string): string {
  if (!text) return '';

  let processedText = text;

  // Skip base64 image processing here - it's handled separately in the main processing
  // This prevents double processing and conflicts with the main image handling logic

  // First, handle tables - convert markdown tables to HTML
  processedText = processedText.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g, (match) => {
    try {
      // Clean up malformed table syntax
      let cleaned = match.trim();
      cleaned = cleaned.replace(/<br\s*\/?>/gi, ' ');

      const lines = cleaned.split('\n').filter(line => line.trim());
      if (lines.length < 2) return match;

      // Parse table structure
      const tableLines = [];
      let hasHeader = false;

      for (const line of lines) {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);

        if (cells.length === 0) continue;

        // Check if this is a separator line
        if (cells.every(cell => cell.match(/^:?-+:?$/))) {
          hasHeader = true;
          continue;
        }

        tableLines.push(cells);
      }

      if (tableLines.length === 0) return match;

      // Generate HTML table
      let html = '<table>';

      if (hasHeader && tableLines.length > 0) {
        html += '<thead><tr>';
        for (const cell of tableLines[0]) {
          html += `<th>${cell}</th>`;
        }
        html += '</tr></thead>';

        if (tableLines.length > 1) {
          html += '<tbody>';
          for (let i = 1; i < tableLines.length; i++) {
            html += '<tr>';
            for (const cell of tableLines[i]) {
              html += `<td>${cell}</td>`;
            }
            html += '</tr>';
          }
          html += '</tbody>';
        }
      } else {
        html += '<tbody>';
        for (const row of tableLines) {
          html += '<tr>';
          for (const cell of row) {
            html += `<td>${cell}</td>`;
          }
          html += '</tr>';
        }
        html += '</tbody>';
      }

      html += '</table>';
      return html;
    } catch (error) {
      console.warn('Error processing table:', error);
      return match;
    }
  });

  return processedText;
}

export const POST = async (req: NextRequest) => {
  try {
    const payload = (await req.json()) as SolutionsPdfPayload;
    console.log('Solutions PDF API called with payload:', payload);

    const {
      title,
      description,
      duration,
      totalMarks,
      questions,
      filename = 'question-paper-solutions.pdf',
      collegeName = '',
      collegeLogoUrl = '',
    } = payload;

    const html = `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${title} - Solutions</title>
  <link href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.renderMathInElement) {
        window.renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ]
        });
      }
    });
  </script>
  <style>
    @page { size: A4; margin: 20mm 15mm; }
    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }
    h1,h2,h3 { margin: 0; padding: 0; }
    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }
    /* Watermark */
    body::before {
      content: 'MEDICOS';
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 96pt;
      font-weight: bold;
      color: rgba(0,128,0,0.08); /* greenish */
      z-index: 0;
      pointer-events: none;
    }
    /* Header / Footer */
    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .college { display: flex; align-items: center; gap: 6px; }
    .college img { height: 32px; width: auto; }
    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }
    .meta { text-align: right; font-size: 10pt; }
    .meta div { margin: 0; }

    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }
    .question { break-inside: avoid; margin-bottom: 20px; page-break-inside: avoid; }
    .question-content { margin-bottom: 8px; }
    .options { margin-left: 12px; margin-bottom: 8px; }
    .options p { margin: 2px 0; }
    .answer { margin-top: 8px; padding: 6px 10px; background-color: #f0f8ff; border-left: 4px solid #2563eb; border-radius: 3px; }
    .answer-text { font-weight: bold; color: #000; font-size: 10pt; }
    .solution { margin-top: 10px; padding: 8px 12px; background-color: #f9f9f9; border-left: 4px solid #10b981; border-radius: 3px; }
    .solution-title { font-weight: bold; color: #059669; margin-bottom: 6px; font-size: 10pt; }
    .solution-content { font-size: 9pt; line-height: 1.4; }
    .solution-content p { margin: 4px 0; }
    .solution-content ol { margin: 4px 0; padding-left: 16px; }
    .solution-content li { margin: 2px 0; }
    .hints { margin-top: 10px; padding: 8px 12px; background-color: #fef3c7; border-left: 4px solid #f59e0b; border-radius: 3px; }
    .hints-title { font-weight: bold; color: #d97706; margin-bottom: 6px; font-size: 10pt; }
    .hint-item { margin: 3px 0; font-size: 9pt; line-height: 1.3; }
    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }
    .subject-heading { font-weight: bold; margin: 12px 0 8px; font-size: 12pt; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px; }
    /* Table styling for proper rendering */
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 8px 0;
      font-size: 9pt;
      break-inside: avoid;
    }
    th, td {
      border: 1px solid #333;
      padding: 4px 6px;
      text-align: left;
      vertical-align: top;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    /* Math rendering support */
    .katex {
      font-size: 1em;
    }
    .katex-display {
      margin: 0.3em 0;
    }
  </style>
</head>
<body>
  <header>
    <div class="college">
      ${collegeLogoUrl ? `<img src="${collegeLogoUrl}" alt="logo" />` : ''}
      <span>${collegeName}</span>
    </div>
    <div class="title">${title} - Solutions</div>
    <div class="meta">
      <div>Total Marks: ${totalMarks}</div>
      <div>Duration: ${duration} mins</div>
    </div>
  </header>
  <hr />
  <p>${description}</p>
  <div class="questions">
    ${questions.reduce((acc, q, idx) => {
      // Check if this is a new subject
      const isNewSubject = q.subject && (idx === 0 || questions[idx-1].subject !== q.subject);
      
      // Calculate question number within current subject
      let questionNumber = 1;
      if (!isNewSubject) {
        // Count questions in current subject up to this point
        const currentSubject = q.subject;
        for (let i = idx - 1; i >= 0; i--) {
          if (questions[i].subject === currentSubject) {
            questionNumber++;
          } else {
            break;
          }
        }
      }
      
      const heading = isNewSubject
        ? `<div class="subject-heading">Subject: ${q.subject}</div>`
        : '';
      
      // Find the correct option letter for the answer
      const answerIndex = q.options.findIndex(opt => opt === q.answer);
      const answerLetter = answerIndex !== -1 ? String.fromCharCode(97 + answerIndex) : q.answer;

      // Build solution section
      let solutionHtml = '';
      if (q.solution) {
        const solutionParts = [];
        if (q.solution.final_explanation) {
          const processedExplanation = processTextForPDF(q.solution.final_explanation);
          solutionParts.push(`<p><strong>Explanation:</strong> ${processedExplanation}</p>`);
        }
        if (q.solution.methodology) {
          const processedMethodology = processTextForPDF(q.solution.methodology);
          solutionParts.push(`<p><strong>Method:</strong> ${processedMethodology}</p>`);
        }
        if (q.solution.steps && q.solution.steps.length > 0) {
          const processedSteps = q.solution.steps.map(step => processTextForPDF(step));
          solutionParts.push(`<p><strong>Steps:</strong></p><ol>${processedSteps.map(step => `<li>${step}</li>`).join('')}</ol>`);
        }

        if (solutionParts.length > 0) {
          solutionHtml = `
            <div class="solution">
              <div class="solution-title">Solution:</div>
              <div class="solution-content">
                ${solutionParts.join('')}
              </div>
            </div>`;
        }
      }

      // Build hints section
      let hintsHtml = '';
      if (q.hints && q.hints.length > 0) {
        const processedHints = q.hints.map(hint => processTextForPDF(hint));
        hintsHtml = `
          <div class="hints">
            <div class="hints-title">Hints:</div>
            ${processedHints.map((hint, i) => `<div class="hint-item">${i + 1}. ${hint}</div>`).join('')}
          </div>`;
      }

      // Process question text and handle images from imageUrls array
      let questionText = q.question;

      // Check for images in imageUrls array (new database structure)
      const imageUrls = (q as any).imageUrls || [];
      if (imageUrls && imageUrls.length > 0) {
        // Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images
        questionText = questionText.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt) => {
          // Use the first available image since backend extracts images in order
          return `<img src="${imageUrls[0]}" alt="${alt || 'Question Image'}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
        });

        // Replace HTML img tags with actual images
        questionText = questionText.replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi, () => {
          // Use the first available image
          return `<img src="${imageUrls[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
        });

        // If content mentions images but no image tags found, append the first image
        if (!questionText.includes('<img') && imageUrls.length > 0) {
          const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(questionText);
          if (hasImageKeywords) {
            questionText += `\n<img src="${imageUrls[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
          }
        }
      }

      // Fallback: Check for legacy imageData or chemicalImages fields
      const imageData = (q as any).imageData || (q as any).chemicalImages;
      if (imageData && typeof imageData === 'object' && !questionText.includes('<img')) {
        // If we have image data but no images in question text, add them
        const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');
        if (!hasImagesInText) {
          // Add the first available image to the question text
          const firstImageKey = Object.keys(imageData)[0];
          if (firstImageKey && imageData[firstImageKey]) {
            questionText = questionText + '\n' + imageData[firstImageKey];
          }
        }
      }

      // Process question text with tables, images, and LaTeX
      let processedQuestion = processTextForPDF(questionText);

      // Apply image processing after table processing
      processedQuestion = processedQuestion
        // Handle markdown images only (base64 images already processed by processTextForPDF)
        .replace(/!\[([^\]]*)\]\(data:image\/([^;]+);base64,([^)]+)\)/g,
          '<img src="data:image/$2;base64,$3" alt="$1" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />')
        // Handle image references from imageData field
        .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt, src) => {
          // Try to find matching image in question's imageData
          const imageData = (q as any).imageData || (q as any).chemicalImages;
          if (imageData && typeof imageData === 'object') {
            // Debug: Looking for image match

            // Try multiple matching strategies
            let imageKey = null;

            // Strategy 1: Exact match
            if (imageData[src]) {
              imageKey = src;
            }
            // Strategy 2: Try without extension
            else {
              const srcWithoutExt = src.replace(/\.(jpeg|jpg|png)$/i, '');
              imageKey = Object.keys(imageData).find(key =>
                key.includes(srcWithoutExt) ||
                key.replace(/\.(jpeg|jpg|png)$/i, '') === srcWithoutExt
              );
            }
            // Strategy 3: Try partial matches
            if (!imageKey) {
              imageKey = Object.keys(imageData).find(key =>
                key.includes(src) || src.includes(key)
              );
            }
            // Strategy 4: Extract numbers and match
            if (!imageKey) {
              const srcNumbers = src.match(/\d+/g);
              if (srcNumbers) {
                imageKey = Object.keys(imageData).find(key =>
                  srcNumbers.some((num: string) => key.includes(num))
                );
              }
            }

            if (imageKey && imageData[imageKey]) {
              return `<img src="${imageData[imageKey]}" alt="${alt}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
            } else {
              // No matching image found
            }
          }
          return `[Missing Image: ${src}]`;
        })
        // Remove broken image references
        .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
        // Remove standalone base64 strings
        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');

      const processedOptions = q.options.map(opt => {
        // Process option text with tables first
        let processedOpt = processTextForPDF(opt);

        // Apply image processing after table processing
        return processedOpt
          // Handle markdown images only (base64 images already processed by processTextForPDF)
          .replace(/!\[([^\]]*)\]\(data:image\/([^;]+);base64,([^)]+)\)/g,
            '<img src="data:image/$2;base64,$3" alt="$1" style="max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;" />')
          // Handle image references from imageData field
          .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt, src) => {
            // Try to find matching image in question's imageData
            const imageData = (q as any).imageData || (q as any).chemicalImages;
            if (imageData && typeof imageData === 'object') {
              // Try multiple matching strategies
              let imageKey = null;

              // Strategy 1: Exact match
              if (imageData[src]) {
                imageKey = src;
              }
              // Strategy 2: Try without extension
              else {
                const srcWithoutExt = src.replace(/\.(jpeg|jpg|png)$/i, '');
                imageKey = Object.keys(imageData).find(key =>
                  key.includes(srcWithoutExt) ||
                  key.replace(/\.(jpeg|jpg|png)$/i, '') === srcWithoutExt
                );
              }
              // Strategy 3: Try partial matches
              if (!imageKey) {
                imageKey = Object.keys(imageData).find(key =>
                  key.includes(src) || src.includes(key)
                );
              }
              // Strategy 4: Extract numbers and match
              if (!imageKey) {
                const srcNumbers = src.match(/\d+/g);
                if (srcNumbers) {
                  imageKey = Object.keys(imageData).find(key =>
                    srcNumbers.some((num: string) => key.includes(num))
                  );
                }
              }

              if (imageKey && imageData[imageKey]) {
                return `<img src="${imageData[imageKey]}" alt="${alt}" style="max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;" />`;
              }
            }
            return `[Missing Image: ${src}]`;
          })
          // Remove broken image references
          .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
          // Remove standalone base64 strings
          .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');
      });

      const qHtml = `
        <div class="question">
          <div class="question-content">
            <p><strong>${questionNumber}.</strong> ${processedQuestion}</p>
          </div>
          <div class="options">
            ${processedOptions.map((opt, i) => `<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}
          </div>
          <div class="answer">
            <p class="answer-text">Answer: ${answerLetter})</p>
          </div>
          ${solutionHtml}
          ${hintsHtml}
        </div>`;
      return acc + heading + qHtml;
    }, '')}
  </div>
  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>
</body>
</html>`;

    console.log('Launching Puppeteer for solutions PDF generation...');
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();

    console.log('Setting HTML content for solutions PDF...');
    await page.setContent(html, { waitUntil: 'networkidle0' });

    // Wait until KaTeX has rendered math. Small delay to be safe.
    await page.waitForFunction(() => {
      return Array.from(document.querySelectorAll('.katex')).length > 0;
    }, { timeout: 3000 }).catch(() => {});

    // Extra small delay to ensure layout settles
    await new Promise(resolve => setTimeout(resolve, 200));

    console.log('Generating solutions PDF...');
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20mm', right: '15mm', bottom: '20mm', left: '15mm' },
    });

    await browser.close();
    console.log('Solutions PDF generated successfully, size:', pdfBuffer.length, 'bytes');

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error: any) {
    console.error('Solutions PDF generation failed:', error);
    return new NextResponse(JSON.stringify({ error: 'Solutions PDF generation failed' }), { status: 500 });
  }
};
