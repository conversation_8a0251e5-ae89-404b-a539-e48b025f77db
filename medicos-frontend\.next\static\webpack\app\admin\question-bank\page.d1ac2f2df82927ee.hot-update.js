"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-list.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,ChevronUp,Edit,Lightbulb,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/text-with-images */ \"(app-pages-browser)/./src/components/ui/text-with-images.tsx\");\n/* harmony import */ var _components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/chemical-image-display */ \"(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction QuestionList(param) {\n    let { questions, onDifficultyChange, onReviewStatusChange, onQuestionDeleted } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionToDelete, setQuestionToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedSolutions, setExpandedSolutions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [expandedHints, setExpandedHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Toggle solution expansion\n    const toggleSolution = (questionId)=>{\n        setExpandedSolutions((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Toggle hints expansion\n    const toggleHints = (questionId)=>{\n        setExpandedHints((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(questionId)) {\n                newSet.delete(questionId);\n            } else {\n                newSet.add(questionId);\n            }\n            return newSet;\n        });\n    };\n    // Handle delete button click\n    const handleDelete = (questionId)=>{\n        setQuestionToDelete(questionId);\n        setIsDeleteDialogOpen(true);\n    };\n    // Handle edit button click\n    const handleEdit = (questionId)=>{\n        router.push(\"/admin/edit-question/\".concat(questionId));\n    };\n    // Confirm delete action\n    const confirmDelete = async ()=>{\n        if (!questionToDelete) return;\n        try {\n            setIsDeleting(true);\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_10__.deleteQuestion)(questionToDelete);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_11__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                if (onQuestionDeleted) {\n                    onQuestionDeleted() // Refresh the list\n                    ;\n                }\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error deleting question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n            setIsDeleteDialogOpen(false);\n            setQuestionToDelete(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap items-center justify-between gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.subject\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"font-normal\",\n                                                    children: question.topic\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                question.reviewStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    className: question.reviewStatus === \"approved\" ? \"bg-green-100 text-green-800 hover:bg-green-100\" : question.reviewStatus === \"rejected\" ? \"bg-red-100 text-red-800 hover:bg-red-100\" : \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                    children: question.reviewStatus.charAt(0).toUpperCase() + question.reviewStatus.slice(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.difficulty.toLowerCase(),\n                                                    onValueChange: (value)=>onDifficultyChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"easy\",\n                                                                    children: \"Easy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"hard\",\n                                                                    children: \"Hard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    defaultValue: question.reviewStatus.toLowerCase(),\n                                                    onValueChange: (value)=>onReviewStatusChange(question.id, value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                            className: \"w-[110px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                                placeholder: \"Review Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"pending\",\n                                                                    children: \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"approved\",\n                                                                    children: \"Approved\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                    value: \"rejected\",\n                                                                    children: \"Rejected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleEdit(question.id),\n                                                    title: \"Edit question\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>handleDelete(question.id),\n                                                    title: \"Delete question\",\n                                                    className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: question.isChemical && (question.chemicalImages || question.imageData) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__.ChemicalImageDisplay, {\n                                        text: question.text,\n                                        images: question.imageData || question.chemicalImages,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                        text: question.text,\n                                        maxImageWidth: 400,\n                                        maxImageHeight: 300,\n                                        questionImages: // Convert imageUrls array to object mapping for compatibility\n                                        question.imageUrls && Array.isArray(question.imageUrls) ? {\n                                            'image-1': question.imageUrls[0]\n                                        } : question.imageData || question.chemicalImages\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: question.options.map((option, index)=>question.isChemical && (question.chemicalImages || question.imageData) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chemical_image_display__WEBPACK_IMPORTED_MODULE_8__.ChemicalOptionDisplay, {\n                                            option: option,\n                                            images: question.imageData || question.chemicalImages,\n                                            isCorrect: option.text === question.correctAnswer\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start p-3 rounded-md border \".concat(option.text === question.correctAnswer ? \"border-green-500 bg-green-50\" : \"border-gray-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: option.text,\n                                                                maxImageWidth: 200,\n                                                                maxImageHeight: 150,\n                                                                questionImages: // Convert imageUrls array to object mapping for compatibility\n                                                                question.imageUrls && Array.isArray(question.imageUrls) ? {\n                                                                    'image-1': question.imageUrls[0]\n                                                                } : question.imageData || question.chemicalImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: option.isImageOption ? \"\" : \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__.Base64Image, {\n                                                                src: option.imageUrl,\n                                                                alt: \"Option \".concat(option.label),\n                                                                maxWidth: 200,\n                                                                maxHeight: 150,\n                                                                className: \"border-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        option.isImageOption && !option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-500 italic\",\n                                                            children: \"Image option\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                question.solution && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleSolution(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-blue-600 hover:text-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Solution\",\n                                                expandedSolutions.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedSolutions.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-3 bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Methodology:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: question.solution.methodology\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this),\n                                                question.solution.key_concepts && question.solution.key_concepts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Key Concepts:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: question.solution.key_concepts.map((concept, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: concept\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 25\n                                                }, this),\n                                                question.solution.steps && question.solution.steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-2\",\n                                                            children: \"Solution Steps:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: question.solution.steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                        text: step,\n                                                                        maxImageWidth: 300,\n                                                                        maxImageHeight: 200,\n                                                                        questionImages: // Convert imageUrls array to object mapping for compatibility\n                                                                        question.imageUrls && Array.isArray(question.imageUrls) ? {\n                                                                            'image-1': question.imageUrls[0]\n                                                                        } : question.imageData || question.chemicalImages\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-blue-800 mb-1\",\n                                                            children: \"Final Explanation:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                                text: question.solution.final_explanation,\n                                                                maxImageWidth: 300,\n                                                                maxImageHeight: 200,\n                                                                questionImages: // Convert imageUrls array to object mapping for compatibility\n                                                                question.imageUrls && Array.isArray(question.imageUrls) ? {\n                                                                    'image-1': question.imageUrls[0]\n                                                                } : question.imageData || question.chemicalImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this),\n                                question.hints && question.hints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>toggleHints(question.id),\n                                            className: \"flex items-center gap-2 p-0 h-auto font-medium text-amber-600 hover:text-amber-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Hints (\",\n                                                question.hints.length,\n                                                \")\",\n                                                expandedHints.has(question.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_ChevronUp_Edit_Lightbulb_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this),\n                                        expandedHints.has(question.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-2 bg-amber-50 p-4 rounded-lg\",\n                                            children: question.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                        text: hint,\n                                                        maxImageWidth: 300,\n                                                        maxImageHeight: 200\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogTitle, {\n                                    children: \"Are you sure you want to delete this question?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogDescription, {\n                                    children: \"This action cannot be undone. This will permanently delete the question and all associated data.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogCancel, {\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_9__.AlertDialogAction, {\n                                    onClick: confirmDelete,\n                                    className: \"bg-red-600 hover:bg-red-700\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"Deleting...\" : \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionList, \"HyLOnO+LyIVOiMu1okpM+izBbNU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter\n    ];\n});\n_c = QuestionList;\nvar _c;\n$RefreshReg$(_c, \"QuestionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\n"));

/***/ })

});