# Adobe PDF Extract API - REST API Integration

## Overview

This integration uses Adobe PDF Extract API via REST API calls instead of the SDK, providing better control and lighter dependencies. The system follows the 5-step Adobe PDF Services workflow:

1. **Get Access Token** - Authenticate with Adobe services
2. **Upload Asset** - Upload PDF to Adobe cloud storage
3. **Create Job** - Submit extraction job
4. **Poll Status** - Wait for job completion
5. **Download Results** - Retrieve extracted content

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Upload    │───▶│  Adobe REST API  │───▶│  Extracted      │
│                 │    │  Workflow        │    │  Content        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                    ┌─────────┼─────────┐
                    ▼         ▼         ▼
            ┌──────────┐ ┌──────────┐ ┌──────────┐
            │   Get    │ │  Upload  │ │ Extract  │
            │  Token   │ │   PDF    │ │ Content  │
            └──────────┘ └──────────┘ └──────────┘
```

## Setup Instructions

### 1. Prerequisites

- Adobe PDF Services account with Extract API access
- `pdfservices-api-credentials.json` file from Adobe Developer Console
- Python with `requests` library

### 2. Place Credentials

```bash
# Copy your credentials file to the backend directory
cp /path/to/your/pdfservices-api-credentials.json drona_backend/python/
```

### 3. Install Dependencies

```bash
cd drona_backend/python
pip install requests
```

### 4. Test the Integration

```bash
# Quick REST API test
python test_adobe_rest_api.py

# Full integration test
python test_adobe_integration.py
```

## API Workflow Details

### Step 1: Get Access Token

```python
# POST https://pdf-services.adobe.io/token
headers = {
    'Content-Type': 'application/x-www-form-urlencoded'
}
data = {
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret'
}
```

### Step 2: Upload PDF Asset

```python
# POST https://pdf-services.adobe.io/assets
headers = {
    'X-API-Key': client_id,
    'Authorization': f'Bearer {access_token}',
    'Content-Type': 'application/json'
}
data = {
    'mediaType': 'application/pdf'
}
```

### Step 3: Create Extraction Job

```python
# POST https://pdf-services.adobe.io/operation/extractpdf
headers = {
    'X-API-Key': client_id,
    'Authorization': f'Bearer {access_token}',
    'Content-Type': 'application/json'
}
data = {
    'assetID': asset_id,
    'elementsToExtract': ['text', 'tables', 'figures'],
    'elementsToExtractRenditions': ['figures', 'tables']
}
```

### Step 4: Poll Job Status

```python
# GET {job_location}/status
headers = {
    'Authorization': f'Bearer {access_token}',
    'X-API-Key': client_id
}
```

### Step 5: Download Results

```python
# GET {download_uri}
# Downloads ZIP file with extracted content
```

## Integration Benefits

### Advantages over SDK

- **Lighter Dependencies**: Only requires `requests` library
- **Better Control**: Direct API calls with full visibility
- **Easier Debugging**: Clear HTTP request/response flow
- **No Version Conflicts**: No SDK version compatibility issues
- **Flexible Authentication**: Can use pre-generated tokens

### Enhanced Extraction Features

- **High-Quality Images**: Superior image extraction from PDFs
- **Structured Text**: Maintains document structure and layout
- **Table Extraction**: Extracts tables with proper formatting
- **Figure Detection**: Identifies and extracts figures/diagrams
- **Metadata Preservation**: Keeps element relationships and positioning

## Usage Examples

### Basic Usage

```python
from adobe_pdf_extractor import AdobePDFExtractor

# Initialize with credentials file
extractor = AdobePDFExtractor('pdfservices-api-credentials.json')

# Extract content
result = extractor.extract_images_and_text('document.pdf')

# Access extracted data
text_content = result['text_content']
images = result['images']
metadata = result['extraction_metadata']
```

### With Pre-generated Token

```python
# If you already have an access token
extractor = AdobePDFExtractor(
    credentials_file_path='pdfservices-api-credentials.json',
    access_token='your_existing_token'
)

result = extractor.extract_images_and_text('document.pdf')
```

### Enhanced Question Extractor

```python
from enhanced_question_extractor import EnhancedQuestionExtractor

# Uses Adobe API automatically if credentials are available
extractor = EnhancedQuestionExtractor(ai_provider='gemini')

# Extract questions with enhanced image processing
result = extractor.extract_comprehensive_data_enhanced('questions.pdf')

questions = result['questions']
metadata = result['extraction_metadata']

print(f"Adobe used: {metadata['adobe_used']}")
print(f"Extraction method: {metadata['extraction_method']}")
```

## Error Handling

### Common Issues and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| `401 Unauthorized` | Invalid credentials | Check client_id and client_secret |
| `403 Forbidden` | No API access | Verify PDF Extract API is enabled |
| `429 Too Many Requests` | Rate limit exceeded | Implement retry with backoff |
| `500 Internal Server Error` | Adobe service issue | Retry after delay |

### Debugging Tips

1. **Check Credentials**: Verify `pdfservices-api-credentials.json` format
2. **Test Token Generation**: Run `python test_adobe_rest_api.py`
3. **Monitor API Calls**: Check request/response logs
4. **Validate PDF**: Ensure PDF is not corrupted or password-protected

## Performance Optimization

### Best Practices

- **Token Reuse**: Cache access tokens (valid for 24 hours)
- **Batch Processing**: Process multiple PDFs with same token
- **Async Processing**: Use async/await for multiple extractions
- **Error Retry**: Implement exponential backoff for retries

### Monitoring

```python
# Track extraction performance
result = extractor.extract_images_and_text('document.pdf')
metadata = result['extraction_metadata']

print(f"Pages processed: {metadata['total_pages']}")
print(f"Images extracted: {metadata['total_images']}")
print(f"Extraction method: {metadata['extraction_method']}")
```

## Testing

### Quick Test

```bash
# Test Adobe REST API connectivity
python test_adobe_rest_api.py
```

### Full Integration Test

```bash
# Test complete integration
python test_adobe_integration.py --pdf sample.pdf
```

### Expected Output

```
🚀 [TEST] Starting Adobe PDF Extract REST API tests...
✅ [TEST] Adobe Credentials: PASS
✅ [TEST] Access Token Generation: PASS
✅ [TEST] Asset Upload Endpoint: PASS
🎉 [TEST] All Adobe REST API tests passed!
```

## Troubleshooting

### Setup Issues

1. **Missing Credentials**: Ensure `pdfservices-api-credentials.json` exists
2. **Invalid Format**: Verify JSON structure matches Adobe format
3. **Network Issues**: Check internet connectivity and firewall settings
4. **API Access**: Confirm PDF Extract API is enabled in Adobe Console

### Runtime Issues

1. **Token Expiration**: Tokens expire after 24 hours - regenerate as needed
2. **Job Timeout**: Large PDFs may take longer - increase polling timeout
3. **Memory Issues**: Large extracted content may require memory optimization
4. **Rate Limits**: Implement proper rate limiting and retry logic

## Support

For issues:

1. **Adobe API Issues**: Check [Adobe Developer Console](https://developer.adobe.com/console/)
2. **Integration Issues**: Run diagnostic tests
3. **Performance Issues**: Monitor extraction metadata
4. **Error Debugging**: Check server logs for detailed error messages

## Next Steps

1. **Test Integration**: Run `python test_adobe_rest_api.py`
2. **Process Sample PDF**: Test with your question papers
3. **Monitor Performance**: Check extraction quality and speed
4. **Scale Up**: Integrate with your production workflow

The REST API integration provides a robust, lightweight solution for enhanced PDF extraction with Adobe's industry-leading technology.
