import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

interface PdfGeneratorPayload {
  title: string;
  description: string;
  duration: number;
  totalMarks: number;
  questions: Array<{
    question: string;
    options: string[];
    answer: string;
    subject?: string;
  }>;
  includeAnswers: boolean;
  filename?: string;
  collegeName?: string;
  collegeLogoUrl?: string;
}

// Function to process text for PDF generation - handles tables and images
function processTextForPDF(text: string): string {
  if (!text) return '';

  let processedText = text;

  // Simple, direct base64 image processing
  // Look for base64 data and convert to img tags
  const base64Pattern = /data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g;
  const matches = processedText.match(base64Pattern);

  if (matches) {
    matches.forEach((base64Data) => {
      // Clean the base64 data - remove any whitespace or newlines
      const cleanBase64 = base64Data.replace(/\s+/g, '');

      // Replace each base64 string with an img tag
      const imgTag = `<img src="${cleanBase64}" alt="" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`;
      processedText = processedText.replace(base64Data, imgTag);
    });
  }

  // First, handle tables - convert markdown tables to HTML
  processedText = processedText.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g, (match) => {
    try {
      // Clean up malformed table syntax
      let cleaned = match.trim();
      cleaned = cleaned.replace(/<br\s*\/?>/gi, ' ');

      const lines = cleaned.split('\n').filter(line => line.trim());
      if (lines.length < 2) return match;

      // Parse table structure
      const tableLines = [];
      let hasHeader = false;

      for (const line of lines) {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);

        if (cells.length === 0) continue;

        // Check if this is a separator line
        if (cells.every(cell => cell.match(/^:?-+:?$/))) {
          hasHeader = true;
          continue;
        }

        tableLines.push(cells);
      }

      if (tableLines.length === 0) return match;

      // Generate HTML table
      let html = '<table>';

      if (hasHeader && tableLines.length > 0) {
        html += '<thead><tr>';
        for (const cell of tableLines[0]) {
          html += `<th>${cell}</th>`;
        }
        html += '</tr></thead>';

        if (tableLines.length > 1) {
          html += '<tbody>';
          for (let i = 1; i < tableLines.length; i++) {
            html += '<tr>';
            for (const cell of tableLines[i]) {
              html += `<td>${cell}</td>`;
            }
            html += '</tr>';
          }
          html += '</tbody>';
        }
      } else {
        html += '<tbody>';
        for (const row of tableLines) {
          html += '<tr>';
          for (const cell of row) {
            html += `<td>${cell}</td>`;
          }
          html += '</tr>';
        }
        html += '</tbody>';
      }

      html += '</table>';
      return html;
    } catch (error) {
      console.warn('Error processing table:', error);
      return match;
    }
  });

  return processedText;
}

export const POST = async (req: NextRequest) => {
  try {
    const payload = (await req.json()) as PdfGeneratorPayload;

    const {
      title,
      description,
      duration,
      totalMarks,
      questions,
      includeAnswers,
      filename = 'question-paper.pdf',
      collegeName = '',
      collegeLogoUrl = '',
    } = payload;

    const html = `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${title}</title>
  <link href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.renderMathInElement) {
        window.renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false,
          errorColor: '#cc0000',
          strict: false
        });
      }
    });
  </script>
  <style>
    @page {
      size: A4;
      margin: 25mm 15mm 20mm 15mm;
    }
    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }
    h1,h2,h3 { margin: 0; padding: 0; }
    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }
    /* Watermark */
    body::before {
      content: 'MEDICOS';
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 96pt;
      font-weight: bold;
      color: rgba(0,128,0,0.08); /* greenish */
      z-index: 0;
      pointer-events: none;
    }
    /* Header / Footer */
    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .college { display: flex; align-items: center; gap: 6px; }
    .college img { height: 24px; width: auto; }
    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }
    .meta { text-align: right; font-size: 10pt; }
    .meta div { margin: 0; }

    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }
    .subject-section {
      page-break-before: avoid;
      margin-top: 20px;
    }
    .subject-section:first-child {
      page-break-before: avoid;
      margin-top: 0;
    }
    .subject-content {
      column-count: 2;
      column-gap: 10mm;
      column-rule: 1px solid #ccc; /* Add middle line separator */
      column-rule-style: solid;
    }
    .question { break-inside: avoid; margin-bottom: 12px; }
    .options { margin-left: 16px; }
    .options p { margin: 2px 0; }
    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }
    .subject-section {
      page-break-inside: auto;
      margin-bottom: 20px;
      page-break-after: avoid;
    }
    .subject-heading {
      font-weight: bold;
      font-size: 12pt;
      margin: 0 0 12px 0;
      text-align: left;
      
      padding-bottom: 4px;
      page-break-after: avoid;
      page-break-before: avoid;
      width: 48%;
      display: inline-block;
      vertical-align: top;
    }
    /* Table styling for proper rendering */
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 8px 0;
      font-size: 9pt;
      break-inside: avoid;
    }
    th, td {
      border: 1px solid #333;
      padding: 4px 6px;
      text-align: left;
      vertical-align: top;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    /* Math rendering support */
    .katex {
      font-size: 1em;
    }
    .katex-display {
      margin: 0.3em 0;
    }
    /* Image styling */
    img {
      max-width: 300px !important;
      height: auto !important;
      display: block !important;
      margin: 10px auto !important;
      border: 1px solid #ddd !important;
      padding: 5px !important;
      break-inside: avoid;
    }
  </style>
</head>
<body>
  <header>
    <div class="college">
      ${collegeLogoUrl ? `<img src="${collegeLogoUrl}" alt="logo" />` : ''}
      <span>${collegeName}</span>
    </div>
    <div class="title">${title}</div>
    <div class="meta">
      <div>Total Marks: ${totalMarks}</div>
      <div>Duration: ${duration} mins</div>
    </div>
  </header>
  <hr style="page-break-after: avoid;" />
  <p style="page-break-after: avoid; margin-bottom: 10px;">${description}</p>
  <div class="questions">
    ${(() => {
      // Group questions by subject
      const groupedQuestions = questions.reduce((groups, question) => {
        const subject = question.subject || 'General';
        if (!groups[subject]) {
          groups[subject] = [];
        }
        groups[subject].push(question);
        return groups;
      }, {} as Record<string, typeof questions>);

      // Generate HTML for each subject group
      return Object.entries(groupedQuestions).map(([subject, subjectQuestions]) => {
        const subjectHtml = `
          <div class="subject-section">
            <div class="subject-heading">Subject: ${subject}</div>
            
            <div class="subject-content">
              ${subjectQuestions.map((q, questionIndex) => {
                // Process question text and handle images from imageUrls array
                let questionText = q.question;

                // Check for images in imageUrls array (new database structure)
                const imageUrls = (q as any).imageUrls || [];
                if (imageUrls && imageUrls.length > 0) {
                  // Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images
                  questionText = questionText.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt) => {
                    // Use the first available image since backend extracts images in order
                    return `<img src="${imageUrls[0]}" alt="${alt || 'Question Image'}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
                  });

                  // Replace HTML img tags with actual images
                  questionText = questionText.replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi, () => {
                    // Use the first available image
                    return `<img src="${imageUrls[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
                  });

                  // If content mentions images but no image tags found, append the first image
                  if (!questionText.includes('<img') && imageUrls.length > 0) {
                    const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(questionText);
                    if (hasImageKeywords) {
                      questionText += `\n<img src="${imageUrls[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`;
                    }
                  }
                }

                // Fallback: Check for legacy imageData or chemicalImages fields
                const imageData = (q as any).imageData || (q as any).chemicalImages;
                if (imageData && typeof imageData === 'object' && !questionText.includes('<img')) {
                  // If we have image data but no images in question text, add them
                  const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');
                  if (!hasImagesInText) {
                    // Add the first available image to the question text
                    const firstImageKey = Object.keys(imageData)[0];
                    if (firstImageKey && imageData[firstImageKey]) {
                      questionText = questionText + '\n' + imageData[firstImageKey];
                    }
                  }
                }

                // Process question text with tables, images, and LaTeX
                let processedQuestion = processTextForPDF(questionText);

                // Apply LaTeX fixes after table processing
                processedQuestion = processedQuestion
                  // Fix the main \ffrac issue - exact patterns from your examples
                  .replace(/\\ffracωLR/g, '\\frac{ω}{LR}')
                  .replace(/\\ffrac1ωCR/g, '\\frac{1}{ωCR}')
                  .replace(/\\ffracLC\\ffrac1R/g, '\\frac{LC}{\\frac{1}{R}}')
                  .replace(/\\ffracRLC/g, '\\frac{R}{LC}')
                  .replace(/\\ffrac100πMHz/g, '\\frac{100}{πMHz}')
                  .replace(/\\ffrac1000πHz/g, '\\frac{1000}{πHz}')
                  .replace(/\\ffrac11000ohm/g, '\\frac{1}{1000ohm}')
                  .replace(/\\ffrac1Cω/g, '\\frac{1}{Cω}')

                  // Fix basic \ffrac patterns
                  .replace(/\\ffrac\{/g, '\\frac{')
                  .replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2}')
                  .replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2$3}')
                  .replace(/\\ffrac([A-Z]+)([A-Z]+)/g, '\\frac{$1}{$2}')

                  // Remove any remaining broken image references
                  .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
                  // Remove any remaining standalone base64 strings that couldn't be processed
                  
                  // Remove broken image references like "img − 1.jpeg (data:...)"
                  .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
                  // Remove any remaining standalone base64 strings that couldn't be processed
                  ;

                const processedOptions = q.options.map(opt => {
                  // Process option text with tables first
                  let processedOpt = processTextForPDF(opt);

                  // Apply LaTeX fixes after table processing
                  return processedOpt
                    // Fix the main \ffrac issue - exact patterns
                    .replace(/\\ffracωLR/g, '\\frac{ω}{LR}')
                    .replace(/\\ffrac1ωCR/g, '\\frac{1}{ωCR}')
                    .replace(/\\ffracLC\\ffrac1R/g, '\\frac{LC}{\\frac{1}{R}}')
                    .replace(/\\ffracRLC/g, '\\frac{R}{LC}')
                    .replace(/\\ffrac100πMHz/g, '\\frac{100}{πMHz}')
                    .replace(/\\ffrac1000πHz/g, '\\frac{1000}{πHz}')
                    .replace(/\\ffrac11000ohm/g, '\\frac{1}{1000ohm}')
                    .replace(/\\ffrac1Cω/g, '\\frac{1}{Cω}')

                    // Fix basic \ffrac patterns
                    .replace(/\\ffrac\{/g, '\\frac{')
                    .replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2}')
                    .replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2$3}')
                    .replace(/\\ffrac([A-Z]+)([A-Z]+)/g, '\\frac{$1}{$2}')

                    // Remove any remaining broken image references
                    .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
                    // Remove any remaining standalone base64 strings that couldn't be processed
                    
                    // Remove broken image references like "img − 1.jpeg (data:...)"
                    .replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi, '')
                    // Remove any remaining standalone base64 strings that couldn't be processed
                    ;
                });

                return `
                  <div class="question">
                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>
                    <div class="options">
                      ${processedOptions.map((opt: string, i: number) => `<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}
                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}
                    </div>
                  </div>`;
              }).join('')}
            </div>
          </div>`;
        return subjectHtml;
      }).join('');
    })()}
  </div>
  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>
</body>
</html>`;

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'domcontentloaded' });

    // Wait for images to load
    await page.evaluate(() => {
      return Promise.all(Array.from(document.images).map(img => {
        if (img.complete) return Promise.resolve();
        return new Promise((resolve) => {
          img.addEventListener('load', resolve);
          img.addEventListener('error', resolve); // Resolve even on error to not block
          setTimeout(resolve, 3000); // Timeout after 3 seconds
        });
      }));
    });

    // Wait for KaTeX to load and render math
    await page.waitForFunction(() => {
      return (window as any).renderMathInElement !== undefined;
    }, { timeout: 5000 }).catch(() => {});

    // Trigger math rendering manually if needed
    await page.evaluate(() => {
      if ((window as any).renderMathInElement) {
        (window as any).renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false,
          errorColor: '#cc0000',
          strict: false
        });
      }
    });

    // Wait for rendering to complete
    await page.waitForFunction(() => {
      const mathElements = document.querySelectorAll('script[type="math/tex"]');
      const katexElements = document.querySelectorAll('.katex');
      return mathElements.length === 0 || katexElements.length > 0;
    }, { timeout: 5000 }).catch(() => {});

    // Extra delay to ensure layout settles
    await new Promise(resolve => setTimeout(resolve, 500));

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20mm', right: '15mm', bottom: '20mm', left: '15mm' },
    });

    await browser.close();

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error: any) {
    console.error('PDF generation failed:', error);
    return new NextResponse(JSON.stringify({ error: 'PDF generation failed' }), { status: 500 });
  }
};
