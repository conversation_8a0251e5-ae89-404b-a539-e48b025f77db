import { Model, Types } from 'mongoose';
import { Question } from '../schema/question.schema';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { FilterQuestionsDto } from './dto/filter-questions.dto';
import { BulkUploadPdfDto } from './dto/bulk-upload-pdf.dto';
import { MistralAiService } from '../mistral-ai/mistral-ai.service';
import { ImageCompressionService } from '../common/services/image-compression.service';
import { HttpService } from '@nestjs/axios';
export declare class QuestionsService {
    private questionModel;
    private readonly mistralAiService;
    private readonly imageCompressionService;
    private readonly httpService;
    constructor(questionModel: Model<Question>, mistralAiService: MistralAiService, imageCompressionService: ImageCompressionService, httpService: HttpService);
    create(createQuestionDto: CreateQuestionDto, user?: any, images?: Express.Multer.File[]): Promise<Question>;
    findAll(filters: FilterQuestionsDto): Promise<any>;
    findOne(id: string): Promise<Question>;
    update(id: string, updateQuestionDto: UpdateQuestionDto, images?: Express.Multer.File[]): Promise<Question>;
    remove(id: string): Promise<Question>;
    findDuplicates(filters?: {
        subjectId?: string;
        limit?: number;
    }): Promise<any>;
    findPendingReviews(filters?: {
        subjectId?: string;
        page?: number;
        limit?: number;
    }): Promise<any>;
    removeQuestion(id: string): Promise<void>;
    getDebugCounts(): Promise<any>;
    private escapeRegExp;
    private normalizeQuestionContent;
    private checkForDuplicateQuestion;
    private checkForDuplicateQuestionExcluding;
    reviewQuestion(id: string, reviewDto: {
        status: 'approved' | 'rejected';
        notes?: string;
    }, user: any): Promise<import("mongoose").Document<unknown, {}, Question> & Question & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    }>;
    bulkReview(questionIds: string[], reviewDto: {
        status: 'approved' | 'rejected';
        notes?: string;
    }, user: any): Promise<{
        message: string;
        reviewedCount: number;
        status: "approved" | "rejected";
        details: {
            matchedCount: number;
            modifiedCount: number;
            upsertedCount: number;
            deletedCount: number;
        };
    }>;
    bulkUploadFromPdf(file: Express.Multer.File, uploadDto: BulkUploadPdfDto, user: any): Promise<{
        message: string;
        questionsAdded: number;
        questionsFailed: number;
        questions: any[];
        errors: string[];
    }>;
    bulkUploadChemicalFromPdf(file: Express.Multer.File, uploadDto: BulkUploadPdfDto, user: any): Promise<{
        message: string;
        questionsAdded: number;
        questionsFailed: number;
        questions: any[];
        errors: string[];
        extractionMetadata: any;
    }>;
    private saveExtractedQuestions;
    private saveChemicalExtractedQuestions;
}
