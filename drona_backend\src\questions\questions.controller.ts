import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { QuestionsService } from './questions.service';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { FilterQuestionsDto } from './dto/filter-questions.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { Question } from '../schema/question.schema';
import { BulkReviewDto } from './dto/bulk-review.dto';
import { BulkUploadPdfDto } from './dto/bulk-upload-pdf.dto';
import { ReviewQuestionDto } from './dto/review-question.dto';

/**
 * Question management endpoints
 *
 * These endpoints allow super admins to create, manage, and review questions.
 * Questions can be filtered, reviewed individually or in bulk, and checked for duplicates.
 */
@ApiTags('Questions')
@ApiBearerAuth('JWT-auth')
@Controller('questions')
@UseGuards(JwtAuthGuard, RolesGuard)
export class QuestionsController {
  constructor(private readonly questionsService: QuestionsService) {}

  @Post()
  @Roles('superAdmin')
  @UseInterceptors(
    FilesInterceptor('images', 5, {
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB max per file to allow large images
      },
      fileFilter: (req, file, callback) => {
        if (!file.mimetype.match(/^image\/(jpeg|jpg|png|webp)$/)) {
          return callback(new Error('Only image files are allowed'), false);
        }
        callback(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new question with optional images',
    description:
      'Creates a new question with large image support and duplicate content checking (super admin only). The system checks for duplicate questions by comparing normalized content (ignoring whitespaces, newlines, and case). Upload up to 5 images (up to 100MB each) along with question data. Images are automatically compressed to 2MB and stored in the database.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        content: { type: 'string', example: 'What is the capital of France?' },
        options: {
          type: 'array',
          items: { type: 'string' },
          example: ['Paris', 'London', 'Berlin', 'Madrid'],
        },
        answer: { type: 'string', example: 'Paris' },
        subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        topicId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        difficulty: {
          type: 'string',
          enum: ['easy', 'medium', 'hard'],
          example: 'medium',
        },
        type: {
          type: 'string',
          enum: ['multiple-choice', 'true-false', 'descriptive'],
          example: 'multiple-choice',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          example: ['geography', 'capitals'],
        },
        images: {
          type: 'array',
          items: { type: 'string', format: 'binary' },
          description:
            'Image files (max 5, JPEG/PNG/WebP, up to 100MB each, compressed to 2MB and stored in database)',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Question created successfully',
    type: Question,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data, image files, or duplicate question content',
    schema: {
      oneOf: [
        {
          example: {
            statusCode: 400,
            message: 'A question with similar content already exists in the database (ID: 60d21b4667d0d8992e610c85). Please modify the question content to make it unique.',
            error: 'Bad Request',
          },
        },
        {
          example: {
            statusCode: 400,
            message: 'Image compression failed: Unable to compress to 2MB target size',
            error: 'Bad Request',
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Body() createQuestionDto: CreateQuestionDto,
    @UploadedFiles() images: Express.Multer.File[],
    @Request() req: any,
  ) {
    return this.questionsService.create(createQuestionDto, req.user, images);
  }

  @Get()
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Get all questions',
    description:
      'Returns all questions with optional filtering and pagination (super admin only)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 20, max: 100)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns questions with pagination',
    schema: {
      type: 'object',
      properties: {
        questions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string' },
              content: { type: 'string' },
              options: { 
                type: 'array',
                items: { type: 'string' },
                example: ['Paris', 'London', 'Berlin', 'Madrid']
              },
              answer: { type: 'string' },
              difficulty: { type: 'string' },
              type: { type: 'string' },
              reviewStatus: { type: 'string' },
              status: { type: 'string' },
              subject: {
                type: 'object',
                properties: {
                  _id: { type: 'string' },
                  name: { type: 'string' },
                },
              },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            currentPage: { type: 'number' },
            totalPages: { type: 'number' },
            totalItems: { type: 'number' },
            itemsPerPage: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Query() filters: FilterQuestionsDto,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = parseInt(page || '1', 10);
    const limitNum = Math.min(parseInt(limit || '20', 10), 100); // Max 100 items per page

    return this.questionsService.findAll({
      ...filters,
      page: pageNum,
      limit: limitNum,
    });
  }

  @Get('duplicates')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Find duplicate questions',
    description:
      'Returns a list of potential duplicate questions with optimized performance (super admin only)',
  })
  @ApiQuery({
    name: 'subjectId',
    required: false,
    description: 'Filter duplicates by subject ID',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limit number of duplicate groups (default: 50)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns potential duplicate questions',
    schema: {
      type: 'object',
      properties: {
        duplicateGroups: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              originalQuestion: {
                type: 'object',
                properties: {
                  _id: { type: 'string' },
                  content: { type: 'string' },
                  subject: {
                    type: 'object',
                    properties: { name: { type: 'string' } },
                  },
                  college: {
                    type: 'object',
                    properties: { name: { type: 'string' } },
                  },
                },
              },
              duplicates: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string' },
                    content: { type: 'string' },
                    similarity: { type: 'string' },
                    subject: {
                      type: 'object',
                      properties: { name: { type: 'string' } },
                    },
                  },
                },
              },
            },
          },
        },
        totalGroups: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findDuplicates(
    @Query('subjectId') subjectId?: string,
    @Query('limit') limit?: string,
  ) {
    const limitNum = parseInt(limit || '50', 10);
    return this.questionsService.findDuplicates({ subjectId, limit: limitNum });
  }

  @Delete('delete/:id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Delete a question',
    description: 'Deletes a question by ID (super admin only)',
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiResponse({ status: 200, description: 'Question deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  removeQuestion(@Param('id') id: string) {
    return this.questionsService.removeQuestion(id);
  }



  @Get('pending-reviews')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Get pending reviews',
    description:
      'Get all questions pending review by super admin across all colleges with pagination and optimized response',
  })
  @ApiQuery({
    name: 'subjectId',
    required: false,
    description: 'Filter by subject ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (default: 20, max: 100)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns pending questions with pagination',
    schema: {
      type: 'object',
      properties: {
        questions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string' },
              content: { type: 'string' },
              difficulty: { type: 'string' },
              type: { type: 'string' },
              subject: {
                type: 'object',
                properties: {
                  _id: { type: 'string' },
                  name: { type: 'string' },
                },
              },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            currentPage: { type: 'number' },
            totalPages: { type: 'number' },
            totalItems: { type: 'number' },
            itemsPerPage: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findPendingReviews(
    @Query('subjectId') subjectId?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = parseInt(page || '1', 10);
    const limitNum = Math.min(parseInt(limit || '20', 10), 100); // Max 100 items per page

    return this.questionsService.findPendingReviews({
      subjectId,
      page: pageNum,
      limit: limitNum,
    });
  }

  @Get(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Get a question by ID',
    description: 'Returns a question by ID (super admin only)',
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the question',
    type: Question,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  findOne(@Param('id') id: string) {
    return this.questionsService.findOne(id);
  }

  @Patch(':id')
  @Roles('superAdmin')
  @UseInterceptors(
    FilesInterceptor('images', 5, {
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB max per file to allow large images
      },
      fileFilter: (req, file, callback) => {
        if (!file.mimetype.match(/^image\/(jpeg|jpg|png|webp)$/)) {
          return callback(new Error('Only image files are allowed'), false);
        }
        callback(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a question with optional new images',
    description:
      'Updates a question by ID with large image support and duplicate content checking (super admin only). The system checks for duplicate questions by comparing normalized content (ignoring whitespaces, newlines, and case) excluding the current question being updated. Images up to 100MB are supported, automatically compressed to 2MB and stored in database.',
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        content: { type: 'string', example: 'What is the capital of France?' },
        options: {
          type: 'array',
          items: { type: 'string' },
          example: ['Paris', 'London', 'Berlin', 'Madrid'],
        },
        answer: { type: 'string', example: 'Paris' },
        subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        topicId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        difficulty: {
          type: 'string',
          enum: ['easy', 'medium', 'hard'],
          example: 'medium',
        },
        type: {
          type: 'string',
          enum: ['multiple-choice', 'true-false', 'descriptive'],
          example: 'multiple-choice',
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          example: ['geography', 'capitals'],
        },
        images: {
          type: 'array',
          items: { type: 'string', format: 'binary' },
          description:
            'New image files to add (max 5, JPEG/PNG/WebP, up to 100MB each, compressed to 2MB and stored in database)',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Question updated successfully',
    type: Question,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data, image files, or duplicate question content',
    schema: {
      oneOf: [
        {
          example: {
            statusCode: 400,
            message: 'A question with similar content already exists in the database (ID: 60d21b4667d0d8992e610c85). Please modify the question content to make it unique.',
            error: 'Bad Request',
          },
        },
        {
          example: {
            statusCode: 400,
            message: 'Image compression failed: Unable to compress to 2MB target size',
            error: 'Bad Request',
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  update(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateQuestionDto,
    @UploadedFiles() images: Express.Multer.File[],
  ) {
    return this.questionsService.update(id, updateQuestionDto, images);
  }

  @Delete(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Delete a question',
    description: 'Deletes a question by ID (super admin only)',
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiResponse({ status: 200, description: 'Question deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  remove(@Param('id') id: string) {
    return this.questionsService.remove(id);
  }

  @Patch(':id/review')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Review a question',
    description: 'Review and approve/reject a question (super admin only)',
  })
  @ApiParam({ name: 'id', description: 'Question ID' })
  @ApiBody({
    type: ReviewQuestionDto,
    description: 'Review data containing status and optional notes',
    examples: {
      approve: {
        summary: 'Approve a question',
        description: 'Example of approving a question with notes',
        value: {
          status: 'approved',
          notes: 'Question meets quality standards and is well-structured',
        },
      },
      reject: {
        summary: 'Reject a question',
        description: 'Example of rejecting a question with feedback',
        value: {
          status: 'rejected',
          notes: 'Question contains grammatical errors and unclear options',
        },
      },
      approveWithoutNotes: {
        summary: 'Approve without notes',
        description: 'Example of approving a question without additional notes',
        value: {
          status: 'approved',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Question reviewed successfully',
    schema: {
      example: {
        message: 'Question reviewed successfully',
        status: 'approved',
        reviewNotes: 'Question meets quality standards',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  async reviewQuestion(
    @Param('id') id: string,
    @Body() reviewDto: ReviewQuestionDto,
    @Request() req: any,
  ) {
    return this.questionsService.reviewQuestion(id, reviewDto, req.user);
  }

  @Patch('bulk-review')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Bulk review multiple questions',
    description:
      'Review multiple questions at once. Only superAdmin can perform this action.',
  })
  @ApiResponse({
    status: 200,
    description: 'Questions successfully reviewed',
    schema: {
      example: {
        message: 'Successfully reviewed 5 questions',
        reviewedCount: 5,
        status: 'approved',
        details: {
          matchedCount: 5,
          modifiedCount: 5,
          upsertedCount: 0,
          deletedCount: 0,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input or already reviewed questions',
    schema: {
      example: {
        statusCode: 400,
        message:
          'Some questions are already reviewed: 60d21b4667d0d8992e610c85',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Questions not found',
    schema: {
      example: {
        statusCode: 404,
        message: 'Some questions were not found: 60d21b4667d0d8992e610c85',
        error: 'Not Found',
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have superAdmin role',
  })
  async bulkReviewQuestions(
    @Body() bulkReviewDto: BulkReviewDto,
    @Request() req: any,
  ) {
    return this.questionsService.bulkReview(
      bulkReviewDto.questionIds,
      {
        status: bulkReviewDto.status,
        notes: bulkReviewDto.notes,
      },
      req.user,
    );
  }



  @Post('bulk-upload-pdf')
  @Roles('superAdmin')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk upload questions from PDF',
    description:
      'Upload a PDF file and extract questions using Mistral AI OCR. Questions will be marked as in-review status.',
  })
  @ApiBody({
    description: 'PDF file, subject ID, and topic ID for bulk question upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'PDF file containing questions',
        },
        subjectId: {
          type: 'string',
          description: 'Subject ID for the questions',
          example: '60d21b4667d0d8992e610c85',
        },
        topicId: {
          type: 'string',
          description: 'Topic ID for the questions',
          example: '60d21b4667d0d8992e610c86',
        },
      },
      required: ['file', 'subjectId', 'topicId'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'PDF processed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        questionsAdded: { type: 'number' },
        questionsFailed: { type: 'number' },
        questions: {
          type: 'array',
          items: { type: 'object' },
        },
        errors: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid file or missing parameters',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUploadPdf(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: BulkUploadPdfDto,
    @Request() req: any,
  ) {
    if (!file) {
      throw new Error('No file provided');
    }

    if (file.mimetype !== 'application/pdf') {
      throw new Error('Only PDF files are allowed');
    }

    return this.questionsService.bulkUploadFromPdf(file, uploadDto, req.user);
  }

  @Get('debug/counts')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Debug endpoint to check question counts and database status',
    description: 'Returns debug information about questions in the database',
  })
  @ApiResponse({
    status: 200,
    description: 'Debug information retrieved successfully',
  })
  async getDebugCounts() {
    return this.questionsService.getDebugCounts();
  }
}
