#!/usr/bin/env python3
"""
Clean PDF Question Extractor - Simplified implementation
Extracts questions, answers, solutions, and hints from PDFs with proper image handling.
"""

import os
import json
import base64
import re
from mistralai import Mistral


class CleanQuestionExtractor:
    """Clean, simple PDF question extractor with image support"""
    
    def __init__(self, api_key_file='key.txt'):
        """Initialize with Mistral API key"""
        try:
            with open(api_key_file, 'r') as f:
                api_key = f.read().strip()
            self.client = Mistral(api_key=api_key)
            print("✅ Mistral AI client initialized")
        except FileNotFoundError:
            print(f"❌ API key file '{api_key_file}' not found")
            self.client = None
    
    def extract_from_pdf(self, pdf_path):
        """
        Main method: Extract questions with solutions, hints, and images from PDF
        
        Returns:
            str: JSON string with processed questions
        """
        if not self.client:
            raise Exception("Mistral AI client not initialized")
        
        print(f"🔄 Processing PDF: {os.path.basename(pdf_path)}")
        
        # Step 1: Extract text and images using OCR
        print("📄 Extracting text and images from PDF...")
        text, images = self._extract_ocr_data(pdf_path)
        print(f"✅ Extracted {len(text):,} characters and {len(images)} images")
        
        # Step 2: Send to AI for question extraction
        print("🤖 Extracting questions using AI...")
        ai_response = self._extract_with_ai(text)
        print(f"✅ Received AI response ({len(ai_response):,} characters)")
        
        # Step 3: Process images in the response
        print("📸 Processing images in questions and options...")
        final_json = self._process_images(ai_response, images)
        print("✅ Image processing completed")
        
        return final_json
    
    def _extract_ocr_data(self, pdf_path):
        """Extract text and images from PDF using Mistral OCR"""
        try:
            # Encode PDF to base64
            with open(pdf_path, "rb") as pdf_file:
                base64_pdf = base64.b64encode(pdf_file.read()).decode('utf-8')
            
            # Call Mistral OCR
            ocr_response = self.client.ocr.process(
                model="mistral-ocr-latest",
                document={
                    "type": "document_url",
                    "document_url": f"data:application/pdf;base64,{base64_pdf}"
                },
                include_image_base64=True
            )
            
            # Extract text and images
            full_text = ""
            all_images = {}
            
            if hasattr(ocr_response, 'pages') and ocr_response.pages:
                for page in ocr_response.pages:
                    # Extract text
                    if hasattr(page, 'markdown') and page.markdown:
                        full_text += page.markdown + "\n"
                    elif hasattr(page, 'text') and page.text:
                        full_text += page.text + "\n"
                    
                    # Extract images
                    if hasattr(page, 'images') and page.images:
                        for image in page.images:
                            if hasattr(image, 'id') and hasattr(image, 'image_base64'):
                                all_images[image.id] = image.image_base64
                            elif hasattr(image, 'id') and hasattr(image, 'base64'):
                                all_images[image.id] = f"data:image/jpeg;base64,{image.base64}"
            else:
                full_text = str(ocr_response)
            
            return full_text.strip(), all_images
            
        except Exception as e:
            raise Exception(f"OCR extraction failed: {e}")
    
    def _extract_with_ai(self, text):
        """Extract questions using AI with a single, comprehensive prompt"""
        
        prompt = f"""Extract ALL multiple-choice questions from this text. Include questions, options, answers, solutions, and hints.

Return a JSON array where each question follows this EXACT structure:

{{
  "question": "Complete question text (keep image references like ![img-0.jpeg](img-0.jpeg))",
  "options": {{
    "A": "Option A text with possible image reference ![img-X.jpeg](img-X.jpeg)",
    "B": "Option B text with possible image reference ![img-Y.jpeg](img-Y.jpeg)",
    "C": "Option C text only",
    "D": "Option D text only"
  }},
  "answer": "A",
  "solution": {{
    "steps": ["Step 1: explanation", "Step 2: explanation"],
    "methodology": "Brief description of solution approach",
    "key_concepts": ["concept1", "concept2"],
    "final_explanation": "Final explanation connecting solution to answer"
  }},
  "hints": ["Hint 1: helpful tip", "Hint 2: another tip"],
  "imageUrl": {{"img-0.jpeg": "", "img-1.jpeg": ""}}
}}

IMPORTANT RULES:
1. For question images: Keep ![img-X.jpeg](img-X.jpeg) in question text AND add empty imageUrl object
2. For option images: Keep text AND image references together like "Text description ![img-X.jpeg](img-X.jpeg)"
3. Extract solutions from any "Solution:", "Explanation:", "Steps:", "Method:" sections
4. Extract hints from "Hint:", "Tip:", "Note:", "Remember:" sections
5. Only include imageUrl if question actually has image references
6. Return ONLY the JSON array, no markdown or explanations

Text to analyze:
{text}"""

        try:
            response = self.client.chat.complete(
                model="mistral-small-latest",
                messages=[{"role": "user", "content": prompt}]
            )
            
            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                raise Exception("No response from AI")
                
        except Exception as e:
            raise Exception(f"AI extraction failed: {e}")
    
    def _process_images(self, ai_response, images):
        """Process the AI response to embed actual image data"""
        try:
            # Clean and parse the AI response
            cleaned_response = self._clean_json_response(ai_response)
            questions = json.loads(cleaned_response)
            
            if not isinstance(questions, list):
                questions = [questions] if isinstance(questions, dict) else []
            
            print(f"📝 Processing {len(questions)} questions for image embedding...")
            
            # Process each question
            for i, question in enumerate(questions):
                if not isinstance(question, dict):
                    continue
                
                # Process question images (add to imageUrl object)
                self._process_question_images(question, images, i+1)
                
                # Process option images (replace placeholders with base64)
                self._process_option_images(question, images, i+1)
            
            # Return clean JSON
            return json.dumps(questions, indent=2, ensure_ascii=False)
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            return ai_response
        except Exception as e:
            print(f"❌ Image processing error: {e}")
            return ai_response
    
    def _clean_json_response(self, response):
        """Clean AI response to extract valid JSON"""
        # Remove markdown formatting
        cleaned = response.strip()
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]
        if cleaned.startswith('```'):
            cleaned = cleaned[3:]
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]
        
        # Fix common JSON escape issues
        cleaned = re.sub(r'(?<!\\)\\([a-zA-Z])', r'\\\\1', cleaned)
        
        # Extract JSON array
        start = cleaned.find('[')
        end = cleaned.rfind(']')
        if start != -1 and end != -1:
            return cleaned[start:end+1]
        
        return cleaned.strip()
    
    def _process_question_images(self, question, images, question_num):
        """Add image data to question's imageUrl object"""
        if 'question' not in question or not question['question']:
            return
        
        # Find image references in question text
        image_refs = re.findall(r'img-(\d+)\.jpeg', question['question'])
        
        if image_refs:
            print(f"📸 Question {question_num} has {len(image_refs)} image references")
            question['imageUrl'] = {}
            
            for img_num in set(image_refs):  # Remove duplicates
                img_id = f"img-{img_num}.jpeg"
                if img_id in images:
                    question['imageUrl'][img_id] = images[img_id]
                    print(f"✅ Added image {img_id} to question {question_num}")
                else:
                    print(f"⚠️ Image {img_id} not found for question {question_num}")
        elif 'imageUrl' in question:
            # Remove empty imageUrl if no images referenced
            del question['imageUrl']
    
    def _process_option_images(self, question, images, question_num):
        """Process image references in option text and create mixed text+image options"""
        if 'options' not in question:
            return

        for option_key, option_value in question['options'].items():
            if isinstance(option_value, str):
                # Find image references in option text like ![img-X.jpeg](img-X.jpeg)
                image_refs = re.findall(r'!\[([^\]]*)\]\(([^)]*)\)', option_value)

                if image_refs:
                    print(f"📸 Option {option_key} in question {question_num} has {len(image_refs)} image references")

                    # Create a mixed content option with text and embedded images
                    processed_option = self._create_mixed_option_content(option_value, image_refs, images)
                    question['options'][option_key] = processed_option

                    print(f"✅ Processed option {option_key} with mixed text+image content")

    def _create_mixed_option_content(self, option_text, image_refs, images):
        """Create mixed content for options that have both text and images"""
        # For now, we'll create a structured object that contains both text and image data
        # This allows the frontend to render both text and images properly

        result = {
            "type": "mixed",
            "text": option_text,
            "images": {}
        }

        # Extract image data for each referenced image
        for alt_text, img_path in image_refs:
            # Extract image ID from path (e.g., "img-1.jpeg" from "img-1.jpeg")
            img_id = img_path

            if img_id in images:
                result["images"][img_id] = {
                    "alt": alt_text,
                    "data": images[img_id]
                }
                print(f"✅ Added image {img_id} to mixed option content")
            else:
                print(f"⚠️ Image {img_id} not found for mixed option content")

        # If no images were found, just return the text
        if not result["images"]:
            return option_text

        return result
