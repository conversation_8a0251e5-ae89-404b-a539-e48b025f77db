"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/admin/add-question-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/add-question-form.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_IMAGE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\nconst ACCEPTED_PDF_TYPES = [\n    \"application/pdf\"\n];\n// Manual form schema with custom validation for options\nconst manualFormSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\n// PDF upload form schema\nconst pdfUploadSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional()\n});\nfunction AddQuestionForm() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Manual form states\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [manualTopics, setManualTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // PDF upload states\n    const [pdfTopics, setPdfTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPdfFile, setSelectedPdfFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useChemicalExtraction, setUseChemicalExtraction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"AddQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AddQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"AddQuestionForm.useEffect\"], []);\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize manual form\n    const manualForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(manualFormSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Initialize PDF upload form\n    const pdfForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(pdfUploadSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\"\n        }\n    });\n    // Handle subject change for manual form\n    const handleManualSubjectChange = (value)=>{\n        manualForm.setValue(\"subject\", value);\n        manualForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setManualTopics(selectedSubject.topics || []);\n        } else {\n            setManualTopics([]);\n        }\n    };\n    // Handle subject change for PDF form\n    const handlePdfSubjectChange = (value)=>{\n        pdfForm.setValue(\"subject\", value);\n        pdfForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setPdfTopics(selectedSubject.topics || []);\n        } else {\n            setPdfTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            manualForm.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function for manual form\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle manual form submission\n    const onManualSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Manual form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Get user ID from localStorage if available\n            const userData = localStorage.getItem(\"userData\");\n            let userId;\n            try {\n                if (userData) {\n                    const parsed = JSON.parse(userData);\n                    userId = parsed._id || parsed.id;\n                }\n            } catch (e) {\n                console.error(\"Error parsing user data:\", e);\n            }\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add createdBy if we have a valid user ID\n            if (userId) {\n                baseQuestionData.createdBy = userId;\n            }\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            // If question has an image, embed it in the question text as base64\n            let finalQuestionData = {\n                ...questionData\n            };\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.createQuestion)(finalQuestionData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                // Reset manual form\n                resetManualForm();\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error adding question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle PDF form submission\n    const onPdfSubmit = async (data)=>{\n        if (!selectedPdfFile) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"Please select a PDF file to upload.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"PDF form data:\", data);\n            // Submit to bulk upload API\n            const result = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"PDF Upload Successful\",\n                description: \"Successfully uploaded \".concat(result.questionsCreated || 'questions', \" from PDF.\")\n            });\n            // Reset PDF form\n            resetPdfForm();\n        } catch (error) {\n            console.error(\"Error uploading PDF:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to upload PDF. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset manual form\n    const resetManualForm = ()=>{\n        manualForm.reset({\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        });\n        setQuestionImage(null);\n        setOptionImages({\n            A: null,\n            B: null,\n            C: null,\n            D: null\n        });\n        setManualTopics([]);\n        setOptionValidationErrors({\n            A: false,\n            B: false,\n            C: false,\n            D: false\n        });\n    };\n    // Reset PDF form\n    const resetPdfForm = ()=>{\n        pdfForm.reset({\n            subject: \"\",\n            topic: \"\"\n        });\n        setSelectedPdfFile(null);\n        setPdfTopics([]);\n    };\n    // Handle PDF file selection\n    const handlePdfFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (file.type !== \"application/pdf\") {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Invalid File Type\",\n                    description: \"Please select a PDF file.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"File Too Large\",\n                    description: \"File size must be less than 50MB.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setSelectedPdfFile(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    children: \"Add Questions\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"manual\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manual Entry\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"pdf\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upload PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"manual\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...manualForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: manualForm.handleSubmit(onManualSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handleManualSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 495,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: manualTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : manualTopics.length > 0 ? \"Select a topic\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 525,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: manualTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"questionText\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Question Text *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                        placeholder: \"Enter your question here...\",\n                                                                        className: \"min-h-[100px]\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Question Image (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 573,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Upload an image to accompany your question\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-9\",\n                                                                    onClick: ()=>{\n                                                                        var _questionImageRef_current;\n                                                                        return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Upload Image\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    ref: questionImageRef,\n                                                                    className: \"hidden\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: (e)=>handleImageUpload(e, \"question\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: questionImage || \"/placeholder.svg\",\n                                                                            alt: \"Question image\",\n                                                                            width: 100,\n                                                                            height: 100,\n                                                                            className: \"object-cover rounded-md border h-[100px] w-[100px]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"destructive\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 absolute -top-2 -right-2 rounded-full\",\n                                                                            onClick: ()=>removeImage(\"question\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: \"Answer Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this),\n                                                [\n                                                    \"A\",\n                                                    \"B\",\n                                                    \"C\",\n                                                    \"D\"\n                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                                control: manualForm.control,\n                                                                name: \"option\".concat(option),\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-green-600 ml-2\",\n                                                                                        children: \"(Image uploaded)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 643,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 640,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                    placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                                    ...field\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 647,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 656,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-red-600\",\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    \" requires either text or an image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 31\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 27\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 mt-8 md:mt-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-9 text-xs\",\n                                                                            onClick: ()=>{\n                                                                                var _optionImageRefs_option_current;\n                                                                                return (_optionImageRefs_option_current = optionImageRefs[option].current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Image\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            ref: optionImageRefs[option],\n                                                                            className: \"hidden\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>handleImageUpload(e, option)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                    src: optionImages[option] || \"/placeholder.svg\",\n                                                                                    alt: \"Option \".concat(option, \" image\"),\n                                                                                    width: 60,\n                                                                                    height: 60,\n                                                                                    className: \"object-cover rounded-md border h-[60px] w-[60px]\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 689,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: \"destructive\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-5 w-5 absolute -top-2 -right-2 rounded-full\",\n                                                                                    onClick: ()=>removeImage(option),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 703,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, option, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"correctAnswer\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Correct Answer *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"A\",\n                                                                            \"B\",\n                                                                            \"C\",\n                                                                            \"D\"\n                                                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: option,\n                                                                                            id: \"manual-option-\".concat(option)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 730,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 729,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-option-\".concat(option),\n                                                                                        children: option\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 732,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, option, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"difficulty\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Difficulty Level *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"Easy\",\n                                                                            \"Medium\",\n                                                                            \"Hard\"\n                                                                        ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: level,\n                                                                                            id: \"manual-level-\".concat(level)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 759,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 758,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-level-\".concat(level),\n                                                                                        children: level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 761,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, level, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                            control: manualForm.control,\n                                            name: \"explanation\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Explanation (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 785,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 784,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 788,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                placeholder: \"Explain why the correct answer is right...\",\n                                                                className: \"min-h-[80px]\",\n                                                                ...field\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormDescription, {\n                                                            children: \"This will be shown to students after they answer the question.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding Question...\"\n                                                        ]\n                                                    }, void 0, true) : \"Add Question\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetManualForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"pdf\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...pdfForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: pdfForm.handleSubmit(onPdfSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handlePdfSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 850,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: pdfTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : pdfTopics.length > 0 ? \"Select a topic (optional)\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 878,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 877,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: pdfTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 891,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                            children: \"PDF File *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 909,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Upload a PDF file containing questions to be extracted and added to the question bank\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 913,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 912,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 907,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"pdf-upload\",\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                                                children: selectedPdfFile ? selectedPdfFile.name : \"Choose PDF file or drag and drop\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 924,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-1 block text-xs text-gray-500\",\n                                                                                children: \"PDF up to 50MB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 927,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"pdf-upload\",\n                                                                        type: \"file\",\n                                                                        className: \"sr-only\",\n                                                                        accept: \".pdf\",\n                                                                        onChange: handlePdfFileChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 931,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById('pdf-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: \"Select PDF File\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedPdfFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: selectedPdfFile.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 955,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (selectedPdfFile.size / 1024 / 1024).toFixed(2),\n                                                                        \" MB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 956,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setSelectedPdfFile(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting || !selectedPdfFile,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 981,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Uploading PDF...\"\n                                                        ]\n                                                    }, void 0, true) : \"Upload PDF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetPdfForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 988,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n        lineNumber: 463,\n        columnNumber: 5\n    }, this);\n}\n_s(AddQuestionForm, \"KWBNHzcTQIFF+oVUttgNWRp9/No=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AddQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"AddQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/add-question-form.tsx\n"));

/***/ })

});