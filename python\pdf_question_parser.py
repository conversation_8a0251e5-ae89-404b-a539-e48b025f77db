#!/usr/bin/env python3
"""
PDF Question Parser - Main script that processes PDF documents using Mistral AI's Document AI
to extract questions and multiple-choice options and format them in the required structure.

Author: AI Assistant
Date: 2025-01-19
"""

import os
import sys
import argparse
from pathlib import Path
from question_extractor import QuestionExtractor
from output_formatter import OutputFormatter

class PDFQuestionParser:
    """Main class for processing PDF documents and extracting formatted questions"""
    
    def __init__(self, api_key_file='key.txt', ai_provider='mistral'):
        """Initialize the PDF question parser"""
        self.extractor = QuestionExtractor(api_key_file, ai_provider=ai_provider)
        self.formatter = OutputFormatter()
    
    def process_pdf(self, pdf_path, output_file=None, json_output=False, extract_solutions=False):
        """
        Process a PDF file and extract formatted questions or solutions

        Args:
            pdf_path (str): Path to the PDF file
            output_file (str): Optional output file path
            json_output (bool): Whether to also save JSON output
            extract_solutions (bool): Whether to extract solutions instead of questions

        Returns:
            dict: Processing results with status, formatted_text, and metadata
        """
        try:
            print(f"🔄 Processing PDF: {pdf_path}")

            # Check if PDF file exists
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"PDF file '{pdf_path}' not found")

            if extract_solutions:
                # Extract solutions from PDF
                print("📄 Extracting solutions from PDF...")
                json_response = self.extractor.extract_solutions_from_pdf(pdf_path)
                content_type = "solutions"
                default_json_name = 'extracted_solutions.json'
            else:
                # Extract questions from PDF (now returns raw JSON string)
                print("📄 Extracting questions from PDF...")
                json_response = self.extractor.extract_questions_from_pdf(pdf_path)
                content_type = "questions"
                default_json_name = 'extracted_questions.json'

            # Save JSON output if requested
            if json_output:
                print("💾 Saving JSON output...")
                json_output_path = output_file.replace('.txt', '.json') if output_file else default_json_name
                with open(json_output_path, 'w', encoding='utf-8') as f:
                    f.write(json_response)

            if not json_response or json_response.strip() == "[]":
                return {
                    'status': 'warning',
                    'message': f'No {content_type} found in the PDF',
                    'formatted_text': json_response,
                    'question_count': 0
                }

            # Return the raw JSON response
            print(f"📝 Returning raw JSON response from model...")
            formatted_text = json_response

            # Save to file if output_file is specified
            if output_file:
                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(formatted_text)
                    print(f"💾 Output saved to: {output_file}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not save output to {output_file}: {e}")

            return {
                'status': 'success',
                'message': f'Successfully extracted {content_type} as JSON',
                'formatted_text': formatted_text,
                'question_count': 'Unknown (raw JSON)',
                'output_file': output_file,
                'content_type': content_type
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error processing PDF: {e}',
                'formatted_text': '',
                'question_count': 0
            }

    def process_pdf_solutions(self, pdf_path, output_file=None, json_output=True):
        """
        Process a single PDF file and extract solutions with detailed steps

        Args:
            pdf_path (str): Path to the PDF file
            output_file (str): Optional output file path
            json_output (bool): Whether to save JSON output

        Returns:
            dict: Processing results with status, formatted_text, and metadata
        """
        return self.process_pdf(pdf_path, output_file, json_output, extract_solutions=True)
    
    def process_directory(self, directory_path, output_dir=None):
        """
        Process all PDF files in a directory
        
        Args:
            directory_path (str): Path to directory containing PDF files
            output_dir (str): Optional output directory for results
            
        Returns:
            dict: Processing results for all PDFs
        """
        try:
            directory = Path(directory_path)
            if not directory.exists():
                raise FileNotFoundError(f"Directory '{directory_path}' not found")
            
            # Find all PDF files
            pdf_files = list(directory.glob('*.pdf'))
            
            if not pdf_files:
                return {
                    'status': 'warning',
                    'message': 'No PDF files found in directory',
                    'results': []
                }
            
            print(f"📁 Found {len(pdf_files)} PDF files in directory")
            
            results = []
            
            for pdf_file in pdf_files:
                print(f"\n{'='*60}")
                
                # Determine output file path
                output_file = None
                if output_dir:
                    output_dir_path = Path(output_dir)
                    output_dir_path.mkdir(exist_ok=True)
                    output_file = output_dir_path / f"{pdf_file.stem}_questions.txt"
                
                # Process the PDF
                result = self.process_pdf(str(pdf_file), str(output_file) if output_file else None, json_output=True)
                result['pdf_file'] = str(pdf_file)
                results.append(result)
                
                # Print summary
                if result['status'] == 'success':
                    print(f"✅ {pdf_file.name}: JSON extracted successfully")
                elif result['status'] == 'warning':
                    print(f"⚠️ {pdf_file.name}: {result['message']}")
                else:
                    print(f"❌ {pdf_file.name}: {result['message']}")
            
            return {
                'status': 'success',
                'message': f'Processed {len(pdf_files)} PDF files',
                'results': results
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error processing directory: {e}',
                'results': []
            }

def main():
    """Main function to handle command line arguments and execute processing"""
    parser = argparse.ArgumentParser(
        description='Extract questions/solutions from PDF documents using Mistral AI. Questions include answers from PDF answer keys. Solutions extract detailed step-by-step procedures.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python pdf_question_parser.py document.pdf
  python pdf_question_parser.py document.pdf -o output.txt
  python pdf_question_parser.py document.pdf -s -o solutions.json
  python pdf_question_parser.py -d ./pdfs/ -od ./outputs/
  python pdf_question_parser.py --list-pdfs
        """
    )
    
    parser.add_argument('pdf_file', nargs='?', help='Path to PDF file to process')
    parser.add_argument('-o', '--output', help='Output file path for formatted questions or solutions')
    parser.add_argument('-j', '--json', action='store_true', help='Also save output as JSON with enhanced structure including answers extracted from PDF answer key sections')
    parser.add_argument('-s', '--solutions', action='store_true', help='Extract detailed solution steps and procedures instead of questions')
    parser.add_argument('-d', '--directory', help='Process all PDF files in directory')
    parser.add_argument('-od', '--output-dir', help='Output directory for results')
    parser.add_argument('--list-pdfs', action='store_true', help='List all PDF files in current directory')
    parser.add_argument('--api-key-file', default='key.txt', help='Path to API key file (default: key.txt)')
    parser.add_argument('--ai-provider', choices=['mistral', 'gemini'], default='mistral',
                       help='AI provider for text analysis (default: mistral). OCR always uses Mistral.')

    args = parser.parse_args()
    
    # List PDFs option
    if args.list_pdfs:
        pdf_files = list(Path('.').glob('*.pdf'))
        if pdf_files:
            print("📄 PDF files found in current directory:")
            for i, pdf_file in enumerate(pdf_files, 1):
                print(f"  {i}. {pdf_file.name}")
        else:
            print("❌ No PDF files found in current directory")
        return
    
    # Initialize parser
    try:
        print(f"🤖 Initializing with {args.ai_provider.upper()} AI provider...")
        parser_instance = PDFQuestionParser(args.api_key_file, ai_provider=args.ai_provider)
    except Exception as e:
        print(f"❌ Error initializing parser: {e}")
        sys.exit(1)
    
    # Process directory
    if args.directory:
        result = parser_instance.process_directory(args.directory, args.output_dir)
        
        if result['status'] == 'success':
            print(f"\n✅ {result['message']}")
            print(f"📊 JSON files generated for all PDFs")
        else:
            print(f"\n❌ {result['message']}")
            sys.exit(1)
    
    # Process single file
    elif args.pdf_file:
        result = parser_instance.process_pdf(args.pdf_file, args.output, json_output=args.json, extract_solutions=args.solutions)
        
        if result['status'] == 'success':
            print(f"\n✅ {result['message']}")

            if not args.output:
                print("\n📝 Raw JSON output:")
                print("="*60)
                print(result['formatted_text'])
        else:
            print(f"\n❌ {result['message']}")
            sys.exit(1)
    
    # No arguments provided
    else:
        # Try to process PDFs in current directory
        result = parser_instance.process_directory('.', args.output_dir)
        
        if result['status'] == 'success' and result['results']:
            print(f"\n✅ {result['message']}")
            print(f"📊 JSON files generated for all PDFs")
        else:
            print("❌ No PDF file specified and no PDFs found in current directory")
            print("Use --help for usage information")
            sys.exit(1)

if __name__ == "__main__":
    main()
