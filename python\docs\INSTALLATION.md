# Installation Guide

## Prerequisites

- **Python 3.7 or higher** - Check your version with `python --version`
- **Mistral AI API Key** - Sign up at [Mistral AI](https://mistral.ai/) to get your API key
- **Internet connection** - Required for API calls to Mistral AI services

## Quick Installation

### 1. Install Dependencies

```bash
# Install the required packages
pip install -r requirements.txt
```

### 2. Set Up API Key

Create a file named `key.txt` in the project directory and add your Mistral AI API key:

```bash
echo "your-mistral-api-key-here" > key.txt
```

**Important:** Keep your API key secure and never commit it to version control.

### 3. Test Installation

```bash
# Test with a sample PDF (if available)
python pdf_question_parser.py --help

# List available PDFs in current directory
python pdf_question_parser.py --list-pdfs
```

## Detailed Installation Options

### Option 1: Basic Installation

For basic usage with minimal dependencies:

```bash
# Clone or download the project
git clone <repository-url>
cd pdf-question-solution-extractor

# Install only required dependencies
pip install mistralai>=1.0.0

# Set up API key
echo "your-api-key" > key.txt
```

### Option 2: Manual Installation

For manual installation with specific versions:

```bash
# Install specific version of mistralai
pip install mistralai>=1.0.0

# Set up API key
echo "your-api-key" > key.txt
```

## Virtual Environment Setup (Recommended)

Using a virtual environment isolates dependencies:

```bash
# Create virtual environment
python -m venv pdf-extractor-env

# Activate virtual environment
# On Windows:
pdf-extractor-env\Scripts\activate
# On macOS/Linux:
source pdf-extractor-env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set up API key
echo "your-api-key" > key.txt
```

## Docker Installation (Optional)

Create a Dockerfile for containerized deployment:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Note: API key should be provided via environment variable
ENV MISTRAL_API_KEY=""

CMD ["python", "pdf_question_parser.py", "--help"]
```

Build and run:

```bash
docker build -t pdf-extractor .
docker run -e MISTRAL_API_KEY="your-key" -v $(pwd)/pdfs:/app/pdfs pdf-extractor
```

## Configuration

### API Key Configuration

You can provide the API key in several ways:

1. **File-based (default):**
   ```bash
   echo "your-api-key" > key.txt
   ```

2. **Environment variable:**
   ```bash
   export MISTRAL_API_KEY="your-api-key"
   ```

3. **Custom file location:**
   ```bash
   python pdf_question_parser.py document.pdf --api-key-file /path/to/key.txt
   ```

### Verify Installation

Test your installation with these commands:

```bash
# Check Python version
python --version

# Check if mistralai package is installed
python -c "import mistralai; print('Mistral AI client installed successfully')"

# Test the main script
python pdf_question_parser.py --help

# List available PDFs in current directory
python pdf_question_parser.py --list-pdfs
```

## Troubleshooting

### Common Issues

1. **"Python not found" error:**
   - Install Python 3.7+ from [python.org](https://python.org)
   - On Windows, ensure Python is added to PATH

2. **"mistralai module not found":**
   ```bash
   pip install mistralai
   ```

3. **"API key file not found":**
   - Create `key.txt` file with your API key
   - Or use `--api-key-file` parameter

4. **Permission errors:**
   ```bash
   # Use --user flag for user-level installation
   pip install --user -r requirements.txt
   ```

5. **SSL/Certificate errors:**
   ```bash
   # Upgrade pip and certificates
   pip install --upgrade pip certifi
   ```

### Getting Help

- Check the [README.md](README.md) for usage examples
- Review [SOLUTION_EXTRACTION_GUIDE.md](SOLUTION_EXTRACTION_GUIDE.md) for detailed features
- Run `python pdf_question_parser.py --help` for command-line options

## System Requirements

### Minimum Requirements
- **OS:** Windows 10, macOS 10.14, or Linux (Ubuntu 18.04+)
- **RAM:** 4GB (8GB recommended for large PDFs)
- **Storage:** 100MB for installation + space for PDF files
- **Network:** Stable internet connection for API calls

### Recommended Requirements
- **OS:** Latest versions of Windows, macOS, or Linux
- **RAM:** 8GB or more
- **Storage:** 1GB+ for processing multiple large PDFs
- **Network:** High-speed internet for faster processing

## Next Steps

After installation:

1. **Read the documentation:**
   - [README.md](README.md) - Basic usage
   - [SOLUTION_EXTRACTION_GUIDE.md](SOLUTION_EXTRACTION_GUIDE.md) - Advanced features

2. **Try the examples:**
   ```bash
   # Extract questions
   python pdf_question_parser.py sample.pdf

   # Extract solutions
   python pdf_question_parser.py sample.pdf -s

   # Process multiple files
   python pdf_question_parser.py -d ./pdfs/ -od ./outputs/
   ```

3. **Test the functionality:**
   ```bash
   # Test with help command
   python pdf_question_parser.py --help
   ```