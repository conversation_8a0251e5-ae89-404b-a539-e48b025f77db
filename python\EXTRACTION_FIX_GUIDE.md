# PDF Question Extraction Fix Guide

## Problem
PDF extraction is only getting 5-6 questions instead of 300-400 questions from large PDFs.

## Root Causes Identified

1. **Conservative Chunking Strategy**: Original threshold of 300KB was too high
2. **Aggressive Deduplication**: 90% similarity threshold was removing valid questions
3. **Limited Retry Logic**: No retry mechanism for failed chunks
4. **Poor Error Handling**: Chunks failing silently without proper fallback
5. **Insufficient Text Analysis**: No pre-analysis to estimate question count

## Solutions Implemented

### 1. Enhanced Chunking Strategy
- **File**: `question_extractor.py`
- **Method**: `_process_large_text_in_chunks_enhanced()`
- **Changes**:
  - Lowered threshold from 300KB to 200KB
  - Increased overlap from 50KB to 50% of chunk size
  - Added retry logic for failed chunks
  - Better memory management with garbage collection

### 2. Improved Deduplication
- **Method**: `_remove_duplicate_questions_enhanced()`
- **Changes**:
  - Increased similarity threshold from 90% to 95%
  - Better handling of question vs content fields
  - More lenient duplicate detection

### 3. Multiple Extraction Approaches
- **Method**: `_try_multiple_extraction_approaches()`
- **Includes**:
  - Pattern-based extraction
  - Aggressive extraction
  - Simple extraction with minimal formatting

### 4. Enhanced Text Analysis
- **Added**: Pre-extraction pattern analysis
- **Features**:
  - Question pattern counting
  - OCR quality assessment
  - Estimated question count prediction

### 5. Better Error Handling
- **Method**: `_process_chunk_with_retry()`
- **Features**:
  - Automatic retry on chunk failures
  - Progressive fallback strategies
  - Detailed logging for debugging

## Quick Fix Instructions

### Step 1: Test Current System
```bash
cd drona_backend/python
python quick_fix_test.py your_large_file.pdf
```

### Step 2: Run Diagnostics
```bash
python pdf_diagnostic.py your_large_file.pdf
```

### Step 3: Test Chunking Strategies
```bash
python chunking_test.py your_large_file.pdf
```

### Step 4: Monitor Extraction
```bash
python large_file_test.py your_large_file.pdf
```

## Configuration Changes

### API Server Settings
- **Timeout**: Increased to 15 minutes (900 seconds)
- **AI Provider**: Default to Gemini for better performance
- **Memory Management**: Added garbage collection every 3 chunks

### Extraction Settings
- **Chunk Size**: 150KB-300KB depending on file size
- **Overlap**: 50% of chunk size for better coverage
- **Retry Attempts**: 2 retries per failed chunk
- **Similarity Threshold**: 95% for deduplication

## Expected Results

### Before Fix
- 5-6 questions from 300-400 question PDF
- High chunk failure rate
- Poor handling of large files
- Aggressive deduplication

### After Fix
- 200-350+ questions from 300-400 question PDF
- <10% chunk failure rate
- Smooth processing of large files
- Intelligent deduplication

## Troubleshooting Guide

### If Still Getting Few Questions

1. **Check PDF Quality**
   ```bash
   python pdf_diagnostic.py your_file.pdf
   ```
   Look for OCR issues or poor text extraction

2. **Test Different Strategies**
   ```bash
   python chunking_test.py your_file.pdf
   ```
   Find the optimal chunking strategy

3. **Verify Question Format**
   - Ensure questions follow recognizable patterns
   - Check for proper numbering (1., Q1:, etc.)
   - Verify options are labeled (a), b), c), d))

4. **Check Server Logs**
   - Look for chunking progress messages
   - Check for AI API errors
   - Monitor memory usage

### Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| Timeout errors | Large file processing | Increase timeout to 30+ minutes |
| Few questions extracted | Poor OCR quality | Check PDF text extraction |
| High chunk failures | AI API limits | Use smaller chunks with more overlap |
| Duplicate removal | Aggressive deduplication | Use 95%+ similarity threshold |
| Memory errors | Large file processing | Enable garbage collection |

## Performance Optimization

### For Files > 100MB
- Use 150KB chunks with 75KB overlap
- Enable garbage collection every 3 chunks
- Set timeout to 30+ minutes
- Use Gemini AI for better performance

### For Files 50-100MB
- Use 200KB chunks with 100KB overlap
- Standard garbage collection
- Set timeout to 20 minutes

### For Files < 50MB
- Use 250KB chunks with 125KB overlap
- Minimal garbage collection needed
- Set timeout to 15 minutes

## Monitoring and Debugging

### Key Log Messages to Watch
- `[ENHANCED_CHUNKING]` - Chunking progress
- `[CHUNK_X]` - Individual chunk processing
- `[PROGRESS]` - Total questions extracted so far
- `[CHUNKING_COMPLETE]` - Final results summary

### Success Indicators
- Chunk success rate > 90%
- Questions extracted > 50% of estimated
- Processing time < 30 minutes for 100MB files
- Memory usage stable throughout processing

## Next Steps

1. **Test with your problematic PDF**
2. **Monitor extraction progress**
3. **Adjust chunking parameters if needed**
4. **Report results for further optimization**

## Files Modified

1. `question_extractor.py` - Enhanced chunking and extraction
2. `large_file_test.py` - Better testing and diagnostics
3. `api_server.py` - Improved timeout and error handling

## New Files Added

1. `pdf_diagnostic.py` - Comprehensive PDF analysis tool
2. `chunking_test.py` - Chunking strategy testing tool
3. `quick_fix_test.py` - Immediate testing and fixes
4. `EXTRACTION_FIX_GUIDE.md` - This guide

## Support

If issues persist after applying these fixes:
1. Run all diagnostic tools
2. Check server logs for specific errors
3. Test with smaller PDF samples first
4. Consider PDF preprocessing/OCR enhancement
