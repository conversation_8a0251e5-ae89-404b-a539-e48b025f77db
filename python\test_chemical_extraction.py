#!/usr/bin/env python3
"""
Test script for Chemical Image Extraction
Tests the new chemical extraction system with a sample PDF
"""

import os
import sys
import json
import time
from chemical_image_extractor import ChemicalImageExtractor

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_chemical_extraction():
    """Test chemical extraction with a sample PDF"""
    
    # Check if we have a test PDF
    test_pdf = "maths.pdf"  # Use existing test PDF
    if not os.path.exists(test_pdf):
        log_print("❌ Test PDF not found. Please place a chemistry PDF as 'maths.pdf' in this directory.")
        return False
    
    log_print("🧪 Testing Chemical Image Extraction System")
    log_print("=" * 50)
    
    try:
        # Initialize chemical extractor
        adobe_credentials = "pdfservices-api-credentials.json"
        if not os.path.exists(adobe_credentials):
            adobe_credentials = None
            log_print("⚠️ Adobe credentials not found, using standard extraction")
        
        extractor = ChemicalImageExtractor(
            ai_provider='gemini',
            adobe_credentials=adobe_credentials
        )
        
        log_print(f"📄 Processing PDF: {test_pdf}")
        start_time = time.time()
        
        # Extract chemical questions
        result = extractor.extract_chemical_questions_with_images(test_pdf)
        
        duration = time.time() - start_time
        log_print(f"⏱️ Extraction completed in {duration:.2f} seconds")
        
        # Parse results
        questions_json = result.get('questions', '[]')
        try:
            questions = json.loads(questions_json) if questions_json else []
        except json.JSONDecodeError as e:
            log_print(f"❌ JSON parsing error: {e}")
            questions = []
        
        metadata = result.get('extraction_metadata', {})
        
        # Display results
        log_print("\n📊 EXTRACTION RESULTS")
        log_print("-" * 30)
        log_print(f"Total Questions: {len(questions)}")
        log_print(f"Total Images: {metadata.get('total_images', 0)}")
        log_print(f"Questions with Images: {metadata.get('questions_with_images', 0)}")
        log_print(f"Extraction Method: {metadata.get('extraction_method', 'unknown')}")
        log_print(f"Chemical Structures Detected: {metadata.get('chemical_structures_detected', False)}")
        
        # Show sample questions
        if questions:
            log_print("\n🔬 SAMPLE CHEMICAL QUESTIONS")
            log_print("-" * 40)
            
            for i, question in enumerate(questions[:3]):  # Show first 3 questions
                log_print(f"\nQuestion {i+1}:")
                content = question.get('content', '') or question.get('question', '')
                log_print(f"Content: {content[:100]}...")
                
                # Check for chemical images
                if 'imageUrl' in question and question['imageUrl']:
                    if isinstance(question['imageUrl'], dict):
                        log_print(f"Chemical Images: {list(question['imageUrl'].keys())}")
                    else:
                        log_print(f"Image URL: {str(question['imageUrl'])[:50]}...")
                
                # Check options
                if 'options' in question:
                    log_print(f"Options: {len(question['options'])} found")
                    for opt_key, opt_value in list(question['options'].items())[:2]:
                        if isinstance(opt_value, str) and opt_value.startswith('data:image/'):
                            log_print(f"  {opt_key}: [Chemical Structure Image]")
                        else:
                            log_print(f"  {opt_key}: {str(opt_value)[:50]}...")
        
        # Test specific chemical features
        log_print("\n🧬 CHEMICAL FEATURES ANALYSIS")
        log_print("-" * 35)
        
        chemical_questions = 0
        image_questions = 0
        formula_questions = 0
        
        for question in questions:
            content = str(question.get('content', '') or question.get('question', ''))
            
            # Check for chemical indicators
            chemical_indicators = [
                'reaction', 'molecule', 'compound', 'chemical', 'formula',
                'CH3', 'NH2', 'COOH', 'benzene', 'phenol', 'acid', 'base'
            ]
            
            if any(indicator.lower() in content.lower() for indicator in chemical_indicators):
                chemical_questions += 1
            
            if 'imageUrl' in question and question['imageUrl']:
                image_questions += 1
            
            if any(char in content for char in ['₂', '₃', '₄', '→', '⇌']):
                formula_questions += 1
        
        log_print(f"Chemical Questions: {chemical_questions}")
        log_print(f"Questions with Images: {image_questions}")
        log_print(f"Questions with Formulas: {formula_questions}")
        
        # Success criteria
        success = len(questions) > 0
        if success:
            log_print("\n✅ Chemical extraction test PASSED!")
        else:
            log_print("\n❌ Chemical extraction test FAILED!")
        
        return success
        
    except Exception as e:
        log_print(f"❌ Chemical extraction test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """Test the chemical extraction API endpoint"""
    log_print("\n🌐 Testing Chemical Extraction API Endpoint")
    log_print("=" * 45)
    
    try:
        import requests
        
        # Check if API server is running
        try:
            response = requests.get('http://localhost:5000/', timeout=5)
            log_print("✅ API server is running")
        except requests.exceptions.RequestException:
            log_print("❌ API server is not running on localhost:5000")
            log_print("Please start the API server first: python api_server.py")
            return False
        
        # Test chemical extraction endpoint
        test_pdf = "maths.pdf"
        if not os.path.exists(test_pdf):
            log_print("❌ Test PDF not found for API test")
            return False
        
        log_print(f"📤 Uploading {test_pdf} to chemical extraction endpoint...")
        
        with open(test_pdf, 'rb') as f:
            files = {'file': f}
            data = {'ai_provider': 'gemini'}
            
            response = requests.post(
                'http://localhost:5000/api/extract-chemical',
                files=files,
                data=data,
                timeout=300  # 5 minutes timeout
            )
        
        if response.status_code == 200:
            result = response.json()
            log_print("✅ Chemical extraction API test PASSED!")
            log_print(f"Questions extracted: {len(result.get('questions', []))}")
            log_print(f"Success: {result.get('success', False)}")
            return True
        else:
            log_print(f"❌ API test failed with status {response.status_code}")
            log_print(f"Response: {response.text[:200]}...")
            return False
            
    except ImportError:
        log_print("⚠️ requests library not available, skipping API test")
        return True
    except Exception as e:
        log_print(f"❌ API test failed: {e}")
        return False

def main():
    """Main test function"""
    log_print("🧪 CHEMICAL IMAGE EXTRACTION TEST SUITE")
    log_print("=" * 50)
    
    # Test 1: Direct extraction
    test1_passed = test_chemical_extraction()
    
    # Test 2: API endpoint
    test2_passed = test_api_endpoint()
    
    # Summary
    log_print("\n📋 TEST SUMMARY")
    log_print("=" * 20)
    log_print(f"Direct Extraction: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    log_print(f"API Endpoint: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        log_print("\n🎉 ALL TESTS PASSED! Chemical extraction system is working correctly.")
        return True
    else:
        log_print("\n⚠️ Some tests failed. Please check the logs above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
