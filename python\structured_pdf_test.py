#!/usr/bin/env python3
"""
Structured PDF Test Script

This script is specifically designed to test PDFs with the structure:
1. Answer key section (1) b  2) a  3) a  4) d)
2. Hints and solutions section with detailed explanations

This matches your JEE/NEET physics PDF format.
"""

import os
import sys
import json
import time
import requests
from question_extractor import QuestionExtractor

def analyze_structured_pdf(pdf_path):
    """
    Analyze the structured PDF to understand its format
    """
    print("🔍 [STRUCTURED_ANALYSIS] Analyzing structured PDF format...")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Extract OCR data
        print("📄 [OCR] Extracting text from PDF...")
        ocr_data = extractor.extract_ocr_data_from_pdf(pdf_path)
        full_text = ocr_data['full_text']
        
        print(f"✅ [OCR] Extracted {len(full_text):,} characters")
        
        # Analyze structure
        import re
        
        # Look for explicit questions (numbered format)
        question_patterns = re.findall(r'(\d+)\.\s+', full_text)
        print(f"📊 [EXPLICIT_QUESTIONS] Found {len(question_patterns)} numbered questions")

        if question_patterns:
            question_numbers = [int(x) for x in question_patterns]
            print(f"   Question range: {min(question_numbers)} to {max(question_numbers)}")
            print(f"   Sample question numbers: {question_numbers[:10]}")

        # Look for options patterns
        option_patterns = re.findall(r'[a-d]\)', full_text)
        print(f"📊 [OPTIONS] Found {len(option_patterns)} option markers")

        # Look for answer key patterns
        answer_patterns = re.findall(r'(\d+)\)\s*([abcdABCD])', full_text)
        print(f"📊 [ANSWER_KEY] Found {len(answer_patterns)} answer entries")

        if answer_patterns:
            print(f"   Sample answers: {answer_patterns[:10]}")
            max_question = max([int(x[0]) for x in answer_patterns])
            print(f"   Highest question number: {max_question}")

        # Look for solution patterns
        solution_sections = re.findall(r'(?i)hints?\s+and\s+solutions?|solutions?\s+and\s+hints?', full_text)
        print(f"📊 [SOLUTIONS] Found {len(solution_sections)} solution section headers")

        # Look for numbered solution entries
        solution_entries = re.findall(r'(\d+)\s*\([abcdABCD]\)', full_text)
        print(f"📊 [SOLUTION_ENTRIES] Found {len(solution_entries)} solution entries")

        if solution_entries:
            print(f"   Sample solution entries: {solution_entries[:5]}")
        
        # Show text samples
        print(f"\n📄 [TEXT_SAMPLE] First 1000 characters:")
        print(f"{full_text[:1000]}...")
        
        print(f"\n📄 [TEXT_SAMPLE] Last 1000 characters:")
        print(f"...{full_text[-1000:]}")
        
        return {
            'text_length': len(full_text),
            'question_count': len(question_patterns),
            'answer_count': len(answer_patterns),
            'solution_count': len(solution_entries),
            'max_question': max([int(x[0]) for x in answer_patterns]) if answer_patterns else 0,
            'full_text': full_text
        }
        
    except Exception as e:
        print(f"❌ [ANALYSIS_ERROR] Error analyzing PDF: {e}")
        return None

def test_structured_extraction(pdf_path, analysis_data):
    """
    Test the structured extraction method
    """
    print("\n🧪 [STRUCTURED_TEST] Testing structured extraction...")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Test the new structured extraction method
        print("🔄 [EXTRACTION] Running structured extraction...")
        start_time = time.time()
        
        result = extractor.extract_structured_questions_from_pdf(pdf_path)
        
        extraction_time = time.time() - start_time
        print(f"✅ [EXTRACTION] Completed in {extraction_time:.1f} seconds")
        
        # Parse results
        questions = json.loads(result)
        
        print(f"📊 [RESULTS] Extracted {len(questions)} questions")
        
        # Analyze results
        questions_with_answers = sum(1 for q in questions if q.get('answer'))
        questions_with_solutions = sum(1 for q in questions if q.get('solution'))
        questions_with_hints = sum(1 for q in questions if q.get('hints'))
        
        print(f"   Questions with answers: {questions_with_answers}")
        print(f"   Questions with solutions: {questions_with_solutions}")
        print(f"   Questions with hints: {questions_with_hints}")
        
        # Show sample questions
        print(f"\n📝 [SAMPLE_QUESTIONS] First 3 questions:")
        for i, question in enumerate(questions[:3]):
            print(f"\n   Question {i+1}:")
            print(f"   Q: {question.get('question', 'No question')[:100]}...")
            print(f"   Answer: {question.get('answer', 'No answer')}")
            if question.get('solution'):
                print(f"   Solution method: {question['solution'].get('methodology', 'N/A')}")
        
        # Check answer distribution
        answer_dist = {}
        for q in questions:
            answer = q.get('answer', 'Unknown')
            answer_dist[answer] = answer_dist.get(answer, 0) + 1
        
        print(f"\n📊 [ANSWER_DISTRIBUTION]:")
        for answer, count in sorted(answer_dist.items()):
            print(f"   {answer}: {count} questions")
        
        return len(questions)
        
    except Exception as e:
        print(f"❌ [EXTRACTION_ERROR] Error in structured extraction: {e}")
        return 0

def test_api_with_structured_pdf(pdf_path):
    """
    Test the API with the structured PDF
    """
    print("\n🌐 [API_TEST] Testing API with structured PDF...")
    
    server_url = "http://localhost:5000"
    
    try:
        # Check server health
        health_response = requests.get(f"{server_url}/", timeout=10)
        if health_response.status_code != 200:
            print(f"❌ [API_ERROR] Server not responding: {health_response.status_code}")
            return False
        
        print("✅ [API_HEALTH] Server is running")
        
        # Upload and process
        with open(pdf_path, 'rb') as f:
            files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
            data = {'ai_provider': 'gemini'}
            
            print("🔄 [API_UPLOAD] Uploading PDF to API...")
            
            # Extended timeout for processing
            timeout = 1800  # 30 minutes
            start_time = time.time()
            
            response = requests.post(
                f"{server_url}/api/extract",
                files=files,
                data=data,
                timeout=timeout
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                questions_count = len(result.get('data', []))
                print(f"✅ [API_SUCCESS] Extracted {questions_count} questions in {processing_time:.1f}s")
                
                # Show performance metrics if available
                if 'performance_metrics' in result:
                    metrics = result['performance_metrics']
                    print(f"📊 [PERFORMANCE]:")
                    print(f"   OCR duration: {metrics.get('ocr_duration', 0):.1f}s")
                    print(f"   AI duration: {metrics.get('ai_duration', 0):.1f}s")
                    print(f"   Text length: {metrics.get('ocr_text_chars', 0):,} chars")
                
                return questions_count
            else:
                print(f"❌ [API_ERROR] API failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   Response: {response.text[:200]}...")
                return 0
                
    except requests.exceptions.Timeout:
        print(f"⏰ [API_TIMEOUT] Request timed out after {timeout} seconds")
        return 0
    except Exception as e:
        print(f"❌ [API_ERROR] Error: {e}")
        return 0

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python structured_pdf_test.py <path_to_structured_pdf>")
        print("Example: python structured_pdf_test.py jee_physics_paper.pdf")
        print("\nThis script is designed for PDFs with:")
        print("1. Answer key section: '1) b  2) a  3) a  4) d'")
        print("2. Hints and solutions section with detailed explanations")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        sys.exit(1)
    
    file_size = os.path.getsize(pdf_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"🚀 Structured PDF Test Script")
    print(f"📄 File: {os.path.basename(pdf_path)}")
    print(f"📊 Size: {file_size_mb:.1f}MB")
    print("=" * 60)
    
    # Step 1: Analyze PDF structure
    print("📋 Step 1: Analyzing PDF structure...")
    analysis_data = analyze_structured_pdf(pdf_path)
    
    if not analysis_data:
        print("❌ PDF analysis failed. Cannot continue.")
        sys.exit(1)
    
    expected_questions = analysis_data['max_question']
    print(f"\n📊 [SUMMARY] Expected questions: {expected_questions}")
    
    # Step 2: Test structured extraction
    print("\n📋 Step 2: Testing structured extraction...")
    extracted_count = test_structured_extraction(pdf_path, analysis_data)
    
    # Step 3: Test API
    print("\n📋 Step 3: Testing API...")
    api_count = test_api_with_structured_pdf(pdf_path)
    
    # Final summary
    print(f"\n🎯 [FINAL_SUMMARY]")
    print(f"Expected questions: {expected_questions}")
    print(f"Direct extraction: {extracted_count}")
    print(f"API extraction: {api_count}")
    
    if extracted_count >= expected_questions * 0.8:
        print("✅ SUCCESS: Structured extraction is working well!")
    elif extracted_count >= expected_questions * 0.5:
        print("⚠️ PARTIAL: Structured extraction needs improvement")
    else:
        print("❌ FAILED: Structured extraction is not working properly")
        
        print(f"\n🔧 [TROUBLESHOOTING]:")
        print("1. Check if PDF has the expected structure")
        print("2. Verify answer key format: '1) b  2) a  3) a'")
        print("3. Ensure solutions section exists")
        print("4. Try with a smaller test PDF first")
    
    print(f"\n📞 [NEXT_STEPS]:")
    if extracted_count < expected_questions * 0.8:
        print("1. Run: python pdf_diagnostic.py your_file.pdf")
        print("2. Check server logs for detailed processing info")
        print("3. Verify PDF text quality and structure")
    else:
        print("1. The structured extraction is working!")
        print("2. You can now process your full 395-question PDF")
        print("3. Monitor the extraction progress in server logs")

if __name__ == "__main__":
    main()
