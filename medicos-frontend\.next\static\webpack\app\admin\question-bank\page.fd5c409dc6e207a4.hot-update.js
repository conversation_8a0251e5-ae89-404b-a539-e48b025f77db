"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        var _q_topicId;\n        let parsedOptions = [];\n        // Ensure options is an array and filter out null/undefined values\n        const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n        if (safeOptions.length > 0) {\n            if (typeof safeOptions[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = safeOptions[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = safeOptions.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = safeOptions.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                        imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                    }));\n            }\n        } else {\n            // Log warning for questions without valid options\n            console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n            // Fallback: create empty options if none exist\n            parsedOptions = [\n                {\n                    label: 'a',\n                    text: 'No options available'\n                },\n                {\n                    label: 'b',\n                    text: 'No options available'\n                },\n                {\n                    label: 'c',\n                    text: 'No options available'\n                },\n                {\n                    label: 'd',\n                    text: 'No options available'\n                }\n            ];\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus,\n            solution: q.solution,\n            hints: q.hints\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});