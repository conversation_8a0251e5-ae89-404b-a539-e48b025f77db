#!/usr/bin/env python3
"""
Adobe PDF Extract API Integration Module
Provides enhanced image and text extraction using Adobe PDF Services REST API
"""

import os
import json
import base64
import tempfile
import shutil
import time
import sys
import requests
import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class AdobePDFExtractor:
    """
    Adobe PDF Extract API integration using REST API for enhanced image and text extraction
    """

    def __init__(self, credentials_file_path: str = None, access_token: str = None):
        """
        Initialize Adobe PDF Extractor

        Args:
            credentials_file_path (str): Path to pdfservices-api-credentials.json
            access_token (str): Adobe PDF Services access token (optional, will be generated if not provided)
                               Can also be set via ADOBE_ACCESS_TOKEN environment variable
        """
        self.credentials_file_path = credentials_file_path or 'pdfservices-api-credentials.json'

        # Check for access token in multiple places (priority order):
        # 1. Provided parameter
        # 2. Environment variable
        # 3. Saved token file
        # 4. Will be generated from credentials
        self.access_token = access_token or os.getenv('ADOBE_ACCESS_TOKEN') or self._load_saved_token()

        self.client_id = None
        self.client_secret = None
        self.temp_dir = None

        # Adobe PDF Services API endpoints
        self.token_url = "https://pdf-services.adobe.io/token"
        self.assets_url = "https://pdf-services.adobe.io/assets"
        self.extract_url = "https://pdf-services.adobe.io/operation/extractpdf"

        # Initialize Adobe PDF Services client
        self._initialize_adobe_client()
    
    def _initialize_adobe_client(self):
        """Initialize Adobe PDF Services REST API client with credentials"""
        try:
            log_print("🔧 [ADOBE_INIT] Initializing Adobe PDF Services REST API client...")

            # Check if credentials file exists
            if not os.path.exists(self.credentials_file_path):
                raise FileNotFoundError(f"Adobe credentials file not found: {self.credentials_file_path}")

            # Load credentials from JSON file
            log_print("🔑 [ADOBE_INIT] Loading Adobe credentials...")
            with open(self.credentials_file_path, 'r') as f:
                credentials = json.load(f)

            # Extract client credentials
            client_credentials = credentials.get('client_credentials', {})
            self.client_id = client_credentials.get('client_id')
            self.client_secret = client_credentials.get('client_secret')

            if not self.client_id or not self.client_secret:
                raise ValueError("Missing client_id or client_secret in credentials file")

            log_print(f"✅ [ADOBE_INIT] Loaded credentials for client_id: {self.client_id[:8]}...")

            # Get access token if not provided
            if not self.access_token:
                log_print("🔑 [ADOBE_INIT] No access token found, generating from credentials...")
                self.access_token = self._get_access_token()
                log_print("✅ [ADOBE_INIT] Access token generated successfully")
            else:
                # Determine token source
                if os.getenv('ADOBE_ACCESS_TOKEN'):
                    token_source = "environment variable"
                elif self._load_saved_token():
                    token_source = "saved token file"
                else:
                    token_source = "parameter"

                log_print("✅ [ADOBE_INIT] Using existing access token")
                log_print(f"🔑 [ADOBE_INIT] Token source: {token_source}")

            log_print("✅ [ADOBE_INIT] Adobe PDF Services REST API client initialized successfully")

        except Exception as e:
            log_print(f"❌ [ADOBE_INIT] Failed to initialize Adobe PDF Services: {e}")
            raise e

    def _get_access_token(self) -> str:
        """Get access token from Adobe PDF Services"""
        try:
            log_print("🔑 [TOKEN] Requesting access token from Adobe...")

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }

            response = requests.post(self.token_url, headers=headers, data=data)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data.get('access_token')

            if not access_token:
                raise ValueError("No access token in response")

            log_print("✅ [TOKEN] Access token obtained successfully")
            return access_token

        except Exception as e:
            log_print(f"❌ [TOKEN] Failed to get access token: {e}")
            raise e

    def _load_saved_token(self, token_file='adobe_access_token.json'):
        """Load access token from saved file if valid"""
        try:
            if not os.path.exists(token_file):
                return None

            with open(token_file, 'r') as f:
                token_data = json.load(f)

            access_token = token_data.get('access_token')
            expires_at_str = token_data.get('expires_at')

            if not access_token or not expires_at_str:
                return None

            # Check if token is still valid (with 5-minute buffer)
            from datetime import datetime, timedelta
            expires_at = datetime.fromisoformat(expires_at_str)

            if datetime.now() + timedelta(minutes=5) < expires_at:
                log_print(f"🔑 [TOKEN] Found valid saved token in {token_file}")
                return access_token
            else:
                log_print(f"⚠️ [TOKEN] Saved token in {token_file} has expired")
                return None

        except Exception as e:
            log_print(f"⚠️ [TOKEN] Error loading saved token: {e}")
            return None
    
    def extract_images_and_text(self, pdf_path: str) -> Dict:
        """
        Extract images and text from PDF using Adobe PDF Extract REST API

        Args:
            pdf_path (str): Path to the PDF file

        Returns:
            Dict: Dictionary containing extracted text, images, and metadata
        """
        try:
            log_print("🚀 [ADOBE_EXTRACT] Starting Adobe PDF extraction via REST API...")
            start_time = time.time()

            # Create temporary directory for output
            self.temp_dir = tempfile.mkdtemp(prefix="adobe_pdf_extract_")
            log_print(f"📁 [ADOBE_EXTRACT] Created temp directory: {self.temp_dir}")

            # Step 1: Upload PDF asset
            log_print("📤 [ADOBE_EXTRACT] Uploading PDF asset...")
            asset_id = self._upload_pdf_asset(pdf_path)

            # Step 2: Create extraction job
            log_print("🔧 [ADOBE_EXTRACT] Creating extraction job...")
            job_location = self._create_extraction_job(asset_id)

            # Step 3: Poll for job completion
            log_print("⏳ [ADOBE_EXTRACT] Polling for job completion...")
            download_uri = self._poll_job_status(job_location)

            # Step 4: Download and process results
            log_print("📥 [ADOBE_EXTRACT] Downloading extraction results...")
            output_zip_path = self._download_results(download_uri)

            extraction_time = time.time() - start_time
            log_print(f"✅ [ADOBE_EXTRACT] Extraction completed in {extraction_time:.2f}s")

            # Process extracted content
            log_print("🔄 [ADOBE_PROCESS] Processing extracted content...")
            processed_data = self._process_extracted_content(output_zip_path)

            total_time = time.time() - start_time
            log_print(f"🎯 [ADOBE_COMPLETE] Total Adobe extraction time: {total_time:.2f}s")

            return processed_data

        except Exception as e:
            log_print(f"❌ [ADOBE_EXTRACT] Error during Adobe PDF extraction: {e}")
            raise e
        finally:
            # Cleanup temporary directory
            self._cleanup_temp_dir()

    def _upload_pdf_asset(self, pdf_path: str) -> str:
        """Upload PDF asset to Adobe and return asset ID"""
        try:
            # Step 1: Get upload pre-signed URI
            log_print("📤 [UPLOAD] Getting upload pre-signed URI...")

            headers = {
                'X-API-Key': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            data = {
                'mediaType': 'application/pdf'
            }

            response = requests.post(self.assets_url, headers=headers, json=data)
            response.raise_for_status()

            upload_data = response.json()
            upload_uri = upload_data.get('uploadUri')
            asset_id = upload_data.get('assetID')

            if not upload_uri or not asset_id:
                raise ValueError("Missing uploadUri or assetID in response")

            log_print(f"✅ [UPLOAD] Got upload URI and asset ID: {asset_id}")

            # Step 2: Upload PDF to pre-signed URI
            log_print("📤 [UPLOAD] Uploading PDF file...")

            with open(pdf_path, 'rb') as pdf_file:
                upload_headers = {
                    'Content-Type': 'application/pdf'
                }

                upload_response = requests.put(upload_uri, headers=upload_headers, data=pdf_file)
                upload_response.raise_for_status()

            log_print("✅ [UPLOAD] PDF uploaded successfully")
            return asset_id

        except Exception as e:
            log_print(f"❌ [UPLOAD] Failed to upload PDF asset: {e}")
            raise e

    def _create_extraction_job(self, asset_id: str) -> str:
        """Create extraction job and return job location"""
        try:
            log_print("🔧 [JOB] Creating extraction job...")

            headers = {
                'X-API-Key': self.client_id,
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            data = {
                'assetID': asset_id,
                'elementsToExtract': ['text', 'tables', 'figures'],
                'elementsToExtractRenditions': ['figures', 'tables']
            }

            response = requests.post(self.extract_url, headers=headers, json=data)
            response.raise_for_status()

            # Get job location from response headers
            job_location = response.headers.get('location')

            if not job_location:
                raise ValueError("No job location in response headers")

            log_print(f"✅ [JOB] Extraction job created: {job_location}")
            return job_location

        except Exception as e:
            log_print(f"❌ [JOB] Failed to create extraction job: {e}")
            raise e
    
    def _poll_job_status(self, job_location: str) -> str:
        """Poll job status until completion and return download URI"""
        try:
            log_print("⏳ [POLL] Polling job status...")

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'X-API-Key': self.client_id
            }

            max_attempts = 60  # 5 minutes with 5-second intervals
            attempt = 0

            while attempt < max_attempts:
                response = requests.get(job_location, headers=headers)
                response.raise_for_status()

                status_data = response.json()
                status = status_data.get('status')

                if status == 'done':
                    download_uri = status_data.get('downloadUri')
                    if not download_uri:
                        raise ValueError("No downloadUri in completed job response")

                    log_print("✅ [POLL] Job completed successfully")
                    return download_uri

                elif status == 'failed':
                    error_msg = status_data.get('error', 'Unknown error')
                    raise Exception(f"Job failed: {error_msg}")

                elif status == 'in progress':
                    attempt += 1
                    log_print(f"⏳ [POLL] Job in progress... (attempt {attempt}/{max_attempts})")
                    time.sleep(5)  # Wait 5 seconds before next poll

                else:
                    raise Exception(f"Unknown job status: {status}")

            raise Exception("Job polling timeout - job did not complete within expected time")

        except Exception as e:
            log_print(f"❌ [POLL] Failed to poll job status: {e}")
            raise e

    def _download_results(self, download_uri: str) -> str:
        """Download extraction results and return path to ZIP file"""
        try:
            log_print("📥 [DOWNLOAD] Downloading extraction results...")

            response = requests.get(download_uri)
            response.raise_for_status()

            # Save to temp directory
            output_zip_path = os.path.join(self.temp_dir, "extracted_content.zip")
            with open(output_zip_path, 'wb') as f:
                f.write(response.content)

            log_print(f"✅ [DOWNLOAD] Results downloaded to: {output_zip_path}")
            return output_zip_path

        except Exception as e:
            log_print(f"❌ [DOWNLOAD] Failed to download results: {e}")
            raise e

    def _process_extracted_content(self, zip_path: str) -> Dict:
        """
        Process the extracted ZIP content from Adobe API

        Args:
            zip_path (str): Path to the extracted ZIP file

        Returns:
            Dict: Processed content with text, images, and metadata
        """
        try:
            log_print("📦 [ADOBE_PROCESS] Extracting ZIP content...")

            # Extract ZIP file
            extract_dir = os.path.join(self.temp_dir, "extracted")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)

            # Find JSON structure file
            json_file_path = os.path.join(extract_dir, "structuredData.json")
            if not os.path.exists(json_file_path):
                raise FileNotFoundError("structuredData.json not found in extracted content")

            # Load JSON structure
            log_print("📄 [ADOBE_PROCESS] Loading structured data...")
            with open(json_file_path, 'r', encoding='utf-8') as f:
                structured_data = json.load(f)

            # Process images
            log_print("🖼️ [ADOBE_PROCESS] Processing extracted images...")
            images_data = self._process_extracted_images(extract_dir, structured_data)

            # Process text
            log_print("📝 [ADOBE_PROCESS] Processing extracted text...")
            text_data = self._process_extracted_text(structured_data)

            # Create final result
            result = {
                'text_content': text_data,
                'images': images_data,
                'structured_data': structured_data,
                'extraction_metadata': {
                    'total_pages': len(structured_data.get('pages', [])),
                    'total_images': len(images_data),
                    'extraction_method': 'adobe_pdf_extract_rest_api'
                }
            }

            log_print(f"📊 [ADOBE_PROCESS] Processed {result['extraction_metadata']['total_pages']} pages, {result['extraction_metadata']['total_images']} images")

            return result

        except Exception as e:
            log_print(f"❌ [ADOBE_PROCESS] Error processing extracted content: {e}")
            raise e
    
    def _process_extracted_images(self, extract_dir: str, structured_data: Dict) -> Dict[str, str]:
        """
        Process extracted images and convert to base64
        
        Args:
            extract_dir (str): Directory containing extracted files
            structured_data (Dict): Structured data from Adobe API
            
        Returns:
            Dict[str, str]: Dictionary mapping image IDs to base64 data
        """
        images_data = {}
        
        try:
            # Find figures directory
            figures_dir = os.path.join(extract_dir, "figures")
            if not os.path.exists(figures_dir):
                log_print("⚠️ [ADOBE_IMAGES] No figures directory found")
                return images_data
            
            # Process each image file
            for filename in os.listdir(figures_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_path = os.path.join(figures_dir, filename)
                    
                    # Convert to base64
                    with open(image_path, 'rb') as img_file:
                        image_data = base64.b64encode(img_file.read()).decode('utf-8')
                        
                    # Create data URL
                    file_ext = filename.lower().split('.')[-1]
                    mime_type = f"image/{file_ext if file_ext != 'jpg' else 'jpeg'}"
                    data_url = f"data:{mime_type};base64,{image_data}"
                    
                    # Use filename as key (Adobe uses figure-X.png format)
                    image_key = filename.replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
                    images_data[image_key] = data_url
                    
                    log_print(f"🖼️ [ADOBE_IMAGES] Processed image: {filename} -> {image_key}")
            
            log_print(f"✅ [ADOBE_IMAGES] Processed {len(images_data)} images")
            
        except Exception as e:
            log_print(f"❌ [ADOBE_IMAGES] Error processing images: {e}")
        
        return images_data
    
    def _process_extracted_text(self, structured_data: Dict) -> str:
        """
        Process extracted text from structured data
        
        Args:
            structured_data (Dict): Structured data from Adobe API
            
        Returns:
            str: Combined text content
        """
        try:
            text_content = ""
            
            # Extract text from each page
            for page in structured_data.get('pages', []):
                for element in page.get('elements', []):
                    if element.get('type') == 'text':
                        text_content += element.get('text', '') + "\n"
            
            log_print(f"📝 [ADOBE_TEXT] Extracted {len(text_content)} characters of text")
            
            return text_content
            
        except Exception as e:
            log_print(f"❌ [ADOBE_TEXT] Error processing text: {e}")
            return ""
    
    def _cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                log_print("🧹 [ADOBE_CLEANUP] Temporary directory cleaned up")
            except Exception as e:
                log_print(f"⚠️ [ADOBE_CLEANUP] Error cleaning up temp directory: {e}")
