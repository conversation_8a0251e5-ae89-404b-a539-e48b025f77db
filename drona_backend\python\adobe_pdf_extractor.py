#!/usr/bin/env python3
"""
Adobe PDF Extract API Integration Module
Provides enhanced image and text extraction using Adobe PDF Services SDK
"""

import os
import json
import base64
import tempfile
import shutil
import time
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class AdobePDFExtractor:
    """
    Adobe PDF Extract API integration for enhanced image and text extraction
    """
    
    def __init__(self, credentials_file_path: str = None):
        """
        Initialize Adobe PDF Extractor
        
        Args:
            credentials_file_path (str): Path to pdfservices-api-credentials.json
        """
        self.credentials_file_path = credentials_file_path or 'pdfservices-api-credentials.json'
        self.client = None
        self.temp_dir = None
        
        # Initialize Adobe PDF Services client
        self._initialize_adobe_client()
    
    def _initialize_adobe_client(self):
        """Initialize Adobe PDF Services client with credentials"""
        try:
            log_print("🔧 [ADOBE_INIT] Initializing Adobe PDF Services client...")
            
            # Check if credentials file exists
            if not os.path.exists(self.credentials_file_path):
                raise FileNotFoundError(f"Adobe credentials file not found: {self.credentials_file_path}")
            
            # Import Adobe PDF Services SDK
            try:
                from adobe.pdfservices.operation.auth.credentials import Credentials
                from adobe.pdfservices.operation.client_config import ClientConfig
                from adobe.pdfservices.operation.execution_context import ExecutionContext
                from adobe.pdfservices.operation.pdfops.extract_pdf_operation import ExtractPDFOperation
                from adobe.pdfservices.operation.pdfops.options.extractpdf.extract_pdf_options import ExtractPDFOptions
                from adobe.pdfservices.operation.pdfops.options.extractpdf.extract_element_type import ExtractElementType
                from adobe.pdfservices.operation.io.file_ref import FileRef
                
                log_print("✅ [ADOBE_INIT] Adobe PDF Services SDK imported successfully")
                
                # Store classes for later use
                self.Credentials = Credentials
                self.ClientConfig = ClientConfig
                self.ExecutionContext = ExecutionContext
                self.ExtractPDFOperation = ExtractPDFOperation
                self.ExtractPDFOptions = ExtractPDFOptions
                self.ExtractElementType = ExtractElementType
                self.FileRef = FileRef
                
            except ImportError as e:
                raise ImportError(f"Adobe PDF Services SDK not installed. Run: pip install pdfservices-sdk. Error: {e}")
            
            # Initialize credentials
            log_print("🔑 [ADOBE_INIT] Loading Adobe credentials...")
            credentials = self.Credentials.service_account_credentials_builder() \
                .from_file(self.credentials_file_path) \
                .build()
            
            # Create client config
            client_config = self.ClientConfig.builder().build()
            
            # Create execution context
            self.execution_context = self.ExecutionContext.create(credentials, client_config)
            
            log_print("✅ [ADOBE_INIT] Adobe PDF Services client initialized successfully")
            
        except Exception as e:
            log_print(f"❌ [ADOBE_INIT] Failed to initialize Adobe PDF Services: {e}")
            raise e
    
    def extract_images_and_text(self, pdf_path: str) -> Dict:
        """
        Extract images and text from PDF using Adobe PDF Extract API
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            Dict: Dictionary containing extracted text, images, and metadata
        """
        try:
            log_print("🚀 [ADOBE_EXTRACT] Starting Adobe PDF extraction...")
            start_time = time.time()
            
            # Create temporary directory for output
            self.temp_dir = tempfile.mkdtemp(prefix="adobe_pdf_extract_")
            log_print(f"📁 [ADOBE_EXTRACT] Created temp directory: {self.temp_dir}")
            
            # Create file reference from PDF
            log_print("📄 [ADOBE_EXTRACT] Creating file reference...")
            source_file_ref = self.FileRef.create_from_local_file(pdf_path)
            
            # Create extract PDF operation
            log_print("🔧 [ADOBE_EXTRACT] Setting up extraction operation...")
            extract_pdf_operation = self.ExtractPDFOperation.create_new()
            extract_pdf_operation.set_input(source_file_ref)
            
            # Configure extraction options
            extract_pdf_options = self.ExtractPDFOptions.builder() \
                .add_elements_to_extract([
                    self.ExtractElementType.TEXT,
                    self.ExtractElementType.TABLES,
                    self.ExtractElementType.FIGURES
                ]) \
                .add_elements_to_extract_renditions([
                    self.ExtractElementType.FIGURES,
                    self.ExtractElementType.TABLES
                ]) \
                .build()
            
            extract_pdf_operation.set_options(extract_pdf_options)
            
            # Execute extraction
            log_print("⚡ [ADOBE_EXTRACT] Executing PDF extraction...")
            result = extract_pdf_operation.execute(self.execution_context)
            
            # Save result to temp directory
            output_zip_path = os.path.join(self.temp_dir, "extracted_content.zip")
            result.save_as(output_zip_path)
            
            extraction_time = time.time() - start_time
            log_print(f"✅ [ADOBE_EXTRACT] Extraction completed in {extraction_time:.2f}s")
            
            # Process extracted content
            log_print("🔄 [ADOBE_PROCESS] Processing extracted content...")
            processed_data = self._process_extracted_content(output_zip_path)
            
            total_time = time.time() - start_time
            log_print(f"🎯 [ADOBE_COMPLETE] Total Adobe extraction time: {total_time:.2f}s")
            
            return processed_data
            
        except Exception as e:
            log_print(f"❌ [ADOBE_EXTRACT] Error during Adobe PDF extraction: {e}")
            raise e
        finally:
            # Cleanup temporary directory
            self._cleanup_temp_dir()
    
    def _process_extracted_content(self, zip_path: str) -> Dict:
        """
        Process the extracted ZIP content from Adobe API
        
        Args:
            zip_path (str): Path to the extracted ZIP file
            
        Returns:
            Dict: Processed content with text, images, and metadata
        """
        try:
            import zipfile
            
            log_print("📦 [ADOBE_PROCESS] Extracting ZIP content...")
            
            # Extract ZIP file
            extract_dir = os.path.join(self.temp_dir, "extracted")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Find JSON structure file
            json_file_path = os.path.join(extract_dir, "structuredData.json")
            if not os.path.exists(json_file_path):
                raise FileNotFoundError("structuredData.json not found in extracted content")
            
            # Load JSON structure
            log_print("📄 [ADOBE_PROCESS] Loading structured data...")
            with open(json_file_path, 'r', encoding='utf-8') as f:
                structured_data = json.load(f)
            
            # Process images
            log_print("🖼️ [ADOBE_PROCESS] Processing extracted images...")
            images_data = self._process_extracted_images(extract_dir, structured_data)
            
            # Process text
            log_print("📝 [ADOBE_PROCESS] Processing extracted text...")
            text_data = self._process_extracted_text(structured_data)
            
            # Create final result
            result = {
                'text_content': text_data,
                'images': images_data,
                'structured_data': structured_data,
                'extraction_metadata': {
                    'total_pages': len(structured_data.get('pages', [])),
                    'total_images': len(images_data),
                    'extraction_method': 'adobe_pdf_extract_api'
                }
            }
            
            log_print(f"📊 [ADOBE_PROCESS] Processed {result['extraction_metadata']['total_pages']} pages, {result['extraction_metadata']['total_images']} images")
            
            return result
            
        except Exception as e:
            log_print(f"❌ [ADOBE_PROCESS] Error processing extracted content: {e}")
            raise e
    
    def _process_extracted_images(self, extract_dir: str, structured_data: Dict) -> Dict[str, str]:
        """
        Process extracted images and convert to base64
        
        Args:
            extract_dir (str): Directory containing extracted files
            structured_data (Dict): Structured data from Adobe API
            
        Returns:
            Dict[str, str]: Dictionary mapping image IDs to base64 data
        """
        images_data = {}
        
        try:
            # Find figures directory
            figures_dir = os.path.join(extract_dir, "figures")
            if not os.path.exists(figures_dir):
                log_print("⚠️ [ADOBE_IMAGES] No figures directory found")
                return images_data
            
            # Process each image file
            for filename in os.listdir(figures_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_path = os.path.join(figures_dir, filename)
                    
                    # Convert to base64
                    with open(image_path, 'rb') as img_file:
                        image_data = base64.b64encode(img_file.read()).decode('utf-8')
                        
                    # Create data URL
                    file_ext = filename.lower().split('.')[-1]
                    mime_type = f"image/{file_ext if file_ext != 'jpg' else 'jpeg'}"
                    data_url = f"data:{mime_type};base64,{image_data}"
                    
                    # Use filename as key (Adobe uses figure-X.png format)
                    image_key = filename.replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
                    images_data[image_key] = data_url
                    
                    log_print(f"🖼️ [ADOBE_IMAGES] Processed image: {filename} -> {image_key}")
            
            log_print(f"✅ [ADOBE_IMAGES] Processed {len(images_data)} images")
            
        except Exception as e:
            log_print(f"❌ [ADOBE_IMAGES] Error processing images: {e}")
        
        return images_data
    
    def _process_extracted_text(self, structured_data: Dict) -> str:
        """
        Process extracted text from structured data
        
        Args:
            structured_data (Dict): Structured data from Adobe API
            
        Returns:
            str: Combined text content
        """
        try:
            text_content = ""
            
            # Extract text from each page
            for page in structured_data.get('pages', []):
                for element in page.get('elements', []):
                    if element.get('type') == 'text':
                        text_content += element.get('text', '') + "\n"
            
            log_print(f"📝 [ADOBE_TEXT] Extracted {len(text_content)} characters of text")
            
            return text_content
            
        except Exception as e:
            log_print(f"❌ [ADOBE_TEXT] Error processing text: {e}")
            return ""
    
    def _cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                log_print("🧹 [ADOBE_CLEANUP] Temporary directory cleaned up")
            except Exception as e:
                log_print(f"⚠️ [ADOBE_CLEANUP] Error cleaning up temp directory: {e}")
