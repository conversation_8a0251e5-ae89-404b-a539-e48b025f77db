#!/usr/bin/env python3
"""
Install Image Processing Dependencies
Installs required packages for superior image extraction
"""

import subprocess
import sys
import os

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def install_package(package_name, import_name=None):
    """Install a package using pip"""
    try:
        # Check if already installed
        if import_name:
            try:
                __import__(import_name)
                log_print(f"✅ {package_name} is already installed")
                return True
            except ImportError:
                pass
        
        log_print(f"📦 Installing {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            log_print(f"✅ Successfully installed {package_name}")
            return True
        else:
            log_print(f"❌ Failed to install {package_name}: {result.stderr}")
            return False
            
    except Exception as e:
        log_print(f"❌ Error installing {package_name}: {e}")
        return False

def main():
    """Install all required dependencies"""
    log_print("🚀 Installing Superior Image Extraction Dependencies...")
    log_print("=" * 60)
    
    # List of required packages
    packages = [
        ("PyMuPDF", "fitz"),  # Direct PDF image extraction
        ("Pillow", "PIL"),    # Image processing
        ("opencv-python", "cv2"),  # Advanced image processing
        ("numpy", "numpy"),   # Numerical operations
    ]
    
    success_count = 0
    total_packages = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    log_print("\n" + "=" * 60)
    log_print(f"📊 Installation Summary: {success_count}/{total_packages} packages installed")
    
    if success_count == total_packages:
        log_print("🎉 All dependencies installed successfully!")
        log_print("\n💡 You can now use the Superior Image Extraction System:")
        log_print("  python test_superior_images.py --pdf your_file.pdf")
    else:
        log_print("⚠️ Some packages failed to install. You may need to:")
        log_print("  1. Update pip: python -m pip install --upgrade pip")
        log_print("  2. Install manually: pip install PyMuPDF Pillow opencv-python numpy")
        log_print("  3. Check your Python environment")
    
    return success_count == total_packages

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
