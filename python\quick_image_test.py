#!/usr/bin/env python3
"""
Quick test for Superior Image Extraction System
"""

import os
import sys
import requests
import json
from pathlib import Path

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_api_with_image_extraction(pdf_path):
    """Test the API with different image extraction methods"""
    
    # Test methods
    methods = [
        ('Standard', {'use_image_enhanced': 'false'}),
        ('Image-Enhanced', {'use_image_enhanced': 'true'})
    ]
    
    results = {}
    
    for method_name, params in methods:
        try:
            log_print(f"🔄 Testing {method_name} extraction...")
            
            with open(pdf_path, 'rb') as f:
                files = {'file': f}
                data = {
                    'ai_provider': 'gemini',
                    **params
                }
                
                response = requests.post(
                    'http://localhost:5000/api/extract', 
                    files=files, 
                    data=data, 
                    timeout=300  # 5 minutes
                )
            
            if response.status_code == 200:
                result = response.json()
                questions_count = result.get('questions_count', 0)
                performance_metrics = result.get('performance_metrics', {})
                
                results[method_name] = {
                    'questions': questions_count,
                    'images': performance_metrics.get('total_images', 0),
                    'questions_with_images': performance_metrics.get('questions_with_images', 0),
                    'extraction_method': performance_metrics.get('extraction_method', 'unknown'),
                    'success': True
                }
                
                log_print(f"✅ {method_name}: {questions_count} questions")
                if 'total_images' in performance_metrics:
                    log_print(f"   📊 Images: {performance_metrics['total_images']}")
                if 'questions_with_images' in performance_metrics:
                    log_print(f"   📊 Questions with images: {performance_metrics['questions_with_images']}")
                
            else:
                log_print(f"❌ {method_name}: Failed with status {response.status_code}")
                results[method_name] = {'success': False, 'error': response.text[:200]}
                
        except Exception as e:
            log_print(f"❌ {method_name}: Error - {e}")
            results[method_name] = {'success': False, 'error': str(e)}
    
    return results

def main():
    """Main test function"""
    log_print("🖼️ Quick Image Extraction Test")
    log_print("=" * 40)
    
    # Check if server is running
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            log_print("❌ API server not responding correctly")
            return False
    except requests.exceptions.ConnectionError:
        log_print("❌ Cannot connect to API server")
        log_print("💡 Start the server with: python api_server.py")
        return False
    
    log_print("✅ API server is running")
    
    # Find a PDF file to test
    pdf_files = list(Path('.').glob('*.pdf'))
    if not pdf_files:
        log_print("❌ No PDF files found in current directory")
        log_print("💡 Place a PDF file in the python directory and run again")
        return False
    
    pdf_path = str(pdf_files[0])
    log_print(f"📄 Testing with: {pdf_path}")
    
    # Test image extraction
    results = test_api_with_image_extraction(pdf_path)
    
    # Display comparison
    log_print("\n📊 Results Comparison:")
    log_print("-" * 40)
    
    for method, result in results.items():
        if result.get('success'):
            log_print(f"{method}:")
            log_print(f"  Questions: {result.get('questions', 0)}")
            log_print(f"  Images: {result.get('images', 0)}")
            log_print(f"  Questions with images: {result.get('questions_with_images', 0)}")
            log_print(f"  Method: {result.get('extraction_method', 'unknown')}")
        else:
            log_print(f"{method}: FAILED - {result.get('error', 'Unknown error')}")
    
    # Check if image-enhanced is better
    if 'Image-Enhanced' in results and 'Standard' in results:
        if (results['Image-Enhanced'].get('success') and 
            results['Standard'].get('success')):
            
            enhanced_images = results['Image-Enhanced'].get('images', 0)
            standard_images = results['Standard'].get('images', 0)
            
            if enhanced_images > standard_images:
                improvement = enhanced_images - standard_images
                log_print(f"\n🎉 Image-Enhanced extracted {improvement} more images!")
                log_print("✅ Superior Image Extraction is working correctly!")
            else:
                log_print(f"\n⚠️ No significant image improvement detected")
                log_print("💡 This PDF might not have many images, or they're already well-extracted")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
