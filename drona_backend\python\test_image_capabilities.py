#!/usr/bin/env python3
"""
Test Image Extraction Capabilities
Comprehensive test of the superior image extraction system
"""

import os
import sys
import requests
import json
from pathlib import Path

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_image_extraction_direct():
    """Test image extraction directly using the superior image extractor"""
    try:
        log_print("🖼️ Testing Superior Image Extractor directly...")
        
        from superior_image_extractor import SuperiorImageExtractor
        
        # Find PDF file
        pdf_files = list(Path('.').glob('*.pdf'))
        if not pdf_files:
            log_print("❌ No PDF files found")
            return False
        
        pdf_path = str(pdf_files[0])
        log_print(f"📄 Testing with: {pdf_path}")
        
        # Test image extraction
        extractor = SuperiorImageExtractor()
        result = extractor.extract_all_images(pdf_path)
        
        images = result.get('images', {})
        image_mapping = result.get('image_mapping', {})
        metadata = result.get('extraction_metadata', {})
        
        log_print(f"✅ Direct image extraction completed")
        log_print(f"📊 Total images found: {len(images)}")
        log_print(f"📊 Image mapping created for: {len(image_mapping)} questions")
        log_print(f"📊 Extraction methods used: {', '.join(metadata.get('extraction_methods', []))}")
        
        # Show image details
        if images:
            log_print("📋 Image details:")
            image_sources = {}
            for img_id, img_info in images.items():
                source = img_info.get('source', 'unknown')
                quality = img_info.get('quality', 'unknown')
                if source not in image_sources:
                    image_sources[source] = {'count': 0, 'qualities': []}
                image_sources[source]['count'] += 1
                image_sources[source]['qualities'].append(quality)
            
            for source, info in image_sources.items():
                qualities = ', '.join(set(info['qualities']))
                log_print(f"   {source}: {info['count']} images ({qualities} quality)")
        else:
            log_print("📋 No images found in this PDF")
            log_print("💡 This PDF might be text-only or have embedded images that require different extraction methods")
        
        return len(images) > 0
        
    except Exception as e:
        log_print(f"❌ Direct image extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_enhanced_extraction():
    """Test the complete image-enhanced extraction pipeline"""
    try:
        log_print("🚀 Testing Image-Enhanced Question Extractor...")
        
        from image_enhanced_extractor import ImageEnhancedExtractor
        
        # Find PDF file
        pdf_files = list(Path('.').glob('*.pdf'))
        if not pdf_files:
            log_print("❌ No PDF files found")
            return False
        
        pdf_path = str(pdf_files[0])
        
        # Test image-enhanced extraction
        extractor = ImageEnhancedExtractor(ai_provider='gemini')
        result = extractor.extract_questions_with_superior_images(pdf_path)
        
        questions_json = result.get('questions', '[]')
        questions = json.loads(questions_json) if questions_json else []
        metadata = result.get('extraction_metadata', {})
        
        log_print(f"✅ Image-enhanced extraction completed")
        log_print(f"📊 Questions extracted: {len(questions)}")
        log_print(f"📊 Total images: {metadata.get('total_images', 0)}")
        log_print(f"📊 Questions with images: {metadata.get('questions_with_images', 0)}")
        log_print(f"📊 Extraction method: {metadata.get('extraction_method', 'unknown')}")
        
        # Analyze questions with images
        questions_with_images = [q for q in questions if q.get('imageUrl')]
        if questions_with_images:
            log_print("📝 Sample questions with images:")
            for i, q in enumerate(questions_with_images[:3]):
                content = q.get('content', '')[:60]
                image_count = len(q.get('imageUrl', {})) if isinstance(q.get('imageUrl'), dict) else (1 if q.get('imageUrl') else 0)
                log_print(f"   {i+1}. {content}... ({image_count} images)")
        else:
            log_print("📝 No questions found with images")
            log_print("💡 This might indicate:")
            log_print("   - PDF is text-only")
            log_print("   - Images are not properly linked to questions")
            log_print("   - Images are embedded in a way that requires different extraction")
        
        return len(questions) > 0
        
    except Exception as e:
        log_print(f"❌ Image-enhanced extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependency_status():
    """Test the status of image processing dependencies"""
    log_print("🔍 Checking image processing dependencies...")
    
    dependencies = [
        ('PyMuPDF', 'fitz', 'Direct PDF image extraction'),
        ('Pillow', 'PIL', 'Image processing and enhancement'),
        ('OpenCV', 'cv2', 'Advanced image operations'),
        ('NumPy', 'numpy', 'Numerical operations')
    ]
    
    available_deps = []
    missing_deps = []
    
    for dep_name, import_name, description in dependencies:
        try:
            __import__(import_name)
            log_print(f"✅ {dep_name}: Available - {description}")
            available_deps.append(dep_name)
        except ImportError:
            log_print(f"❌ {dep_name}: Missing - {description}")
            missing_deps.append(dep_name)
    
    if missing_deps:
        log_print(f"\n⚠️ Missing dependencies: {', '.join(missing_deps)}")
        log_print("💡 Install with: python install_image_dependencies.py")
        return False
    else:
        log_print(f"\n✅ All dependencies available: {', '.join(available_deps)}")
        return True

def analyze_pdf_for_images(pdf_path):
    """Analyze PDF to understand its image content"""
    try:
        log_print(f"🔍 Analyzing PDF for image content: {pdf_path}")
        
        # Try to get basic PDF info
        file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # MB
        log_print(f"📊 File size: {file_size:.1f} MB")
        
        # Try PyMuPDF analysis if available
        try:
            import fitz
            doc = fitz.open(pdf_path)
            
            total_images = 0
            pages_with_images = 0
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()
                
                if image_list:
                    pages_with_images += 1
                    total_images += len(image_list)
            
            doc.close()
            
            log_print(f"📊 Total pages: {len(doc)}")
            log_print(f"📊 Pages with images: {pages_with_images}")
            log_print(f"📊 Total embedded images: {total_images}")
            
            if total_images == 0:
                log_print("💡 This PDF appears to be text-only")
                log_print("💡 Superior image extraction will focus on text extraction quality")
            else:
                log_print("💡 This PDF contains embedded images")
                log_print("💡 Superior image extraction should find and process these images")
            
            return total_images > 0
            
        except ImportError:
            log_print("⚠️ PyMuPDF not available for detailed PDF analysis")
            return None
            
    except Exception as e:
        log_print(f"❌ PDF analysis failed: {e}")
        return None

def main():
    """Main test function"""
    log_print("🖼️ Superior Image Extraction Capabilities Test")
    log_print("=" * 60)
    
    # Test 1: Dependencies
    log_print("\n📦 Test 1: Dependency Status")
    deps_ok = test_dependency_status()
    
    # Test 2: PDF Analysis
    log_print("\n🔍 Test 2: PDF Content Analysis")
    pdf_files = list(Path('.').glob('*.pdf'))
    if pdf_files:
        pdf_path = str(pdf_files[0])
        has_images = analyze_pdf_for_images(pdf_path)
    else:
        log_print("❌ No PDF files found for analysis")
        log_print("💡 Place a PDF file in the python directory to test")
        return False
    
    # Test 3: Direct Image Extraction
    log_print("\n🖼️ Test 3: Direct Image Extraction")
    if deps_ok:
        direct_success = test_image_extraction_direct()
    else:
        log_print("⚠️ Skipping due to missing dependencies")
        direct_success = False
    
    # Test 4: Image-Enhanced Question Extraction
    log_print("\n🚀 Test 4: Image-Enhanced Question Extraction")
    enhanced_success = test_image_enhanced_extraction()
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 Test Summary:")
    log_print(f"✅ Dependencies: {'OK' if deps_ok else 'Missing some'}")
    log_print(f"✅ PDF has images: {'Yes' if has_images else 'No' if has_images is False else 'Unknown'}")
    log_print(f"✅ Direct extraction: {'Success' if direct_success else 'Failed/No images'}")
    log_print(f"✅ Enhanced extraction: {'Success' if enhanced_success else 'Failed'}")
    
    if not deps_ok:
        log_print("\n🔧 Next Steps:")
        log_print("1. Install missing dependencies: python install_image_dependencies.py")
        log_print("2. Re-run this test")
    elif has_images is False:
        log_print("\n💡 PDF Analysis:")
        log_print("- Your test PDF appears to be text-only")
        log_print("- Superior image extraction is working but has no images to extract")
        log_print("- Try with a PDF that contains diagrams, figures, or images")
    elif direct_success and enhanced_success:
        log_print("\n🎉 Superior Image Extraction System is working correctly!")
        log_print("💡 Ready for production use with image-heavy PDFs")
    else:
        log_print("\n⚠️ Some issues detected - check logs above for details")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
