"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/admin/add-question-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/add-question-form.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_IMAGE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\nconst ACCEPTED_PDF_TYPES = [\n    \"application/pdf\"\n];\n// Manual form schema with custom validation for options\nconst manualFormSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\n// PDF upload form schema\nconst pdfUploadSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional()\n});\nfunction AddQuestionForm() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Manual form states\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [manualTopics, setManualTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // PDF upload states\n    const [pdfTopics, setPdfTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPdfFile, setSelectedPdfFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [useChemicalExtraction, setUseChemicalExtraction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"AddQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AddQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"AddQuestionForm.useEffect\"], []);\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize manual form\n    const manualForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(manualFormSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Initialize PDF upload form\n    const pdfForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(pdfUploadSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\"\n        }\n    });\n    // Handle subject change for manual form\n    const handleManualSubjectChange = (value)=>{\n        manualForm.setValue(\"subject\", value);\n        manualForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setManualTopics(selectedSubject.topics || []);\n        } else {\n            setManualTopics([]);\n        }\n    };\n    // Handle subject change for PDF form\n    const handlePdfSubjectChange = (value)=>{\n        pdfForm.setValue(\"subject\", value);\n        pdfForm.setValue(\"topic\", \"\");\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setPdfTopics(selectedSubject.topics || []);\n        } else {\n            setPdfTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            manualForm.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function for manual form\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle manual form submission\n    const onManualSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Manual form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Get user ID from localStorage if available\n            const userData = localStorage.getItem(\"userData\");\n            let userId;\n            try {\n                if (userData) {\n                    const parsed = JSON.parse(userData);\n                    userId = parsed._id || parsed.id;\n                }\n            } catch (e) {\n                console.error(\"Error parsing user data:\", e);\n            }\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add createdBy if we have a valid user ID\n            if (userId) {\n                baseQuestionData.createdBy = userId;\n            }\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            // If question has an image, embed it in the question text as base64\n            let finalQuestionData = {\n                ...questionData\n            };\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API\n            const response = await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.createQuestion)(finalQuestionData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_16__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                // Reset manual form\n                resetManualForm();\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error adding question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle PDF form submission\n    const onPdfSubmit = async (data)=>{\n        if (!selectedPdfFile) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: \"Please select a PDF file to upload.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"PDF form data:\", data, \"Chemical extraction:\", useChemicalExtraction);\n            // Submit to appropriate bulk upload API based on extraction type\n            const result = useChemicalExtraction ? await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadChemicalQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined) : await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.bulkUploadQuestionsPDF)(selectedPdfFile, data.subject, data.topic || undefined);\n            // Display success toast with extraction type info\n            const extractionType = useChemicalExtraction ? \"chemical questions with molecular structures\" : \"questions\";\n            const questionsCount = result.questionsAdded || result.questionsCreated || 'questions';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"PDF Upload Successful\",\n                description: \"Successfully uploaded \".concat(questionsCount, \" \").concat(extractionType, \" from PDF.\")\n            });\n            // Reset PDF form\n            resetPdfForm();\n        } catch (error) {\n            console.error(\"Error uploading PDF:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to upload PDF. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset manual form\n    const resetManualForm = ()=>{\n        manualForm.reset({\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        });\n        setQuestionImage(null);\n        setOptionImages({\n            A: null,\n            B: null,\n            C: null,\n            D: null\n        });\n        setManualTopics([]);\n        setOptionValidationErrors({\n            A: false,\n            B: false,\n            C: false,\n            D: false\n        });\n    };\n    // Reset PDF form\n    const resetPdfForm = ()=>{\n        pdfForm.reset({\n            subject: \"\",\n            topic: \"\"\n        });\n        setSelectedPdfFile(null);\n        setPdfTopics([]);\n    };\n    // Handle PDF file selection\n    const handlePdfFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (file.type !== \"application/pdf\") {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Invalid File Type\",\n                    description: \"Please select a PDF file.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (file.size > MAX_FILE_SIZE) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"File Too Large\",\n                    description: \"File size must be less than 50MB.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setSelectedPdfFile(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                    children: \"Add Questions\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsList, {\n                            className: \"grid w-full grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"manual\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manual Entry\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsTrigger, {\n                                    value: \"pdf\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upload PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"manual\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...manualForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: manualForm.handleSubmit(onManualSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handleManualSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: manualTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : manualTopics.length > 0 ? \"Select a topic\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: manualTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 548,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"questionText\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Question Text *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                        placeholder: \"Enter your question here...\",\n                                                                        className: \"min-h-[100px]\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Question Image (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Upload an image to accompany your question\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-9\",\n                                                                    onClick: ()=>{\n                                                                        var _questionImageRef_current;\n                                                                        return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Upload Image\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"file\",\n                                                                    ref: questionImageRef,\n                                                                    className: \"hidden\",\n                                                                    accept: \"image/*\",\n                                                                    onChange: (e)=>handleImageUpload(e, \"question\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            src: questionImage || \"/placeholder.svg\",\n                                                                            alt: \"Question image\",\n                                                                            width: 100,\n                                                                            height: 100,\n                                                                            className: \"object-cover rounded-md border h-[100px] w-[100px]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"destructive\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 absolute -top-2 -right-2 rounded-full\",\n                                                                            onClick: ()=>removeImage(\"question\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium\",\n                                                    children: \"Answer Options\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, this),\n                                                [\n                                                    \"A\",\n                                                    \"B\",\n                                                    \"C\",\n                                                    \"D\"\n                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                                control: manualForm.control,\n                                                                name: \"option\".concat(option),\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-green-600 ml-2\",\n                                                                                        children: \"(Image uploaded)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                    placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                                    ...field\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 665,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-red-600\",\n                                                                                children: [\n                                                                                    \"Option \",\n                                                                                    option,\n                                                                                    \" requires either text or an image\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 667,\n                                                                                columnNumber: 31\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 27\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 mt-8 md:mt-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-9 text-xs\",\n                                                                            onClick: ()=>{\n                                                                                var _optionImageRefs_option_current;\n                                                                                return (_optionImageRefs_option_current = optionImageRefs[option].current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 685,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Image\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            ref: optionImageRefs[option],\n                                                                            className: \"hidden\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>handleImageUpload(e, option)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                    src: optionImages[option] || \"/placeholder.svg\",\n                                                                                    alt: \"Option \".concat(option, \" image\"),\n                                                                                    width: 60,\n                                                                                    height: 60,\n                                                                                    className: \"object-cover rounded-md border h-[60px] w-[60px]\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 698,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: \"destructive\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-5 w-5 absolute -top-2 -right-2 rounded-full\",\n                                                                                    onClick: ()=>removeImage(option),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 712,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 705,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, option, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"correctAnswer\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Correct Answer *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"A\",\n                                                                            \"B\",\n                                                                            \"C\",\n                                                                            \"D\"\n                                                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: option,\n                                                                                            id: \"manual-option-\".concat(option)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 739,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-option-\".concat(option),\n                                                                                        children: option\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 741,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, option, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: manualForm.control,\n                                                    name: \"difficulty\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Difficulty Level *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                                        onValueChange: field.onChange,\n                                                                        value: field.value || \"\",\n                                                                        className: \"flex space-x-4\",\n                                                                        children: [\n                                                                            \"Easy\",\n                                                                            \"Medium\",\n                                                                            \"Hard\"\n                                                                        ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                                            value: level,\n                                                                                            id: \"manual-level-\".concat(level)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                            lineNumber: 768,\n                                                                                            columnNumber: 35\n                                                                                        }, void 0)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 767,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                        className: \"font-normal\",\n                                                                                        htmlFor: \"manual-level-\".concat(level),\n                                                                                        children: level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                        lineNumber: 770,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                ]\n                                                                            }, level, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 31\n                                                                            }, void 0))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                            control: manualForm.control,\n                                            name: \"explanation\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Explanation (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 794,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                                placeholder: \"Explain why the correct answer is right...\",\n                                                                className: \"min-h-[80px]\",\n                                                                ...field\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormDescription, {\n                                                            children: \"This will be shown to students after they answer the question.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding Question...\"\n                                                        ]\n                                                    }, void 0, true) : \"Add Question\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetManualForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_13__.TabsContent, {\n                            value: \"pdf\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                                ...pdfForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: pdfForm.handleSubmit(onPdfSubmit),\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"subject\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Subject *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: handlePdfSubjectChange,\n                                                                    value: field.value,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 856,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 855,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                value: \"loading\",\n                                                                                disabled: true,\n                                                                                children: \"Loading subjects...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: subject._id,\n                                                                                    children: subject.name\n                                                                                }, subject._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 33\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                    control: pdfForm.control,\n                                                    name: \"topic\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                    children: \"Topic (Optional)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 883,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                                    onValueChange: field.onChange,\n                                                                    value: field.value,\n                                                                    disabled: pdfTopics.length === 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                                    placeholder: loading ? \"Loading topics...\" : pdfTopics.length > 0 ? \"Select a topic (optional)\" : \"Select a subject first\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 887,\n                                                                                    columnNumber: 31\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 885,\n                                                                            columnNumber: 27\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                                            children: pdfTopics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                                    value: topic._id,\n                                                                                    children: topic.name\n                                                                                }, topic._id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                    lineNumber: 900,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 27\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                            children: \"PDF File *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 918,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Upload a PDF file containing questions to be extracted and added to the question bank\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"pdf-upload\",\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-2 block text-sm font-medium text-gray-900\",\n                                                                                children: selectedPdfFile ? selectedPdfFile.name : \"Choose PDF file or drag and drop\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 933,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-1 block text-xs text-gray-500\",\n                                                                                children: \"PDF up to 50MB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 936,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 932,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"pdf-upload\",\n                                                                        type: \"file\",\n                                                                        className: \"sr-only\",\n                                                                        accept: \".pdf\",\n                                                                        onChange: handlePdfFileChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById('pdf-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: \"Select PDF File\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 928,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedPdfFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: selectedPdfFile.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        (selectedPdfFile.size / 1024 / 1024).toFixed(2),\n                                                                        \" MB)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setSelectedPdfFile(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"chemical-extraction\",\n                                                            checked: useChemicalExtraction,\n                                                            onChange: (e)=>setUseChemicalExtraction(e.target.checked),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"chemical-extraction\",\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Use Chemical Extraction (for Chemistry PDFs with molecular structures)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 19\n                                                }, this),\n                                                useChemicalExtraction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 border border-blue-200 rounded-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium mb-1\",\n                                                                        children: \"Chemical Extraction Mode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"This mode is optimized for chemistry PDFs containing:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 1002,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside mt-1 space-y-0.5\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Molecular structures and chemical diagrams\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 1004,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Chemical equations and formulas\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 1005,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Reaction mechanisms\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Complex chemical images\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                                lineNumber: 1007,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-2 text-blue-700\",\n                                                                        children: \"Processing may take longer but provides better accuracy for chemical content.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 1009,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 1000,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"submit\",\n                                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                                    disabled: isSubmitting || !selectedPdfFile,\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 1025,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Uploading PDF...\"\n                                                        ]\n                                                    }, void 0, true) : \"Upload PDF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 1018,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    className: \"w-full sm:w-auto\",\n                                                    onClick: resetPdfForm,\n                                                    disabled: isSubmitting,\n                                                    children: \"Reset Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_s(AddQuestionForm, \"KWBNHzcTQIFF+oVUttgNWRp9/No=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AddQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"AddQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/add-question-form.tsx\n"));

/***/ })

});