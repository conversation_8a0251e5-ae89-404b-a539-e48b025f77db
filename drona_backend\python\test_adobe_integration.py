#!/usr/bin/env python3
"""
Test script for Adobe PDF Extract API integration
Tests the enhanced question extractor with Adobe PDF Services
"""

import os
import sys
import json
import time
from pathlib import Path

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_adobe_credentials():
    """Test if Adobe credentials are properly configured"""
    log_print("🔍 [TEST] Checking Adobe PDF Services credentials...")
    
    credentials_file = 'pdfservices-api-credentials.json'
    private_key_file = 'private.key'
    
    if not os.path.exists(credentials_file):
        log_print(f"❌ [TEST] Adobe credentials file not found: {credentials_file}")
        return False
    
    if not os.path.exists(private_key_file):
        log_print(f"❌ [TEST] Adobe private key file not found: {private_key_file}")
        return False
    
    try:
        with open(credentials_file, 'r') as f:
            credentials = json.load(f)
        
        required_fields = ['client_credentials', 'service_account_credentials']
        for field in required_fields:
            if field not in credentials:
                log_print(f"❌ [TEST] Missing field in credentials: {field}")
                return False
        
        log_print("✅ [TEST] Adobe credentials file is valid")
        return True
        
    except Exception as e:
        log_print(f"❌ [TEST] Error reading Adobe credentials: {e}")
        return False

def test_requests_library():
    """Test if requests library is properly installed for REST API"""
    log_print("🔍 [TEST] Checking requests library installation...")

    try:
        import requests
        log_print("✅ [TEST] Requests library is properly installed")
        return True

    except ImportError as e:
        log_print(f"❌ [TEST] Requests library not installed: {e}")
        log_print("💡 [TEST] Install with: pip install requests")
        return False

def test_adobe_token_generation():
    """Test Adobe access token generation"""
    log_print("🔍 [TEST] Testing Adobe access token generation...")

    try:
        from adobe_pdf_extractor import AdobePDFExtractor

        # Create extractor without providing access token (will generate one)
        extractor = AdobePDFExtractor()

        if extractor.access_token:
            log_print(f"✅ [TEST] Access token generated successfully: {extractor.access_token[:20]}...")
            return True
        else:
            log_print("❌ [TEST] No access token generated")
            return False

    except Exception as e:
        log_print(f"❌ [TEST] Access token generation failed: {e}")
        return False

def test_adobe_extractor():
    """Test Adobe PDF Extractor initialization"""
    log_print("🔍 [TEST] Testing Adobe PDF Extractor initialization...")
    
    try:
        from adobe_pdf_extractor import AdobePDFExtractor
        
        extractor = AdobePDFExtractor()
        log_print("✅ [TEST] Adobe PDF Extractor initialized successfully")
        return True, extractor
        
    except Exception as e:
        log_print(f"❌ [TEST] Adobe PDF Extractor initialization failed: {e}")
        return False, None

def test_enhanced_extractor():
    """Test Enhanced Question Extractor initialization"""
    log_print("🔍 [TEST] Testing Enhanced Question Extractor initialization...")
    
    try:
        from enhanced_question_extractor import EnhancedQuestionExtractor
        
        extractor = EnhancedQuestionExtractor(ai_provider='gemini')
        capabilities = extractor.get_extraction_capabilities()
        
        log_print("✅ [TEST] Enhanced Question Extractor initialized successfully")
        log_print(f"📊 [TEST] Extraction capabilities: {capabilities}")
        
        return True, extractor
        
    except Exception as e:
        log_print(f"❌ [TEST] Enhanced Question Extractor initialization failed: {e}")
        return False, None

def test_pdf_extraction(pdf_path=None):
    """Test PDF extraction with a sample file"""
    if not pdf_path:
        # Look for any PDF file in the current directory
        pdf_files = list(Path('.').glob('*.pdf'))
        if not pdf_files:
            log_print("⚠️ [TEST] No PDF files found for testing")
            log_print("💡 [TEST] Place a test PDF file in the current directory to test extraction")
            return True  # Not a failure, just no test file
        
        pdf_path = str(pdf_files[0])
        log_print(f"📄 [TEST] Using test PDF: {pdf_path}")
    
    log_print("🔍 [TEST] Testing PDF extraction with enhanced extractor...")
    
    try:
        from enhanced_question_extractor import EnhancedQuestionExtractor
        
        extractor = EnhancedQuestionExtractor(ai_provider='gemini')
        
        log_print("🚀 [TEST] Starting enhanced extraction test...")
        start_time = time.time()
        
        result = extractor.extract_comprehensive_data_enhanced(pdf_path)
        
        duration = time.time() - start_time
        log_print(f"✅ [TEST] Enhanced extraction completed in {duration:.2f}s")
        
        # Analyze results
        metadata = result.get('extraction_metadata', {})
        log_print(f"📊 [TEST] Adobe used: {metadata.get('adobe_used', False)}")
        log_print(f"📊 [TEST] Mistral used: {metadata.get('mistral_used', False)}")
        log_print(f"📊 [TEST] Extraction method: {metadata.get('extraction_method', 'unknown')}")
        
        # Try to parse questions
        questions_json = result.get('questions', '[]')
        try:
            questions = json.loads(questions_json)
            log_print(f"📋 [TEST] Extracted {len(questions)} questions")
            
            if len(questions) > 0:
                first_question = questions[0]
                log_print(f"📝 [TEST] Sample question keys: {list(first_question.keys())}")
        except json.JSONDecodeError:
            log_print("⚠️ [TEST] Could not parse questions JSON")
        
        return True
        
    except Exception as e:
        log_print(f"❌ [TEST] PDF extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests(pdf_path=None):
    """Run all integration tests"""
    log_print("🚀 [TEST] Starting Adobe PDF Extract API integration tests...")
    log_print("=" * 60)
    
    tests = [
        ("Adobe Credentials", test_adobe_credentials),
        ("Requests Library", test_requests_library),
        ("Adobe Token Generation", test_adobe_token_generation),
        ("Adobe Extractor", lambda: test_adobe_extractor()[0]),
        ("Enhanced Extractor", lambda: test_enhanced_extractor()[0]),
    ]
    
    # Add PDF extraction test if PDF file is available
    if pdf_path or list(Path('.').glob('*.pdf')):
        tests.append(("PDF Extraction", lambda: test_pdf_extraction(pdf_path)))
    
    results = {}
    
    for test_name, test_func in tests:
        log_print(f"\n🧪 [TEST] Running {test_name} test...")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                log_print(f"✅ [TEST] {test_name} test PASSED")
            else:
                log_print(f"❌ [TEST] {test_name} test FAILED")
        except Exception as e:
            log_print(f"❌ [TEST] {test_name} test ERROR: {e}")
            results[test_name] = False
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 [TEST] Test Results Summary:")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        log_print(f"  {test_name}: {status}")
    
    log_print(f"\n🎯 [TEST] Overall: {passed}/{total} tests passed")
    
    if passed == total:
        log_print("🎉 [TEST] All tests passed! Adobe PDF Extract API integration is ready.")
        log_print("\n💡 [NEXT] You can now:")
        log_print("  1. Start the API server: python api_server.py")
        log_print("  2. Test with your frontend")
        log_print("  3. Use enhanced extraction in production")
    else:
        log_print("⚠️ [TEST] Some tests failed. Please check the setup:")
        log_print("  1. Ensure Adobe credentials are in place")
        log_print("  2. Install required dependencies: pip install -r requirements.txt")
        log_print("  3. Check the ADOBE_PDF_SETUP.md guide")
    
    return passed == total

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Adobe PDF Extract API integration")
    parser.add_argument("--pdf", help="Path to test PDF file", default=None)
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    success = run_all_tests(args.pdf)
    sys.exit(0 if success else 1)
