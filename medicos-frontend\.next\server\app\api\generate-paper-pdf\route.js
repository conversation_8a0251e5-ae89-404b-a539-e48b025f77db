/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle embedded base64 images before processing tables\n    processedText = processedText// Handle newline-separated base64 images (common in extracted content)\n    .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+)/g, '<br><img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle inline base64 images\n    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+)/g, '<img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />');\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: avoid;\n      margin-bottom: 20px;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n      page-break-after: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(q.question);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n                    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                        // Try to find matching image in question's imageData\n                        const imageData = q.imageData || q.chemicalImages;\n                        if (imageData && typeof imageData === 'object') {\n                            // Debug: Looking for image match\n                            // Try multiple matching strategies\n                            let imageKey = null;\n                            // Strategy 1: Exact match\n                            if (imageData[src]) {\n                                imageKey = src;\n                            } else if (imageData[src]) {\n                                imageKey = src;\n                            } else {\n                                const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                            }\n                            // Strategy 4: Try partial matches\n                            if (!imageKey) {\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                            }\n                            // Strategy 5: Extract numbers and match\n                            if (!imageKey) {\n                                const srcNumbers = src.match(/\\d+/g);\n                                if (srcNumbers) {\n                                    imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                }\n                            }\n                            if (imageKey && imageData[imageKey]) {\n                                return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            } else {\n                            // No matching image found\n                            }\n                        }\n                        return `[Missing Image: ${src}]`;\n                    })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                            // Try to find matching image in question's imageData\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object') {\n                                // Try multiple matching strategies\n                                let imageKey = null;\n                                // Strategy 1: Exact match\n                                if (imageData[src]) {\n                                    imageKey = src;\n                                } else {\n                                    const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                                }\n                                // Strategy 3: Try partial matches\n                                if (!imageKey) {\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                                }\n                                // Strategy 4: Extract numbers and match\n                                if (!imageKey) {\n                                    const srcNumbers = src.match(/\\d+/g);\n                                    if (srcNumbers) {\n                                        imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                    }\n                                }\n                                if (imageKey && imageData[imageKey]) {\n                                    return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                                }\n                            }\n                            return `[Missing Image: ${src}]`;\n                        })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();