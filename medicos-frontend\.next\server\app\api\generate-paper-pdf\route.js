/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Fix LaTeX formatting issues more comprehensively\n                    // Simple LaTeX fixes - keep it minimal and working\n                    const fixedQuestion = q.question// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle raw data URLs that might not be in markdown format\n                    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const fixedOptions = q.options.map((opt)=>// Simple LaTeX fixes for options - same as questions\n                        opt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64 (same as questions)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle raw data URLs that might not be in markdown format\n                        .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Option Image\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, ''));\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${fixedQuestion}</p>\n                    <div class=\"options\">\n                      ${fixedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1wYXBlci1wZGYvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdEO0FBQ3RCO0FBbUIzQixNQUFNRSxPQUFPLE9BQU9DO0lBQ3pCLElBQUk7UUFDRixNQUFNQyxVQUFXLE1BQU1ELElBQUlFLElBQUk7UUFFL0IsTUFBTSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFDWEMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFNBQVMsRUFDVEMsY0FBYyxFQUNkQyxXQUFXLG9CQUFvQixFQUMvQkMsY0FBYyxFQUFFLEVBQ2hCQyxpQkFBaUIsRUFBRSxFQUNwQixHQUFHVjtRQUVKLE1BQU1XLE9BQU8sQ0FBQzs7OztTQUlULEVBQUVULE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O01Bd0dYLEVBQUVRLGlCQUFpQixDQUFDLFVBQVUsRUFBRUEsZUFBZSxlQUFlLENBQUMsR0FBRyxHQUFHO1lBQy9ELEVBQUVELFlBQVk7O3VCQUVILEVBQUVQLE1BQU07O3dCQUVQLEVBQUVHLFdBQVc7cUJBQ2hCLEVBQUVELFNBQVM7Ozs7S0FJM0IsRUFBRUQsWUFBWTs7SUFFZixFQUFFLENBQUM7WUFDRCw2QkFBNkI7WUFDN0IsTUFBTVMsbUJBQW1CTixVQUFVTyxNQUFNLENBQUMsQ0FBQ0MsUUFBUUM7Z0JBQ2pELE1BQU1DLFVBQVVELFNBQVNDLE9BQU8sSUFBSTtnQkFDcEMsSUFBSSxDQUFDRixNQUFNLENBQUNFLFFBQVEsRUFBRTtvQkFDcEJGLE1BQU0sQ0FBQ0UsUUFBUSxHQUFHLEVBQUU7Z0JBQ3RCO2dCQUNBRixNQUFNLENBQUNFLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDRjtnQkFDckIsT0FBT0Q7WUFDVCxHQUFHLENBQUM7WUFFSix1Q0FBdUM7WUFDdkMsT0FBT0ksT0FBT0MsT0FBTyxDQUFDUCxrQkFBa0JRLEdBQUcsQ0FBQyxDQUFDLENBQUNKLFNBQVNLLGlCQUFpQjtnQkFDdEUsTUFBTUMsY0FBYyxDQUFDOztrREFFcUIsRUFBRU4sUUFBUTs7O2NBRzlDLEVBQUVLLGlCQUFpQkQsR0FBRyxDQUFDLENBQUNHLEdBQUdDO29CQUN6QixtREFBbUQ7b0JBQ25ELG1EQUFtRDtvQkFDbkQsTUFBTUMsZ0JBQWdCRixFQUFFUixRQUFRLGdFQUNrQztxQkFDL0RXLE9BQU8sQ0FBQyxlQUFlLGlCQUN2QkEsT0FBTyxDQUFDLGdCQUFnQixrQkFDeEJBLE9BQU8sQ0FBQyx1QkFBdUIsNEJBQy9CQSxPQUFPLENBQUMsZUFBZSxpQkFDdkJBLE9BQU8sQ0FBQyxtQkFBbUIscUJBQzNCQSxPQUFPLENBQUMsbUJBQW1CLHFCQUMzQkEsT0FBTyxDQUFDLG9CQUFvQixzQkFDNUJBLE9BQU8sQ0FBQyxlQUFlLGdCQUV4Qiw0QkFBNEI7cUJBQzNCQSxPQUFPLENBQUMsY0FBYyxXQUN0QkEsT0FBTyxDQUFDLDhCQUE4QixrQkFDdENBLE9BQU8sQ0FBQyxtQ0FBbUMsb0JBQzNDQSxPQUFPLENBQUMsNEJBQTRCLGlCQUVyQyx1REFBdUQ7cUJBQ3REQSxPQUFPLENBQUMsd0RBQ1AsdUpBQ0YsNERBQTREO3FCQUMzREEsT0FBTyxDQUFDLGdEQUNQLDhJQUNGLGdFQUFnRTtxQkFDL0RBLE9BQU8sQ0FBQyxrREFBa0QsR0FDM0QsNEVBQTRFO3FCQUMzRUEsT0FBTyxDQUFDLDhCQUE4QjtvQkFFekMsTUFBTUMsZUFBZUosRUFBRUssT0FBTyxDQUFDUixHQUFHLENBQUNTLENBQUFBLE1BQ2pDLHFEQUFxRDt3QkFDckRBLEdBQ0UsNkNBQTZDO3lCQUM1Q0gsT0FBTyxDQUFDLGVBQWUsaUJBQ3ZCQSxPQUFPLENBQUMsZ0JBQWdCLGtCQUN4QkEsT0FBTyxDQUFDLHVCQUF1Qiw0QkFDL0JBLE9BQU8sQ0FBQyxlQUFlLGlCQUN2QkEsT0FBTyxDQUFDLG1CQUFtQixxQkFDM0JBLE9BQU8sQ0FBQyxtQkFBbUIscUJBQzNCQSxPQUFPLENBQUMsb0JBQW9CLHNCQUM1QkEsT0FBTyxDQUFDLGVBQWUsZ0JBRXhCLDRCQUE0Qjt5QkFDM0JBLE9BQU8sQ0FBQyxjQUFjLFdBQ3RCQSxPQUFPLENBQUMsOEJBQThCLGtCQUN0Q0EsT0FBTyxDQUFDLG1DQUFtQyxvQkFDM0NBLE9BQU8sQ0FBQyw0QkFBNEIsaUJBRXJDLDJFQUEyRTt5QkFDMUVBLE9BQU8sQ0FBQyx3REFDUCx3SkFDRiw0REFBNEQ7eUJBQzNEQSxPQUFPLENBQUMsZ0RBQ1AsNklBQ0YsZ0VBQWdFO3lCQUMvREEsT0FBTyxDQUFDLGtEQUFrRCxHQUMzRCw0RUFBNEU7eUJBQzNFQSxPQUFPLENBQUMsOEJBQThCO29CQUczQyxPQUFPLENBQUM7OytCQUVPLEVBQUVGLGdCQUFnQixFQUFFLFdBQVcsRUFBRUMsY0FBYzs7c0JBRXhELEVBQUVFLGFBQWFQLEdBQUcsQ0FBQyxDQUFDUyxLQUFLQyxJQUFNLENBQUMsR0FBRyxFQUFFQyxPQUFPQyxZQUFZLENBQUMsS0FBS0YsR0FBRyxFQUFFLEVBQUVELElBQUksSUFBSSxDQUFDLEVBQUVJLElBQUksQ0FBQyxJQUFJO3NCQUN6RixFQUFFMUIsaUJBQWlCLENBQUMsb0JBQW9CLEVBQUVnQixFQUFFVyxNQUFNLENBQUMsSUFBSSxDQUFDLEdBQUcsR0FBRzs7d0JBRTVELENBQUM7Z0JBQ1gsR0FBR0QsSUFBSSxDQUFDLElBQUk7O2dCQUVWLENBQUM7Z0JBQ1QsT0FBT1g7WUFDVCxHQUFHVyxJQUFJLENBQUM7UUFDVixLQUFLOztvQkFFVyxFQUFFLElBQUlFLE9BQU9DLGtCQUFrQixHQUFHOztPQUUvQyxDQUFDO1FBRUosTUFBTUMsVUFBVSxNQUFNeEMsd0RBQWdCLENBQUM7WUFDckMwQyxNQUFNO2dCQUFDO2dCQUFnQjthQUEyQjtRQUNwRDtRQUNBLE1BQU1DLE9BQU8sTUFBTUgsUUFBUUksT0FBTztRQUVsQyxNQUFNRCxLQUFLRSxVQUFVLENBQUMvQixNQUFNO1lBQUVnQyxXQUFXO1FBQWU7UUFFeEQseUNBQXlDO1FBQ3pDLE1BQU1ILEtBQUtJLGVBQWUsQ0FBQztZQUN6QixPQUFPLE9BQWdCRSxtQkFBbUIsS0FBS0M7UUFDakQsR0FBRztZQUFFQyxTQUFTO1FBQUssR0FBR0MsS0FBSyxDQUFDLEtBQU87UUFFbkMsNENBQTRDO1FBQzVDLE1BQU1ULEtBQUtVLFFBQVEsQ0FBQztZQUNsQixJQUFJLE9BQWdCSixtQkFBbUIsRUFBRTtnQkFDdENELE9BQWVDLG1CQUFtQixDQUFDSyxTQUFTQyxJQUFJLEVBQUU7b0JBQ2pEQyxZQUFZO3dCQUNWOzRCQUFDQyxNQUFNOzRCQUFNQyxPQUFPOzRCQUFNQyxTQUFTO3dCQUFJO3dCQUN2Qzs0QkFBQ0YsTUFBTTs0QkFBS0MsT0FBTzs0QkFBS0MsU0FBUzt3QkFBSzt3QkFDdEM7NEJBQUNGLE1BQU07NEJBQU9DLE9BQU87NEJBQU9DLFNBQVM7d0JBQUs7d0JBQzFDOzRCQUFDRixNQUFNOzRCQUFPQyxPQUFPOzRCQUFPQyxTQUFTO3dCQUFJO3FCQUMxQztvQkFDREMsY0FBYztvQkFDZEMsWUFBWTtvQkFDWkMsUUFBUTtnQkFDVjtZQUNGO1FBQ0Y7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTW5CLEtBQUtJLGVBQWUsQ0FBQztZQUN6QixNQUFNZ0IsZUFBZVQsU0FBU1UsZ0JBQWdCLENBQUM7WUFDL0MsTUFBTUMsZ0JBQWdCWCxTQUFTVSxnQkFBZ0IsQ0FBQztZQUNoRCxPQUFPRCxhQUFhRyxNQUFNLEtBQUssS0FBS0QsY0FBY0MsTUFBTSxHQUFHO1FBQzdELEdBQUc7WUFBRWYsU0FBUztRQUFLLEdBQUdDLEtBQUssQ0FBQyxLQUFPO1FBRW5DLHVDQUF1QztRQUN2QyxNQUFNLElBQUllLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7UUFFakQsTUFBTUUsWUFBWSxNQUFNM0IsS0FBSzRCLEdBQUcsQ0FBQztZQUMvQkMsUUFBUTtZQUNSQyxpQkFBaUI7WUFDakJDLFFBQVE7Z0JBQUVDLEtBQUs7Z0JBQVFqQixPQUFPO2dCQUFRa0IsUUFBUTtnQkFBUW5CLE1BQU07WUFBTztRQUNyRTtRQUVBLE1BQU1qQixRQUFRcUMsS0FBSztRQUVuQixPQUFPLElBQUk5RSxxREFBWUEsQ0FBQ3VFLFdBQVc7WUFDakNRLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7Z0JBQ2hCLHVCQUF1QixDQUFDLHNCQUFzQixFQUFFcEUsU0FBUyxDQUFDLENBQUM7WUFDN0Q7UUFDRjtJQUNGLEVBQUUsT0FBT3FFLE9BQVk7UUFDbkJDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU8sSUFBSWpGLHFEQUFZQSxDQUFDbUYsS0FBS0MsU0FBUyxDQUFDO1lBQUVILE9BQU87UUFBd0IsSUFBSTtZQUFFRixRQUFRO1FBQUk7SUFDNUY7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcbWVkaWNvc1xcbWVkaWNvcy1mcm9udGVuZFxcc3JjXFxhcHBcXGFwaVxcZ2VuZXJhdGUtcGFwZXItcGRmXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xyXG5pbXBvcnQgcHVwcGV0ZWVyIGZyb20gJ3B1cHBldGVlcic7XHJcblxyXG5pbnRlcmZhY2UgUGRmR2VuZXJhdG9yUGF5bG9hZCB7XHJcbiAgdGl0bGU6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGR1cmF0aW9uOiBudW1iZXI7XHJcbiAgdG90YWxNYXJrczogbnVtYmVyO1xyXG4gIHF1ZXN0aW9uczogQXJyYXk8e1xyXG4gICAgcXVlc3Rpb246IHN0cmluZztcclxuICAgIG9wdGlvbnM6IHN0cmluZ1tdO1xyXG4gICAgYW5zd2VyOiBzdHJpbmc7XHJcbiAgICBzdWJqZWN0Pzogc3RyaW5nO1xyXG4gIH0+O1xyXG4gIGluY2x1ZGVBbnN3ZXJzOiBib29sZWFuO1xyXG4gIGZpbGVuYW1lPzogc3RyaW5nO1xyXG4gIGNvbGxlZ2VOYW1lPzogc3RyaW5nO1xyXG4gIGNvbGxlZ2VMb2dvVXJsPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgUE9TVCA9IGFzeW5jIChyZXE6IE5leHRSZXF1ZXN0KSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHBheWxvYWQgPSAoYXdhaXQgcmVxLmpzb24oKSkgYXMgUGRmR2VuZXJhdG9yUGF5bG9hZDtcclxuXHJcbiAgICBjb25zdCB7XHJcbiAgICAgIHRpdGxlLFxyXG4gICAgICBkZXNjcmlwdGlvbixcclxuICAgICAgZHVyYXRpb24sXHJcbiAgICAgIHRvdGFsTWFya3MsXHJcbiAgICAgIHF1ZXN0aW9ucyxcclxuICAgICAgaW5jbHVkZUFuc3dlcnMsXHJcbiAgICAgIGZpbGVuYW1lID0gJ3F1ZXN0aW9uLXBhcGVyLnBkZicsXHJcbiAgICAgIGNvbGxlZ2VOYW1lID0gJycsXHJcbiAgICAgIGNvbGxlZ2VMb2dvVXJsID0gJycsXHJcbiAgICB9ID0gcGF5bG9hZDtcclxuXHJcbiAgICBjb25zdCBodG1sID0gYDwhZG9jdHlwZSBodG1sPlxyXG48aHRtbD5cclxuPGhlYWQ+XHJcbiAgPG1ldGEgY2hhcnNldD1cInV0Zi04XCIgLz5cclxuICA8dGl0bGU+JHt0aXRsZX08L3RpdGxlPlxyXG4gIDxsaW5rIGhyZWY9XCJodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL2thdGV4QDAuMTYuMTAvZGlzdC9rYXRleC5taW4uY3NzXCIgcmVsPVwic3R5bGVzaGVldFwiIC8+XHJcbiAgPHNjcmlwdCBzcmM9XCJodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL2thdGV4QDAuMTYuMTAvZGlzdC9rYXRleC5taW4uanNcIj48L3NjcmlwdD5cclxuICA8c2NyaXB0IHNyYz1cImh0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0va2F0ZXhAMC4xNi4xMC9kaXN0L2NvbnRyaWIvYXV0by1yZW5kZXIubWluLmpzXCI+PC9zY3JpcHQ+XHJcbiAgPHNjcmlwdD5cclxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJET01Db250ZW50TG9hZGVkXCIsIGZ1bmN0aW9uKCkge1xyXG4gICAgICBpZiAod2luZG93LnJlbmRlck1hdGhJbkVsZW1lbnQpIHtcclxuICAgICAgICB3aW5kb3cucmVuZGVyTWF0aEluRWxlbWVudChkb2N1bWVudC5ib2R5LCB7XHJcbiAgICAgICAgICBkZWxpbWl0ZXJzOiBbXHJcbiAgICAgICAgICAgIHtsZWZ0OiAnJCQnLCByaWdodDogJyQkJywgZGlzcGxheTogdHJ1ZX0sXHJcbiAgICAgICAgICAgIHtsZWZ0OiAnJCcsIHJpZ2h0OiAnJCcsIGRpc3BsYXk6IGZhbHNlfSxcclxuICAgICAgICAgICAge2xlZnQ6ICdcXFxcKCcsIHJpZ2h0OiAnXFxcXCknLCBkaXNwbGF5OiBmYWxzZX0sXHJcbiAgICAgICAgICAgIHtsZWZ0OiAnXFxcXFsnLCByaWdodDogJ1xcXFxdJywgZGlzcGxheTogdHJ1ZX1cclxuICAgICAgICAgIF0sXHJcbiAgICAgICAgICB0aHJvd09uRXJyb3I6IGZhbHNlLFxyXG4gICAgICAgICAgZXJyb3JDb2xvcjogJyNjYzAwMDAnLFxyXG4gICAgICAgICAgc3RyaWN0OiBmYWxzZVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuICA8L3NjcmlwdD5cclxuICA8c3R5bGU+XHJcbiAgICBAcGFnZSB7XHJcbiAgICAgIHNpemU6IEE0O1xyXG4gICAgICBtYXJnaW46IDI1bW0gMTVtbSAyMG1tIDE1bW07XHJcbiAgICB9XHJcbiAgICBib2R5IHsgZm9udC1mYW1pbHk6ICdUaW1lcyBOZXcgUm9tYW4nLCBzZXJpZjsgZm9udC1zaXplOiAxMHB0OyBsaW5lLWhlaWdodDogMS4yOyBwb3NpdGlvbjogcmVsYXRpdmU7IH1cclxuICAgIGgxLGgyLGgzIHsgbWFyZ2luOiAwOyBwYWRkaW5nOiAwOyB9XHJcbiAgICBociB7IG1hcmdpbjogOHB4IDA7IGJvcmRlcjogbm9uZTsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMwMDA7IH1cclxuICAgIC8qIFdhdGVybWFyayAqL1xyXG4gICAgYm9keTo6YmVmb3JlIHtcclxuICAgICAgY29udGVudDogJ01FRElDT1MnO1xyXG4gICAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICAgIHRvcDogNTAlO1xyXG4gICAgICBsZWZ0OiA1MCU7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpIHJvdGF0ZSgtMzBkZWcpO1xyXG4gICAgICBmb250LXNpemU6IDk2cHQ7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICBjb2xvcjogcmdiYSgwLDEyOCwwLDAuMDgpOyAvKiBncmVlbmlzaCAqL1xyXG4gICAgICB6LWluZGV4OiAwO1xyXG4gICAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICAgIH1cclxuICAgIC8qIEhlYWRlciAvIEZvb3RlciAqL1xyXG4gICAgaGVhZGVyIHsgZGlzcGxheTogZmxleDsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyBhbGlnbi1pdGVtczogY2VudGVyOyBtYXJnaW4tYm90dG9tOiA4cHg7IH1cclxuICAgIC5jb2xsZWdlIHsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiA2cHg7IH1cclxuICAgIC5jb2xsZWdlIGltZyB7IGhlaWdodDogMzJweDsgd2lkdGg6IGF1dG87IH1cclxuICAgIC50aXRsZSB7IHRleHQtYWxpZ246IGNlbnRlcjsgZmxleDogMTsgZm9udC1zaXplOiAxNHB0OyBmb250LXdlaWdodDogYm9sZDsgfVxyXG4gICAgLm1ldGEgeyB0ZXh0LWFsaWduOiByaWdodDsgZm9udC1zaXplOiAxMHB0OyB9XHJcbiAgICAubWV0YSBkaXYgeyBtYXJnaW46IDA7IH1cclxuXHJcbiAgICAucXVlc3Rpb25zIHsgcG9zaXRpb246IHJlbGF0aXZlOyB6LWluZGV4OiAxOyBwYWRkaW5nLWJvdHRvbTogMjVtbTsgfVxyXG4gICAgLnN1YmplY3Qtc2VjdGlvbiB7IHBhZ2UtYnJlYWstYmVmb3JlOiBhbHdheXM7IH1cclxuICAgIC5zdWJqZWN0LXNlY3Rpb246Zmlyc3QtY2hpbGQgeyBwYWdlLWJyZWFrLWJlZm9yZTogYXZvaWQ7IH1cclxuICAgIC5zdWJqZWN0LWNvbnRlbnQge1xyXG4gICAgICBjb2x1bW4tY291bnQ6IDI7XHJcbiAgICAgIGNvbHVtbi1nYXA6IDEwbW07XHJcbiAgICAgIGNvbHVtbi1ydWxlOiAxcHggc29saWQgI2NjYzsgLyogQWRkIG1pZGRsZSBsaW5lIHNlcGFyYXRvciAqL1xyXG4gICAgICBjb2x1bW4tcnVsZS1zdHlsZTogc29saWQ7XHJcbiAgICB9XHJcbiAgICAucXVlc3Rpb24geyBicmVhay1pbnNpZGU6IGF2b2lkOyBtYXJnaW4tYm90dG9tOiAxMnB4OyB9XHJcbiAgICAub3B0aW9ucyB7IG1hcmdpbi1sZWZ0OiAxNnB4OyB9XHJcbiAgICAub3B0aW9ucyBwIHsgbWFyZ2luOiAycHggMDsgfVxyXG4gICAgZm9vdGVyIHsgcG9zaXRpb246IGZpeGVkOyBib3R0b206IDNtbTsgbGVmdDogMDsgcmlnaHQ6IDA7IHRleHQtYWxpZ246IGNlbnRlcjsgZm9udC1zaXplOiA5cHQ7IGNvbG9yOiAjNjY2OyBiYWNrZ3JvdW5kOiAjZmZmOyB6LWluZGV4OiAyOyB9XHJcbiAgICAuc3ViamVjdC1oZWFkaW5nIHtcclxuICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTJwdDtcclxuICAgICAgbWFyZ2luOiAxNnB4IDAgMTJweDtcclxuICAgICAgdGV4dC1hbGlnbjogbGVmdDtcclxuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICMzMzM7XHJcbiAgICAgIHBhZGRpbmctYm90dG9tOiA0cHg7XHJcbiAgICB9XHJcbiAgICAvKiBUYWJsZSBzdHlsaW5nIGZvciBwcm9wZXIgcmVuZGVyaW5nICovXHJcbiAgICB0YWJsZSB7XHJcbiAgICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBtYXJnaW46IDhweCAwO1xyXG4gICAgICBmb250LXNpemU6IDlwdDtcclxuICAgICAgYnJlYWstaW5zaWRlOiBhdm9pZDtcclxuICAgIH1cclxuICAgIHRoLCB0ZCB7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMzMzM7XHJcbiAgICAgIHBhZGRpbmc6IDRweCA2cHg7XHJcbiAgICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgICAgIHZlcnRpY2FsLWFsaWduOiB0b3A7XHJcbiAgICB9XHJcbiAgICB0aCB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgfVxyXG4gICAgdHI6bnRoLWNoaWxkKGV2ZW4pIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTtcclxuICAgIH1cclxuICAgIC8qIE1hdGggcmVuZGVyaW5nIHN1cHBvcnQgKi9cclxuICAgIC5rYXRleCB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMWVtO1xyXG4gICAgfVxyXG4gICAgLmthdGV4LWRpc3BsYXkge1xyXG4gICAgICBtYXJnaW46IDAuM2VtIDA7XHJcbiAgICB9XHJcbiAgPC9zdHlsZT5cclxuPC9oZWFkPlxyXG48Ym9keT5cclxuICA8aGVhZGVyPlxyXG4gICAgPGRpdiBjbGFzcz1cImNvbGxlZ2VcIj5cclxuICAgICAgJHtjb2xsZWdlTG9nb1VybCA/IGA8aW1nIHNyYz1cIiR7Y29sbGVnZUxvZ29Vcmx9XCIgYWx0PVwibG9nb1wiIC8+YCA6ICcnfVxyXG4gICAgICA8c3Bhbj4ke2NvbGxlZ2VOYW1lfTwvc3Bhbj5cclxuICAgIDwvZGl2PlxyXG4gICAgPGRpdiBjbGFzcz1cInRpdGxlXCI+JHt0aXRsZX08L2Rpdj5cclxuICAgIDxkaXYgY2xhc3M9XCJtZXRhXCI+XHJcbiAgICAgIDxkaXY+VG90YWwgTWFya3M6ICR7dG90YWxNYXJrc308L2Rpdj5cclxuICAgICAgPGRpdj5EdXJhdGlvbjogJHtkdXJhdGlvbn0gbWluczwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgPC9oZWFkZXI+XHJcbiAgPGhyIC8+XHJcbiAgPHA+JHtkZXNjcmlwdGlvbn08L3A+XHJcbiAgPGRpdiBjbGFzcz1cInF1ZXN0aW9uc1wiPlxyXG4gICAgJHsoKCkgPT4ge1xyXG4gICAgICAvLyBHcm91cCBxdWVzdGlvbnMgYnkgc3ViamVjdFxyXG4gICAgICBjb25zdCBncm91cGVkUXVlc3Rpb25zID0gcXVlc3Rpb25zLnJlZHVjZSgoZ3JvdXBzLCBxdWVzdGlvbikgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN1YmplY3QgPSBxdWVzdGlvbi5zdWJqZWN0IHx8ICdHZW5lcmFsJztcclxuICAgICAgICBpZiAoIWdyb3Vwc1tzdWJqZWN0XSkge1xyXG4gICAgICAgICAgZ3JvdXBzW3N1YmplY3RdID0gW107XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGdyb3Vwc1tzdWJqZWN0XS5wdXNoKHF1ZXN0aW9uKTtcclxuICAgICAgICByZXR1cm4gZ3JvdXBzO1xyXG4gICAgICB9LCB7fSBhcyBSZWNvcmQ8c3RyaW5nLCB0eXBlb2YgcXVlc3Rpb25zPik7XHJcblxyXG4gICAgICAvLyBHZW5lcmF0ZSBIVE1MIGZvciBlYWNoIHN1YmplY3QgZ3JvdXBcclxuICAgICAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKGdyb3VwZWRRdWVzdGlvbnMpLm1hcCgoW3N1YmplY3QsIHN1YmplY3RRdWVzdGlvbnNdKSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc3ViamVjdEh0bWwgPSBgXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzPVwic3ViamVjdC1zZWN0aW9uXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJzdWJqZWN0LWhlYWRpbmdcIj5TdWJqZWN0OiAke3N1YmplY3R9PC9kaXY+XHJcbiAgICAgICAgICAgIDxociBzdHlsZT1cIm1hcmdpbjogOHB4IDA7IGJvcmRlcjogbm9uZTsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMzMzM7XCIgLz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzcz1cInN1YmplY3QtY29udGVudFwiPlxyXG4gICAgICAgICAgICAgICR7c3ViamVjdFF1ZXN0aW9ucy5tYXAoKHEsIHF1ZXN0aW9uSW5kZXgpID0+IHtcclxuICAgICAgICAgICAgICAgIC8vIEZpeCBMYVRlWCBmb3JtYXR0aW5nIGlzc3VlcyBtb3JlIGNvbXByZWhlbnNpdmVseVxyXG4gICAgICAgICAgICAgICAgLy8gU2ltcGxlIExhVGVYIGZpeGVzIC0ga2VlcCBpdCBtaW5pbWFsIGFuZCB3b3JraW5nXHJcbiAgICAgICAgICAgICAgICBjb25zdCBmaXhlZFF1ZXN0aW9uID0gcS5xdWVzdGlvblxyXG4gICAgICAgICAgICAgICAgICAvLyBGaXggdGhlIG1haW4gXFxmZnJhYyBpc3N1ZSAtIGV4YWN0IHBhdHRlcm5zIGZyb20geW91ciBleGFtcGxlc1xyXG4gICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxcXGZmcmFjz4lMUi9nLCAnXFxcXGZyYWN7z4l9e0xSfScpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxz4lDUi9nLCAnXFxcXGZyYWN7MX17z4lDUn0nKVxyXG4gICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxcXGZmcmFjTENcXFxcZmZyYWMxUi9nLCAnXFxcXGZyYWN7TEN9e1xcXFxmcmFjezF9e1J9fScpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWNSTEMvZywgJ1xcXFxmcmFje1J9e0xDfScpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxMDDPgE1Iei9nLCAnXFxcXGZyYWN7MTAwfXvPgE1Ien0nKVxyXG4gICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxcXGZmcmFjMTAwMM+ASHovZywgJ1xcXFxmcmFjezEwMDB9e8+ASHp9JylcclxuICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXFxmZnJhYzExMDAwb2htL2csICdcXFxcZnJhY3sxfXsxMDAwb2htfScpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxQ8+JL2csICdcXFxcZnJhY3sxfXtDz4l9JylcclxuXHJcbiAgICAgICAgICAgICAgICAgIC8vIEZpeCBiYXNpYyBcXGZmcmFjIHBhdHRlcm5zXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWNcXHsvZywgJ1xcXFxmcmFjeycpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMoW8+Jz4DOsS3Pic6pXSkoW0EtWl0rKS9nLCAnXFxcXGZyYWN7JDF9eyQyfScpXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMoXFxkKykoW8+Jz4DOsS3Pic6pXSkoW0EtWl0rKS9nLCAnXFxcXGZyYWN7JDF9eyQyJDN9JylcclxuICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXFxmZnJhYyhbQS1aXSspKFtBLVpdKykvZywgJ1xcXFxmcmFjeyQxfXskMn0nKVxyXG5cclxuICAgICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBpbWFnZXMgLSBoYW5kbGUgYm90aCBtYXJrZG93biBhbmQgcmF3IGJhc2U2NFxyXG4gICAgICAgICAgICAgICAgICAucmVwbGFjZSgvIVxcWyhbXlxcXV0qKVxcXVxcKGRhdGE6aW1hZ2VcXC8oW147XSspO2Jhc2U2NCwoW14pXSspXFwpL2csXHJcbiAgICAgICAgICAgICAgICAgICAgJzxpbWcgc3JjPVwiZGF0YTppbWFnZS8kMjtiYXNlNjQsJDNcIiBhbHQ9XCIkMVwiIHN0eWxlPVwibWF4LXdpZHRoOjMwMHB4O2hlaWdodDphdXRvO2Rpc3BsYXk6YmxvY2s7bWFyZ2luOjEwcHggYXV0bztib3JkZXI6MXB4IHNvbGlkICNkZGQ7cGFkZGluZzo1cHg7XCIgLz4nKVxyXG4gICAgICAgICAgICAgICAgICAvLyBIYW5kbGUgcmF3IGRhdGEgVVJMcyB0aGF0IG1pZ2h0IG5vdCBiZSBpbiBtYXJrZG93biBmb3JtYXRcclxuICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoLyhkYXRhOmltYWdlXFwvW147XSs7YmFzZTY0LFtBLVphLXowLTkrL10rPSopL2csXHJcbiAgICAgICAgICAgICAgICAgICAgJzxpbWcgc3JjPVwiJDFcIiBhbHQ9XCJRdWVzdGlvbiBJbWFnZVwiIHN0eWxlPVwibWF4LXdpZHRoOjMwMHB4O2hlaWdodDphdXRvO2Rpc3BsYXk6YmxvY2s7bWFyZ2luOjEwcHggYXV0bztib3JkZXI6MXB4IHNvbGlkICNkZGQ7cGFkZGluZzo1cHg7XCIgLz4nKVxyXG4gICAgICAgICAgICAgICAgICAvLyBSZW1vdmUgYnJva2VuIGltYWdlIHJlZmVyZW5jZXMgbGlrZSBcImltZyDiiJIgMS5qcGVnIChkYXRhOi4uLilcIlxyXG4gICAgICAgICAgICAgICAgICAucmVwbGFjZSgvaW1nXFxzKlviiJItXVxccypcXGQrXFwuKGpwZWd8anBnfHBuZylcXHMqXFwoW14pXSpcXCkvZ2ksICcnKVxyXG4gICAgICAgICAgICAgICAgICAvLyBSZW1vdmUgYW55IHJlbWFpbmluZyBzdGFuZGFsb25lIGJhc2U2NCBzdHJpbmdzIHRoYXQgY291bGRuJ3QgYmUgcHJvY2Vzc2VkXHJcbiAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9bQS1aYS16MC05Ky9dezEwMCx9PXswLDJ9L2csICcnKTtcclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBmaXhlZE9wdGlvbnMgPSBxLm9wdGlvbnMubWFwKG9wdCA9PlxyXG4gICAgICAgICAgICAgICAgICAvLyBTaW1wbGUgTGFUZVggZml4ZXMgZm9yIG9wdGlvbnMgLSBzYW1lIGFzIHF1ZXN0aW9uc1xyXG4gICAgICAgICAgICAgICAgICBvcHRcclxuICAgICAgICAgICAgICAgICAgICAvLyBGaXggdGhlIG1haW4gXFxmZnJhYyBpc3N1ZSAtIGV4YWN0IHBhdHRlcm5zXHJcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXFxmZnJhY8+JTFIvZywgJ1xcXFxmcmFje8+JfXtMUn0nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxz4lDUi9nLCAnXFxcXGZyYWN7MX17z4lDUn0nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWNMQ1xcXFxmZnJhYzFSL2csICdcXFxcZnJhY3tMQ317XFxcXGZyYWN7MX17Un19JylcclxuICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxcXGZmcmFjUkxDL2csICdcXFxcZnJhY3tSfXtMQ30nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxMDDPgE1Iei9nLCAnXFxcXGZyYWN7MTAwfXvPgE1Ien0nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxMDAwz4BIei9nLCAnXFxcXGZyYWN7MTAwMH17z4BIen0nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxMTAwMG9obS9nLCAnXFxcXGZyYWN7MX17MTAwMG9obX0nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMxQ8+JL2csICdcXFxcZnJhY3sxfXtDz4l9JylcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRml4IGJhc2ljIFxcZmZyYWMgcGF0dGVybnNcclxuICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXFxcXGZmcmFjXFx7L2csICdcXFxcZnJhY3snKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMoW8+Jz4DOsS3Pic6pXSkoW0EtWl0rKS9nLCAnXFxcXGZyYWN7JDF9eyQyfScpXHJcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXFxmZnJhYyhcXGQrKShbz4nPgM6xLc+JzqldKShbQS1aXSspL2csICdcXFxcZnJhY3skMX17JDIkM30nKVxyXG4gICAgICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcZmZyYWMoW0EtWl0rKShbQS1aXSspL2csICdcXFxcZnJhY3skMX17JDJ9JylcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBpbWFnZXMgLSBoYW5kbGUgYm90aCBtYXJrZG93biBhbmQgcmF3IGJhc2U2NCAoc2FtZSBhcyBxdWVzdGlvbnMpXHJcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoLyFcXFsoW15cXF1dKilcXF1cXChkYXRhOmltYWdlXFwvKFteO10rKTtiYXNlNjQsKFteKV0rKVxcKS9nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgJzxpbWcgc3JjPVwiZGF0YTppbWFnZS8kMjtiYXNlNjQsJDNcIiBhbHQ9XCIkMVwiIHN0eWxlPVwibWF4LXdpZHRoOjIwMHB4O2hlaWdodDphdXRvO2Rpc3BsYXk6aW5saW5lLWJsb2NrO21hcmdpbjo1cHg7Ym9yZGVyOjFweCBzb2xpZCAjZGRkO3BhZGRpbmc6M3B4O1wiIC8+JylcclxuICAgICAgICAgICAgICAgICAgICAvLyBIYW5kbGUgcmF3IGRhdGEgVVJMcyB0aGF0IG1pZ2h0IG5vdCBiZSBpbiBtYXJrZG93biBmb3JtYXRcclxuICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvKGRhdGE6aW1hZ2VcXC9bXjtdKztiYXNlNjQsW0EtWmEtejAtOSsvXSs9KikvZyxcclxuICAgICAgICAgICAgICAgICAgICAgICc8aW1nIHNyYz1cIiQxXCIgYWx0PVwiT3B0aW9uIEltYWdlXCIgc3R5bGU9XCJtYXgtd2lkdGg6MjAwcHg7aGVpZ2h0OmF1dG87ZGlzcGxheTppbmxpbmUtYmxvY2s7bWFyZ2luOjVweDtib3JkZXI6MXB4IHNvbGlkICNkZGQ7cGFkZGluZzozcHg7XCIgLz4nKVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSBicm9rZW4gaW1hZ2UgcmVmZXJlbmNlcyBsaWtlIFwiaW1nIOKIkiAxLmpwZWcgKGRhdGE6Li4uKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL2ltZ1xccypb4oiSLV1cXHMqXFxkK1xcLihqcGVnfGpwZ3xwbmcpXFxzKlxcKFteKV0qXFwpL2dpLCAnJylcclxuICAgICAgICAgICAgICAgICAgICAvLyBSZW1vdmUgYW55IHJlbWFpbmluZyBzdGFuZGFsb25lIGJhc2U2NCBzdHJpbmdzIHRoYXQgY291bGRuJ3QgYmUgcHJvY2Vzc2VkXHJcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1tBLVphLXowLTkrL117MTAwLH09ezAsMn0vZywgJycpXHJcbiAgICAgICAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiBgXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJxdWVzdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+JHtxdWVzdGlvbkluZGV4ICsgMX0uPC9zdHJvbmc+ICR7Zml4ZWRRdWVzdGlvbn08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz1cIm9wdGlvbnNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICR7Zml4ZWRPcHRpb25zLm1hcCgob3B0LCBpKSA9PiBgPHA+JHtTdHJpbmcuZnJvbUNoYXJDb2RlKDk3ICsgaSl9KSAke29wdH08L3A+YCkuam9pbignJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAke2luY2x1ZGVBbnN3ZXJzID8gYDxwPjxlbT5BbnN3ZXI6PC9lbT4gJHtxLmFuc3dlcn08L3A+YCA6ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5gO1xyXG4gICAgICAgICAgICAgIH0pLmpvaW4oJycpfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PmA7XHJcbiAgICAgICAgcmV0dXJuIHN1YmplY3RIdG1sO1xyXG4gICAgICB9KS5qb2luKCcnKTtcclxuICAgIH0pKCl9XHJcbiAgPC9kaXY+XHJcbiAgPGZvb3Rlcj5NZWRpY29zIHwgJHtuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvZm9vdGVyPlxyXG48L2JvZHk+XHJcbjwvaHRtbD5gO1xyXG5cclxuICAgIGNvbnN0IGJyb3dzZXIgPSBhd2FpdCBwdXBwZXRlZXIubGF1bmNoKHtcclxuICAgICAgYXJnczogWyctLW5vLXNhbmRib3gnLCAnLS1kaXNhYmxlLXNldHVpZC1zYW5kYm94J10sXHJcbiAgICB9KTtcclxuICAgIGNvbnN0IHBhZ2UgPSBhd2FpdCBicm93c2VyLm5ld1BhZ2UoKTtcclxuXHJcbiAgICBhd2FpdCBwYWdlLnNldENvbnRlbnQoaHRtbCwgeyB3YWl0VW50aWw6ICduZXR3b3JraWRsZTAnIH0pO1xyXG5cclxuICAgIC8vIFdhaXQgZm9yIEthVGVYIHRvIGxvYWQgYW5kIHJlbmRlciBtYXRoXHJcbiAgICBhd2FpdCBwYWdlLndhaXRGb3JGdW5jdGlvbigoKSA9PiB7XHJcbiAgICAgIHJldHVybiAod2luZG93IGFzIGFueSkucmVuZGVyTWF0aEluRWxlbWVudCAhPT0gdW5kZWZpbmVkO1xyXG4gICAgfSwgeyB0aW1lb3V0OiA1MDAwIH0pLmNhdGNoKCgpID0+IHt9KTtcclxuXHJcbiAgICAvLyBUcmlnZ2VyIG1hdGggcmVuZGVyaW5nIG1hbnVhbGx5IGlmIG5lZWRlZFxyXG4gICAgYXdhaXQgcGFnZS5ldmFsdWF0ZSgoKSA9PiB7XHJcbiAgICAgIGlmICgod2luZG93IGFzIGFueSkucmVuZGVyTWF0aEluRWxlbWVudCkge1xyXG4gICAgICAgICh3aW5kb3cgYXMgYW55KS5yZW5kZXJNYXRoSW5FbGVtZW50KGRvY3VtZW50LmJvZHksIHtcclxuICAgICAgICAgIGRlbGltaXRlcnM6IFtcclxuICAgICAgICAgICAge2xlZnQ6ICckJCcsIHJpZ2h0OiAnJCQnLCBkaXNwbGF5OiB0cnVlfSxcclxuICAgICAgICAgICAge2xlZnQ6ICckJywgcmlnaHQ6ICckJywgZGlzcGxheTogZmFsc2V9LFxyXG4gICAgICAgICAgICB7bGVmdDogJ1xcXFwoJywgcmlnaHQ6ICdcXFxcKScsIGRpc3BsYXk6IGZhbHNlfSxcclxuICAgICAgICAgICAge2xlZnQ6ICdcXFxcWycsIHJpZ2h0OiAnXFxcXF0nLCBkaXNwbGF5OiB0cnVlfVxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIHRocm93T25FcnJvcjogZmFsc2UsXHJcbiAgICAgICAgICBlcnJvckNvbG9yOiAnI2NjMDAwMCcsXHJcbiAgICAgICAgICBzdHJpY3Q6IGZhbHNlXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG5cclxuICAgIC8vIFdhaXQgZm9yIHJlbmRlcmluZyB0byBjb21wbGV0ZVxyXG4gICAgYXdhaXQgcGFnZS53YWl0Rm9yRnVuY3Rpb24oKCkgPT4ge1xyXG4gICAgICBjb25zdCBtYXRoRWxlbWVudHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdzY3JpcHRbdHlwZT1cIm1hdGgvdGV4XCJdJyk7XHJcbiAgICAgIGNvbnN0IGthdGV4RWxlbWVudHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcua2F0ZXgnKTtcclxuICAgICAgcmV0dXJuIG1hdGhFbGVtZW50cy5sZW5ndGggPT09IDAgfHwga2F0ZXhFbGVtZW50cy5sZW5ndGggPiAwO1xyXG4gICAgfSwgeyB0aW1lb3V0OiA1MDAwIH0pLmNhdGNoKCgpID0+IHt9KTtcclxuXHJcbiAgICAvLyBFeHRyYSBkZWxheSB0byBlbnN1cmUgbGF5b3V0IHNldHRsZXNcclxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcclxuXHJcbiAgICBjb25zdCBwZGZCdWZmZXIgPSBhd2FpdCBwYWdlLnBkZih7XHJcbiAgICAgIGZvcm1hdDogJ0E0JyxcclxuICAgICAgcHJpbnRCYWNrZ3JvdW5kOiB0cnVlLFxyXG4gICAgICBtYXJnaW46IHsgdG9wOiAnMjBtbScsIHJpZ2h0OiAnMTVtbScsIGJvdHRvbTogJzIwbW0nLCBsZWZ0OiAnMTVtbScgfSxcclxuICAgIH0pO1xyXG5cclxuICAgIGF3YWl0IGJyb3dzZXIuY2xvc2UoKTtcclxuXHJcbiAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShwZGZCdWZmZXIsIHtcclxuICAgICAgc3RhdHVzOiAyMDAsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL3BkZicsXHJcbiAgICAgICAgJ0NvbnRlbnQtRGlzcG9zaXRpb24nOiBgYXR0YWNobWVudDsgZmlsZW5hbWU9XCIke2ZpbGVuYW1lfVwiYCxcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ1BERiBnZW5lcmF0aW9uIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShKU09OLnN0cmluZ2lmeSh7IGVycm9yOiAnUERGIGdlbmVyYXRpb24gZmFpbGVkJyB9KSwgeyBzdGF0dXM6IDUwMCB9KTtcclxuICB9XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJwdXBwZXRlZXIiLCJQT1NUIiwicmVxIiwicGF5bG9hZCIsImpzb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZHVyYXRpb24iLCJ0b3RhbE1hcmtzIiwicXVlc3Rpb25zIiwiaW5jbHVkZUFuc3dlcnMiLCJmaWxlbmFtZSIsImNvbGxlZ2VOYW1lIiwiY29sbGVnZUxvZ29VcmwiLCJodG1sIiwiZ3JvdXBlZFF1ZXN0aW9ucyIsInJlZHVjZSIsImdyb3VwcyIsInF1ZXN0aW9uIiwic3ViamVjdCIsInB1c2giLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwic3ViamVjdFF1ZXN0aW9ucyIsInN1YmplY3RIdG1sIiwicSIsInF1ZXN0aW9uSW5kZXgiLCJmaXhlZFF1ZXN0aW9uIiwicmVwbGFjZSIsImZpeGVkT3B0aW9ucyIsIm9wdGlvbnMiLCJvcHQiLCJpIiwiU3RyaW5nIiwiZnJvbUNoYXJDb2RlIiwiam9pbiIsImFuc3dlciIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJicm93c2VyIiwibGF1bmNoIiwiYXJncyIsInBhZ2UiLCJuZXdQYWdlIiwic2V0Q29udGVudCIsIndhaXRVbnRpbCIsIndhaXRGb3JGdW5jdGlvbiIsIndpbmRvdyIsInJlbmRlck1hdGhJbkVsZW1lbnQiLCJ1bmRlZmluZWQiLCJ0aW1lb3V0IiwiY2F0Y2giLCJldmFsdWF0ZSIsImRvY3VtZW50IiwiYm9keSIsImRlbGltaXRlcnMiLCJsZWZ0IiwicmlnaHQiLCJkaXNwbGF5IiwidGhyb3dPbkVycm9yIiwiZXJyb3JDb2xvciIsInN0cmljdCIsIm1hdGhFbGVtZW50cyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJrYXRleEVsZW1lbnRzIiwibGVuZ3RoIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwicGRmQnVmZmVyIiwicGRmIiwiZm9ybWF0IiwicHJpbnRCYWNrZ3JvdW5kIiwibWFyZ2luIiwidG9wIiwiYm90dG9tIiwiY2xvc2UiLCJzdGF0dXMiLCJoZWFkZXJzIiwiZXJyb3IiLCJjb25zb2xlIiwiSlNPTiIsInN0cmluZ2lmeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();