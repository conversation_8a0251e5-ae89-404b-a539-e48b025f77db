/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle embedded base64 images before processing tables\n    processedText = processedText// Handle newline-separated base64 images (common in extracted content)\n    .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+)/g, '<br><img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle inline base64 images\n    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+)/g, '<img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />');\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(q.question);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n                    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                        // Try to find matching image in question's imageData\n                        const imageData = q.imageData || q.chemicalImages;\n                        if (imageData && typeof imageData === 'object') {\n                            // Debug: Looking for image match\n                            // Try multiple matching strategies\n                            let imageKey = null;\n                            // Strategy 1: Exact match\n                            if (imageData[src]) {\n                                imageKey = src;\n                            } else if (imageData[src]) {\n                                imageKey = src;\n                            } else {\n                                const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                            }\n                            // Strategy 4: Try partial matches\n                            if (!imageKey) {\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                            }\n                            // Strategy 5: Extract numbers and match\n                            if (!imageKey) {\n                                const srcNumbers = src.match(/\\d+/g);\n                                if (srcNumbers) {\n                                    imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                }\n                            }\n                            if (imageKey && imageData[imageKey]) {\n                                return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            } else {\n                            // No matching image found\n                            }\n                        }\n                        return `[Missing Image: ${src}]`;\n                    })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                            // Try to find matching image in question's imageData\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object') {\n                                // Try multiple matching strategies\n                                let imageKey = null;\n                                // Strategy 1: Exact match\n                                if (imageData[src]) {\n                                    imageKey = src;\n                                } else {\n                                    const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                                }\n                                // Strategy 3: Try partial matches\n                                if (!imageKey) {\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                                }\n                                // Strategy 4: Extract numbers and match\n                                if (!imageKey) {\n                                    const srcNumbers = src.match(/\\d+/g);\n                                    if (srcNumbers) {\n                                        imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                    }\n                                }\n                                if (imageKey && imageData[imageKey]) {\n                                    return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                                }\n                            }\n                            return `[Missing Image: ${src}]`;\n                        })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();