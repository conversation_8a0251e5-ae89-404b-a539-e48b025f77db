/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle embedded base64 images before processing tables\n    processedText = processedText// Handle newline-separated base64 images (common in extracted content)\n    .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<br><img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle inline base64 images\n    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />');\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(q.question);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle raw data URLs that are directly embedded in text (like your case)\n                    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle newline-separated base64 images (common in extracted content)\n                    .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<br><img src=\"$1\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n                    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                        // Try to find matching image in question's imageData\n                        const imageData = q.imageData || q.chemicalImages;\n                        if (imageData && typeof imageData === 'object') {\n                            // Debug: Looking for image match\n                            // Try multiple matching strategies\n                            let imageKey = null;\n                            // Strategy 1: Exact match\n                            if (imageData[src]) {\n                                imageKey = src;\n                            } else if (imageData[src]) {\n                                imageKey = src;\n                            } else {\n                                const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                            }\n                            // Strategy 4: Try partial matches\n                            if (!imageKey) {\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                            }\n                            // Strategy 5: Extract numbers and match\n                            if (!imageKey) {\n                                const srcNumbers = src.match(/\\d+/g);\n                                if (srcNumbers) {\n                                    imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                }\n                            }\n                            if (imageKey && imageData[imageKey]) {\n                                return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            } else {\n                            // No matching image found\n                            }\n                        }\n                        return `[Missing Image: ${src}]`;\n                    })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64 (same as questions)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle raw data URLs that are directly embedded in text\n                        .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Option Image\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle newline-separated base64 images in options\n                        .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<br><img src=\"$1\" alt=\"Option Image\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                            // Try to find matching image in question's imageData\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object') {\n                                // Try multiple matching strategies\n                                let imageKey = null;\n                                // Strategy 1: Exact match\n                                if (imageData[src]) {\n                                    imageKey = src;\n                                } else {\n                                    const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                                }\n                                // Strategy 3: Try partial matches\n                                if (!imageKey) {\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                                }\n                                // Strategy 4: Extract numbers and match\n                                if (!imageKey) {\n                                    const srcNumbers = src.match(/\\d+/g);\n                                    if (srcNumbers) {\n                                        imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                    }\n                                }\n                                if (imageKey && imageData[imageKey]) {\n                                    return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                                }\n                            }\n                            return `[Missing Image: ${src}]`;\n                        })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();