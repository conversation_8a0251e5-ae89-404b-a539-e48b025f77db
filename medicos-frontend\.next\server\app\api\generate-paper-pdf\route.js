/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // Skip base64 image processing here - it's handled separately in the main processing\n    // This prevents double processing and conflicts with the main image handling logic\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        // Debug logging\n        console.log('PDF Generation Request:', {\n            title,\n            questionsCount: questions?.length || 0,\n            firstQuestion: questions?.[0] ? {\n                question: questions[0].question?.substring(0, 100) + '...',\n                hasImageUrls: !!questions[0].imageUrls,\n                imageUrlsCount: (questions[0].imageUrls || []).length\n            } : null\n        });\n        // Validate questions data\n        if (!questions || !Array.isArray(questions) || questions.length === 0) {\n            console.error('PDF Generation Error: No questions provided');\n            throw new Error('No questions provided for PDF generation');\n        }\n        // Validate each question has required fields\n        const validQuestions = questions.filter((q)=>q && (q.question || q.content));\n        if (validQuestions.length === 0) {\n            console.error('PDF Generation Error: No valid questions found');\n            throw new Error('No valid questions found for PDF generation');\n        }\n        console.log(`Processing ${validQuestions.length} valid questions out of ${questions.length} total`);\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 24px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      \n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n    /* Image styling */\n    img {\n      max-width: 300px !important;\n      height: auto !important;\n      display: block !important;\n      margin: 10px auto !important;\n      border: 1px solid #ddd !important;\n      padding: 5px !important;\n      break-inside: avoid;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = validQuestions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            console.log('Grouped questions for HTML:', Object.keys(groupedQuestions));\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            \n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    try {\n                        // Process question text and handle images from imageUrls array\n                        let questionText = q.question;\n                        try {\n                            // Check for images in imageUrls array (new database structure)\n                            const imageUrls = q.imageUrls || [];\n                            console.log(`Question ${questionIndex + 1} imageUrls:`, imageUrls?.length || 0, 'images');\n                            if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {\n                                // Validate that the first image is a valid base64 string\n                                const firstImage = imageUrls[0];\n                                console.log(`Question ${questionIndex + 1} first image:`, firstImage ? firstImage.substring(0, 50) + '...' : 'null');\n                                if (firstImage && typeof firstImage === 'string' && firstImage.startsWith('data:image/')) {\n                                    console.log(`Question ${questionIndex + 1}: Processing valid base64 image`);\n                                    // Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images\n                                    const originalText = questionText;\n                                    let markdownReplacements = 0;\n                                    questionText = questionText.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (match, alt)=>{\n                                        console.log(`Replacing markdown image: ${match}`);\n                                        markdownReplacements++;\n                                        return `<img src=\"${firstImage}\" alt=\"${alt || 'Question Image'}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                    });\n                                    // Replace HTML img tags with actual images (but preserve existing valid ones)\n                                    let htmlReplacements = 0;\n                                    questionText = questionText.replace(/<img[^>]*>/gi, (match)=>{\n                                        // If it already has a valid src attribute, keep it\n                                        if (match.includes('src=\"data:image/')) {\n                                            return match;\n                                        }\n                                        console.log(`Replacing incomplete HTML img tag: ${match.substring(0, 50)}...`);\n                                        htmlReplacements++;\n                                        return `<img src=\"${firstImage}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                    });\n                                    console.log(`Question ${questionIndex + 1}: Made ${markdownReplacements} markdown replacements, ${htmlReplacements} HTML replacements`);\n                                    // If content mentions images but no image tags found, append the first image\n                                    if (!questionText.includes('<img') && imageUrls.length > 0) {\n                                        const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(questionText);\n                                        if (hasImageKeywords) {\n                                            console.log(`Question ${questionIndex + 1}: Adding image for keywords`);\n                                            questionText += `\\n<img src=\"${firstImage}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                        }\n                                    }\n                                    if (originalText !== questionText) {\n                                        console.log(`Question ${questionIndex + 1}: Text modified for images`);\n                                    }\n                                } else {\n                                    console.log(`Question ${questionIndex + 1}: Invalid image format:`, typeof firstImage, firstImage ? firstImage.substring(0, 20) : 'null');\n                                }\n                            } else {\n                                console.log(`Question ${questionIndex + 1}: No imageUrls found`);\n                            }\n                        } catch (error) {\n                            console.error(`Error processing images for question ${questionIndex + 1}:`, error);\n                        // Continue without images if there's an error\n                        }\n                        // Fallback: Check for legacy imageData or chemicalImages fields\n                        try {\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object' && !questionText.includes('<img')) {\n                                // If we have image data but no images in question text, add them\n                                const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                                if (!hasImagesInText) {\n                                    // Add the first available image to the question text\n                                    const firstImageKey = Object.keys(imageData)[0];\n                                    if (firstImageKey && imageData[firstImageKey]) {\n                                        questionText = questionText + '\\n' + imageData[firstImageKey];\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error processing legacy image data:', error);\n                        }\n                        // Process question text with tables, images, and LaTeX\n                        let processedQuestion = '';\n                        try {\n                            processedQuestion = processTextForPDF(questionText);\n                            // Final cleanup: Remove any broken img tags that don't have src attributes\n                            processedQuestion = processedQuestion.replace(/<img(?![^>]*src=)[^>]*>/gi, '');\n                            // Also clean up any remaining broken image references\n                            processedQuestion = processedQuestion.replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                        } catch (error) {\n                            console.error('Error processing question text:', error);\n                            processedQuestion = questionText; // Fallback to raw text\n                        }\n                        // Apply LaTeX fixes after table processing\n                        processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                        const processedOptions = (q.options || []).map((opt)=>{\n                            try {\n                                // Process option text with tables first\n                                let processedOpt = processTextForPDF(opt);\n                                // Clean up any broken img tags in options too\n                                processedOpt = processedOpt.replace(/<img(?![^>]*src=)[^>]*>/gi, '');\n                                // Apply LaTeX fixes after table processing\n                                return processedOpt// Fix the main \\ffrac issue - exact patterns\n                                .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                                .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                                .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                                // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                                .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                            } catch (error) {\n                                console.error('Error processing option:', error);\n                                return opt; // Fallback to raw option text\n                            }\n                        });\n                        return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                    } catch (error) {\n                        console.error('Error processing question:', error);\n                        // Fallback to basic question display\n                        return `\n                    <div class=\"question\">\n                      <p><strong>${questionIndex + 1}.</strong> ${q.question || 'Error loading question'}</p>\n                      <div class=\"options\">\n                        ${(q.options || []).map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                        ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                      </div>\n                    </div>`;\n                    }\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'domcontentloaded'\n        });\n        // Wait for images to load\n        await page.evaluate(()=>{\n            return Promise.all(Array.from(document.images).map((img)=>{\n                if (img.complete) return Promise.resolve();\n                return new Promise((resolve)=>{\n                    img.addEventListener('load', resolve);\n                    img.addEventListener('error', resolve); // Resolve even on error to not block\n                    setTimeout(resolve, 3000); // Timeout after 3 seconds\n                });\n            }));\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();