/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // Simple, direct base64 image processing\n    // Look for base64 data and convert to img tags\n    const base64Pattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+/g;\n    const matches = processedText.match(base64Pattern);\n    if (matches) {\n        matches.forEach((base64Data)=>{\n            // Clean the base64 data - remove any whitespace or newlines\n            const cleanBase64 = base64Data.replace(/\\s+/g, '');\n            // Replace each base64 string with an img tag\n            const imgTag = `<img src=\"${cleanBase64}\" alt=\"\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n            processedText = processedText.replace(base64Data, imgTag);\n        });\n    }\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 24px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      \n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n    /* Image styling */\n    img {\n      max-width: 300px !important;\n      height: auto !important;\n      display: block !important;\n      margin: 10px auto !important;\n      border: 1px solid #ddd !important;\n      padding: 5px !important;\n      break-inside: avoid;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            \n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // First, check if there are images in separate fields and inject them into question text\n                    let questionText = q.question;\n                    // Check for images in imageData, chemicalImages, or imageUrls fields\n                    const imageData = q.imageData || q.chemicalImages;\n                    if (imageData && typeof imageData === 'object') {\n                        // If we have image data but no images in question text, add them\n                        const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                        if (!hasImagesInText) {\n                            // Add the first available image to the question text\n                            const firstImageKey = Object.keys(imageData)[0];\n                            if (firstImageKey && imageData[firstImageKey]) {\n                                questionText = questionText + '\\n' + imageData[firstImageKey];\n                            }\n                        }\n                    }\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(questionText);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'domcontentloaded'\n        });\n        // Wait for images to load\n        await page.evaluate(()=>{\n            return Promise.all(Array.from(document.images).map((img)=>{\n                if (img.complete) return Promise.resolve();\n                return new Promise((resolve)=>{\n                    img.addEventListener('load', resolve);\n                    img.addEventListener('error', resolve); // Resolve even on error to not block\n                    setTimeout(resolve, 3000); // Timeout after 3 seconds\n                });\n            }));\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();