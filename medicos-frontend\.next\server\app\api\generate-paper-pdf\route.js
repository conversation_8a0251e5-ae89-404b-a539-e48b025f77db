/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // Simple, direct base64 image processing\n    // Look for base64 data and convert to img tags\n    const base64Pattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+/g;\n    const matches = processedText.match(base64Pattern);\n    if (matches) {\n        matches.forEach((base64Data)=>{\n            // Clean the base64 data - remove any whitespace or newlines\n            const cleanBase64 = base64Data.replace(/\\s+/g, '');\n            // Replace each base64 string with an img tag\n            const imgTag = `<img src=\"${cleanBase64}\" alt=\"\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n            processedText = processedText.replace(base64Data, imgTag);\n        });\n    }\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 24px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      \n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n    /* Image styling */\n    img {\n      max-width: 300px !important;\n      height: auto !important;\n      display: block !important;\n      margin: 10px auto !important;\n      border: 1px solid #ddd !important;\n      padding: 5px !important;\n      break-inside: avoid;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            \n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Process question text and handle images from imageUrls array\n                    let questionText = q.question;\n                    // Check for images in imageUrls array (new database structure)\n                    const imageUrls = q.imageUrls || [];\n                    if (imageUrls && imageUrls.length > 0) {\n                        // Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images\n                        questionText = questionText.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt)=>{\n                            // Use the first available image since backend extracts images in order\n                            return `<img src=\"${imageUrls[0]}\" alt=\"${alt || 'Question Image'}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                        });\n                        // Replace HTML img tags with actual images\n                        questionText = questionText.replace(/<img[^>]*src=[\"']([^\"']*)[\"'][^>]*>/gi, ()=>{\n                            // Use the first available image\n                            return `<img src=\"${imageUrls[0]}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                        });\n                        // If content mentions images but no image tags found, append the first image\n                        if (!questionText.includes('<img') && imageUrls.length > 0) {\n                            const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(questionText);\n                            if (hasImageKeywords) {\n                                questionText += `\\n<img src=\"${imageUrls[0]}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            }\n                        }\n                    }\n                    // Fallback: Check for legacy imageData or chemicalImages fields\n                    const imageData = q.imageData || q.chemicalImages;\n                    if (imageData && typeof imageData === 'object' && !questionText.includes('<img')) {\n                        // If we have image data but no images in question text, add them\n                        const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                        if (!hasImagesInText) {\n                            // Add the first available image to the question text\n                            const firstImageKey = Object.keys(imageData)[0];\n                            if (firstImageKey && imageData[firstImageKey]) {\n                                questionText = questionText + '\\n' + imageData[firstImageKey];\n                            }\n                        }\n                    }\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(questionText);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'domcontentloaded'\n        });\n        // Wait for images to load\n        await page.evaluate(()=>{\n            return Promise.all(Array.from(document.images).map((img)=>{\n                if (img.complete) return Promise.resolve();\n                return new Promise((resolve)=>{\n                    img.addEventListener('load', resolve);\n                    img.addEventListener('error', resolve); // Resolve even on error to not block\n                    setTimeout(resolve, 3000); // Timeout after 3 seconds\n                });\n            }));\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();