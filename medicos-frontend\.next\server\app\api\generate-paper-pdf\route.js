/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // Simple, direct base64 image processing\n    // Look for base64 data and convert to img tags\n    const base64Pattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+/g;\n    const matches = processedText.match(base64Pattern);\n    if (matches) {\n        matches.forEach((base64Data)=>{\n            // Replace each base64 string with an img tag\n            const imgTag = `<img src=\"${base64Data}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n            processedText = processedText.replace(base64Data, imgTag);\n        });\n    }\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // First, check if there are images in separate fields and inject them into question text\n                    let questionText = q.question;\n                    // Check for images in imageData, chemicalImages, or imageUrls fields\n                    const imageData = q.imageData || q.chemicalImages;\n                    if (imageData && typeof imageData === 'object') {\n                        // If we have image data but no images in question text, add them\n                        const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                        if (!hasImagesInText) {\n                            // Add the first available image to the question text\n                            const firstImageKey = Object.keys(imageData)[0];\n                            if (firstImageKey && imageData[firstImageKey]) {\n                                questionText = questionText + '\\n' + imageData[firstImageKey];\n                            }\n                        }\n                    }\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(questionText);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '')// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '')// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();