/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(q.question);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle raw data URLs that might not be in markdown format\n                    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n                    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                        // Try to find matching image in question's imageData\n                        const imageData = q.imageData || q.chemicalImages;\n                        if (imageData && typeof imageData === 'object') {\n                            // Debug: Looking for image match\n                            // Try multiple matching strategies\n                            let imageKey = null;\n                            // Strategy 1: Exact match\n                            if (imageData[src]) {\n                                imageKey = src;\n                            } else if (imageData[src]) {\n                                imageKey = src;\n                            } else {\n                                const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                            }\n                            // Strategy 4: Try partial matches\n                            if (!imageKey) {\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                            }\n                            // Strategy 5: Extract numbers and match\n                            if (!imageKey) {\n                                const srcNumbers = src.match(/\\d+/g);\n                                if (srcNumbers) {\n                                    imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                }\n                            }\n                            if (imageKey && imageData[imageKey]) {\n                                return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            } else {\n                            // No matching image found\n                            }\n                        }\n                        return `[Missing Image: ${src}]`;\n                    })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle both markdown and raw base64 (same as questions)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle raw data URLs that might not be in markdown format\n                        .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Option Image\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                            // Try to find matching image in question's imageData\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object') {\n                                // Try multiple matching strategies\n                                let imageKey = null;\n                                // Strategy 1: Exact match\n                                if (imageData[src]) {\n                                    imageKey = src;\n                                } else {\n                                    const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                                }\n                                // Strategy 3: Try partial matches\n                                if (!imageKey) {\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                                }\n                                // Strategy 4: Extract numbers and match\n                                if (!imageKey) {\n                                    const srcNumbers = src.match(/\\d+/g);\n                                    if (srcNumbers) {\n                                        imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                    }\n                                }\n                                if (imageKey && imageData[imageKey]) {\n                                    return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                                }\n                            }\n                            return `[Missing Image: ${src}]`;\n                        })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();