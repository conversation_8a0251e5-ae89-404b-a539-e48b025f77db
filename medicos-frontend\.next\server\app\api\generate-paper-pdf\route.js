/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // Simple, direct base64 image processing\n    // Look for base64 data and convert to img tags\n    const base64Pattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/=]+/g;\n    const matches = processedText.match(base64Pattern);\n    if (matches) {\n        matches.forEach((base64Data)=>{\n            // Clean the base64 data - remove any whitespace or newlines\n            const cleanBase64 = base64Data.replace(/\\s+/g, '');\n            // Replace each base64 string with an img tag\n            const imgTag = `<img src=\"${cleanBase64}\" alt=\"\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n            processedText = processedText.replace(base64Data, imgTag);\n        });\n    }\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        // Debug logging\n        console.log('PDF Generation Request:', {\n            title,\n            questionsCount: questions?.length || 0,\n            firstQuestion: questions?.[0] ? {\n                question: questions[0].question?.substring(0, 100) + '...',\n                hasImageUrls: !!questions[0].imageUrls,\n                imageUrlsCount: (questions[0].imageUrls || []).length\n            } : null\n        });\n        // Validate questions data\n        if (!questions || !Array.isArray(questions) || questions.length === 0) {\n            console.error('PDF Generation Error: No questions provided');\n            throw new Error('No questions provided for PDF generation');\n        }\n        // Validate each question has required fields\n        const validQuestions = questions.filter((q)=>q && (q.question || q.content));\n        if (validQuestions.length === 0) {\n            console.error('PDF Generation Error: No valid questions found');\n            throw new Error('No valid questions found for PDF generation');\n        }\n        console.log(`Processing ${validQuestions.length} valid questions out of ${questions.length} total`);\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 24px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      \n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n    /* Image styling */\n    img {\n      max-width: 300px !important;\n      height: auto !important;\n      display: block !important;\n      margin: 10px auto !important;\n      border: 1px solid #ddd !important;\n      padding: 5px !important;\n      break-inside: avoid;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = validQuestions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            console.log('Grouped questions for HTML:', Object.keys(groupedQuestions));\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            \n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    try {\n                        // Process question text and handle images from imageUrls array\n                        let questionText = q.question;\n                        try {\n                            // Check for images in imageUrls array (new database structure)\n                            const imageUrls = q.imageUrls || [];\n                            if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {\n                                // Validate that the first image is a valid base64 string\n                                const firstImage = imageUrls[0];\n                                if (firstImage && typeof firstImage === 'string' && firstImage.startsWith('data:image/')) {\n                                    // Replace markdown image references like ![img-13.jpeg](img-13.jpeg) with actual images\n                                    questionText = questionText.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt)=>{\n                                        // Use the first available image since backend extracts images in order\n                                        return `<img src=\"${firstImage}\" alt=\"${alt || 'Question Image'}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                    });\n                                    // Replace HTML img tags with actual images\n                                    questionText = questionText.replace(/<img[^>]*src=[\"']([^\"']*)[\"'][^>]*>/gi, ()=>{\n                                        // Use the first available image\n                                        return `<img src=\"${firstImage}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                    });\n                                    // If content mentions images but no image tags found, append the first image\n                                    if (!questionText.includes('<img') && imageUrls.length > 0) {\n                                        const hasImageKeywords = /image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(questionText);\n                                        if (hasImageKeywords) {\n                                            questionText += `\\n<img src=\"${firstImage}\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" onerror=\"this.style.display='none';\" />`;\n                                        }\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error processing images for question:', error);\n                        // Continue without images if there's an error\n                        }\n                        // Fallback: Check for legacy imageData or chemicalImages fields\n                        try {\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object' && !questionText.includes('<img')) {\n                                // If we have image data but no images in question text, add them\n                                const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                                if (!hasImagesInText) {\n                                    // Add the first available image to the question text\n                                    const firstImageKey = Object.keys(imageData)[0];\n                                    if (firstImageKey && imageData[firstImageKey]) {\n                                        questionText = questionText + '\\n' + imageData[firstImageKey];\n                                    }\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error processing legacy image data:', error);\n                        }\n                        // Process question text with tables, images, and LaTeX\n                        let processedQuestion = '';\n                        try {\n                            processedQuestion = processTextForPDF(questionText);\n                        } catch (error) {\n                            console.error('Error processing question text:', error);\n                            processedQuestion = questionText; // Fallback to raw text\n                        }\n                        // Apply LaTeX fixes after table processing\n                        processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                        const processedOptions = (q.options || []).map((opt)=>{\n                            try {\n                                // Process option text with tables first\n                                let processedOpt = processTextForPDF(opt);\n                                // Apply LaTeX fixes after table processing\n                                return processedOpt// Fix the main \\ffrac issue - exact patterns\n                                .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                                .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Remove any remaining broken image references\n                                .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                                // Remove broken image references like \"img − 1.jpeg (data:...)\"\n                                .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '');\n                            } catch (error) {\n                                console.error('Error processing option:', error);\n                                return opt; // Fallback to raw option text\n                            }\n                        });\n                        return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                    } catch (error) {\n                        console.error('Error processing question:', error);\n                        // Fallback to basic question display\n                        return `\n                    <div class=\"question\">\n                      <p><strong>${questionIndex + 1}.</strong> ${q.question || 'Error loading question'}</p>\n                      <div class=\"options\">\n                        ${(q.options || []).map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                        ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                      </div>\n                    </div>`;\n                    }\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'domcontentloaded'\n        });\n        // Wait for images to load\n        await page.evaluate(()=>{\n            return Promise.all(Array.from(document.images).map((img)=>{\n                if (img.complete) return Promise.resolve();\n                return new Promise((resolve)=>{\n                    img.addEventListener('load', resolve);\n                    img.addEventListener('error', resolve); // Resolve even on error to not block\n                    setTimeout(resolve, 3000); // Timeout after 3 seconds\n                });\n            }));\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();