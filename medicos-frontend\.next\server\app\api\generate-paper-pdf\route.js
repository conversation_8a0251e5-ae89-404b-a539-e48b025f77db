/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle embedded base64 images before processing tables\n    processedText = processedText// Handle newline-separated base64 images (common in extracted content)\n    .replace(/\\n(data:image\\/[^;]+;base64,[A-Za-z0-9+/=\\s]+)/g, '<br><img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle inline base64 images (more permissive pattern)\n    .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/=\\s]+)/g, '<img src=\"$1\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle base64 images that might have whitespace or line breaks\n    .replace(/data:image\\/([^;]+);base64,\\s*([A-Za-z0-9+/=\\s]+)/g, '<img src=\"data:image/$1;base64,$2\" alt=\"Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />');\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section {\n      page-break-before: avoid;\n      margin-top: 20px;\n    }\n    .subject-section:first-child {\n      page-break-before: avoid;\n      margin-top: 0;\n    }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-section {\n      page-break-inside: auto;\n      margin-bottom: 20px;\n      page-break-after: avoid;\n    }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 0 0 12px 0;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n      page-break-after: avoid;\n      page-break-before: avoid;\n      width: 48%;\n      display: inline-block;\n      vertical-align: top;\n    }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr style=\"page-break-after: avoid;\" />\n  <p style=\"page-break-after: avoid; margin-bottom: 10px;\">${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // First, check if there are images in separate fields and inject them into question text\n                    let questionText = q.question;\n                    // Check for images in imageData, chemicalImages, or imageUrls fields\n                    const imageData = q.imageData || q.chemicalImages;\n                    if (imageData && typeof imageData === 'object') {\n                        // If we have image data but no images in question text, add them\n                        const hasImagesInText = questionText.includes('data:image/') || questionText.includes('![');\n                        if (!hasImagesInText) {\n                            // Add the first available image to the question text\n                            const firstImageKey = Object.keys(imageData)[0];\n                            if (firstImageKey && imageData[firstImageKey]) {\n                                questionText = questionText + '\\n' + imageData[firstImageKey];\n                            }\n                        }\n                    }\n                    // Process question text with tables, images, and LaTeX\n                    let processedQuestion = processTextForPDF(questionText);\n                    // Apply LaTeX fixes after table processing\n                    processedQuestion = processedQuestion// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n                    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                        // Try to find matching image in question's imageData\n                        const imageData = q.imageData || q.chemicalImages;\n                        if (imageData && typeof imageData === 'object') {\n                            // Debug: Looking for image match\n                            // Try multiple matching strategies\n                            let imageKey = null;\n                            // Strategy 1: Exact match\n                            if (imageData[src]) {\n                                imageKey = src;\n                            } else if (imageData[src]) {\n                                imageKey = src;\n                            } else {\n                                const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                            }\n                            // Strategy 4: Try partial matches\n                            if (!imageKey) {\n                                imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                            }\n                            // Strategy 5: Extract numbers and match\n                            if (!imageKey) {\n                                const srcNumbers = src.match(/\\d+/g);\n                                if (srcNumbers) {\n                                    imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                }\n                            }\n                            if (imageKey && imageData[imageKey]) {\n                                return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                            } else {\n                            // No matching image found\n                            }\n                        }\n                        return `[Missing Image: ${src}]`;\n                    })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                    .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                    .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    const processedOptions = q.options.map((opt)=>{\n                        // Process option text with tables first\n                        let processedOpt = processTextForPDF(opt);\n                        // Apply LaTeX fixes after table processing\n                        return processedOpt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - handle markdown format only (base64 images already processed by processTextForPDF)\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                        .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                            // Try to find matching image in question's imageData\n                            const imageData = q.imageData || q.chemicalImages;\n                            if (imageData && typeof imageData === 'object') {\n                                // Try multiple matching strategies\n                                let imageKey = null;\n                                // Strategy 1: Exact match\n                                if (imageData[src]) {\n                                    imageKey = src;\n                                } else {\n                                    const srcWithoutExt = src.replace(/\\.(jpeg|jpg|png)$/i, '');\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(srcWithoutExt) || key.replace(/\\.(jpeg|jpg|png)$/i, '') === srcWithoutExt);\n                                }\n                                // Strategy 3: Try partial matches\n                                if (!imageKey) {\n                                    imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key));\n                                }\n                                // Strategy 4: Extract numbers and match\n                                if (!imageKey) {\n                                    const srcNumbers = src.match(/\\d+/g);\n                                    if (srcNumbers) {\n                                        imageKey = Object.keys(imageData).find((key)=>srcNumbers.some((num)=>key.includes(num)));\n                                    }\n                                }\n                                if (imageKey && imageData[imageKey]) {\n                                    return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                                }\n                            }\n                            return `[Missing Image: ${src}]`;\n                        })// Remove broken image references like \"img − 1.jpeg (data:...)\"\n                        .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove any remaining standalone base64 strings that couldn't be processed\n                        .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n                    });\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${processedQuestion}</p>\n                    <div class=\"options\">\n                      ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();