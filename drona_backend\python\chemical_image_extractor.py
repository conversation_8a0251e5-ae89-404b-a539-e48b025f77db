#!/usr/bin/env python3
"""
Chemical Structure Image Extractor
Specialized for extracting and processing chemical diagrams and molecular structures
"""

import os
import sys
import json
import base64
import re
import time
from typing import Dict, List, Tuple, Optional

# Import existing extractors
from superior_image_extractor import SuperiorImageExtractor
from question_extractor import QuestionExtractor

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

class ChemicalImageExtractor:
    """
    Specialized extractor for chemical structure PDFs with molecular diagrams
    """
    
    def __init__(self, ai_provider='gemini', adobe_credentials=None):
        """
        Initialize Chemical Image Extractor
        
        Args:
            ai_provider (str): AI provider for text analysis
            adobe_credentials (str): Path to Adobe credentials file
        """
        self.ai_provider = ai_provider
        self.adobe_credentials = adobe_credentials
        
        # Initialize extractors
        self.superior_extractor = SuperiorImageExtractor(adobe_credentials=adobe_credentials)
        self.question_extractor = QuestionExtractor(ai_provider=ai_provider)
        
        # Storage for extracted data
        self.extracted_images = {}
        self.processed_questions = []
        
        log_print("✅ [INIT] Chemical Image Extractor initialized")
    
    def extract_chemical_questions_with_images(self, pdf_path: str) -> Dict:
        """
        Extract chemical questions with proper image handling
        
        Args:
            pdf_path (str): Path to PDF file
            
        Returns:
            Dict: Enhanced extraction results with chemical images
        """
        try:
            log_print("🧪 [CHEMICAL] Starting chemical structure extraction...")
            start_time = time.time()
            
            # Step 1: Extract all images using superior method
            log_print("🖼️ [STEP_1] Extracting chemical structure images...")
            image_result = self.superior_extractor.extract_all_images(pdf_path)
            
            self.extracted_images = image_result.get('images', {})
            log_print(f"✅ [STEP_1] Extracted {len(self.extracted_images)} images")
            
            # Step 2: Extract questions and text
            log_print("📝 [STEP_2] Extracting questions and text...")
            questions_json = self.question_extractor.extract_structured_questions_from_pdf(pdf_path)
            
            try:
                questions = json.loads(questions_json) if questions_json else []
            except json.JSONDecodeError:
                log_print("⚠️ [STEP_2] JSON parsing failed, trying to clean...")
                questions = self._parse_and_clean_questions(questions_json)
            
            log_print(f"✅ [STEP_2] Extracted {len(questions)} questions")
            
            # Step 3: Enhanced chemical image-question matching
            log_print("🔗 [STEP_3] Performing chemical image-question matching...")
            enhanced_questions = self._match_chemical_images_to_questions(questions)
            
            log_print(f"✅ [STEP_3] Enhanced {len(enhanced_questions)} questions with chemical images")
            
            # Step 4: Fix image references and convert to proper format
            log_print("🔧 [STEP_4] Fixing image references and format...")
            fixed_questions = self._fix_image_references(enhanced_questions)
            
            # Step 5: Validate chemical questions
            log_print("✨ [STEP_5] Validating chemical questions...")
            validated_questions = self._validate_chemical_questions(fixed_questions)
            
            total_duration = time.time() - start_time
            log_print(f"🎯 [CHEMICAL] Chemical extraction completed in {total_duration:.2f}s")
            
            # Count questions with images
            questions_with_images = sum(1 for q in validated_questions 
                                     if q.get('imageUrl') and q['imageUrl'])
            
            return {
                'questions': json.dumps(validated_questions, indent=2),
                'extraction_metadata': {
                    'total_duration': total_duration,
                    'extraction_method': 'chemical_specialized',
                    'total_questions': len(validated_questions),
                    'total_images': len(self.extracted_images),
                    'questions_with_images': questions_with_images,
                    'chemical_structures_detected': True
                }
            }
            
        except Exception as e:
            log_print(f"❌ [CHEMICAL] Chemical extraction failed: {e}")
            import traceback
            traceback.print_exc()
            # Fallback to standard extraction
            return self._fallback_extraction(pdf_path)
    
    def _match_chemical_images_to_questions(self, questions: List[Dict]) -> List[Dict]:
        """Match chemical images to questions using multiple strategies"""
        try:
            enhanced_questions = []
            
            # Create a mapping of available images
            available_images = list(self.extracted_images.keys())
            log_print(f"📊 [MATCH] Available images: {available_images}")
            
            for i, question in enumerate(questions):
                try:
                    enhanced_q = question.copy()
                    question_num = i + 1
                    
                    # Get question content
                    question_content = question.get('content', '') or question.get('question', '')
                    
                    # Strategy 1: Look for explicit image references in question text
                    image_refs = self._find_image_references_in_text(question_content)
                    
                    # Strategy 2: Match by question number
                    if not image_refs:
                        image_refs = self._find_images_by_question_number(question_num)
                    
                    # Strategy 3: Match by proximity (for chemical structures)
                    if not image_refs:
                        image_refs = self._find_images_by_proximity(question_num, len(questions))
                    
                    # Add matched images to question
                    if image_refs:
                        enhanced_q['imageUrl'] = {}
                        
                        for j, img_ref in enumerate(image_refs[:3]):  # Max 3 images per question
                            if img_ref in self.extracted_images:
                                img_key = f"chemical_img_{question_num}_{j+1}"
                                enhanced_q['imageUrl'][img_key] = self.extracted_images[img_ref]['data']
                                log_print(f"🧪 [MATCH] Added chemical image to question {question_num}: {img_ref}")
                    
                    # Also check options for chemical structures
                    enhanced_q = self._match_images_to_options(enhanced_q, question_num)
                    
                    enhanced_questions.append(enhanced_q)
                    
                except Exception as e:
                    log_print(f"⚠️ [MATCH] Error matching images for question {i+1}: {e}")
                    enhanced_questions.append(question)
            
            return enhanced_questions
            
        except Exception as e:
            log_print(f"❌ [MATCH] Image matching failed: {e}")
            return questions
    
    def _find_image_references_in_text(self, text: str) -> List[str]:
        """Find image references in question text"""
        try:
            image_refs = []
            
            # Look for various image reference patterns
            patterns = [
                r'!\[([^\]]*)\]\(([^)]+)\)',  # Markdown format: ![alt](img.jpg)
                r'img-(\d+)\.jpeg',          # Direct reference: img-5.jpeg
                r'img-(\d+)\.jpg',           # Direct reference: img-5.jpg
                r'img-(\d+)\.png',           # Direct reference: img-5.png
                r'image-(\d+)',              # Image reference: image-5
                r'figure\s*(\d+)',           # Figure reference: figure 5
                r'diagram\s*(\d+)',          # Diagram reference: diagram 5
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        # For patterns that capture groups
                        if len(match) == 2:  # Markdown format
                            img_ref = match[1]
                        else:  # Number patterns
                            img_ref = f"img-{match[0]}.jpeg"
                    else:
                        img_ref = match
                    
                    # Find matching image in extracted images
                    matching_img = self._find_matching_image_key(img_ref)
                    if matching_img:
                        image_refs.append(matching_img)
            
            return list(set(image_refs))  # Remove duplicates
            
        except Exception as e:
            log_print(f"⚠️ [REF] Error finding image references: {e}")
            return []
    
    def _find_images_by_question_number(self, question_num: int) -> List[str]:
        """Find images that likely belong to a specific question number"""
        try:
            matching_images = []
            
            # Look for images with question number in their ID
            for img_id in self.extracted_images.keys():
                # Extract numbers from image ID
                numbers = re.findall(r'\d+', img_id)
                for num_str in numbers:
                    if int(num_str) == question_num:
                        matching_images.append(img_id)
                        break
            
            return matching_images
            
        except Exception as e:
            log_print(f"⚠️ [NUM] Error finding images by number: {e}")
            return []
    
    def _find_images_by_proximity(self, question_num: int, total_questions: int) -> List[str]:
        """Find images by proximity to question number"""
        try:
            matching_images = []
            
            # For chemical PDFs, images are often sequential
            # Look for images with numbers close to the question number
            search_range = 2  # Look 2 numbers before and after
            
            for img_id in self.extracted_images.keys():
                numbers = re.findall(r'\d+', img_id)
                for num_str in numbers:
                    img_num = int(num_str)
                    if abs(img_num - question_num) <= search_range:
                        matching_images.append(img_id)
                        break
            
            # If no close matches, try page-based matching
            if not matching_images:
                estimated_images_per_page = max(1, len(self.extracted_images) // max(1, total_questions // 10))
                start_img = (question_num - 1) * estimated_images_per_page
                end_img = start_img + estimated_images_per_page
                
                img_list = list(self.extracted_images.keys())
                if start_img < len(img_list):
                    matching_images = img_list[start_img:min(end_img, len(img_list))]
            
            return matching_images[:2]  # Return max 2 images
            
        except Exception as e:
            log_print(f"⚠️ [PROX] Error finding images by proximity: {e}")
            return []
    
    def _find_matching_image_key(self, img_ref: str) -> Optional[str]:
        """Find the actual image key that matches a reference"""
        try:
            # Direct match
            if img_ref in self.extracted_images:
                return img_ref
            
            # Try variations
            variations = [
                img_ref,
                img_ref.replace('.jpeg', ''),
                img_ref.replace('.jpg', ''),
                img_ref.replace('.png', ''),
                f"img-{img_ref}",
                f"image-{img_ref}",
                f"mistral_{img_ref}",
                f"pymupdf_{img_ref}",
                f"adobe_{img_ref}"
            ]
            
            for variation in variations:
                for img_key in self.extracted_images.keys():
                    if variation.lower() in img_key.lower() or img_key.lower() in variation.lower():
                        return img_key
            
            return None
            
        except Exception as e:
            log_print(f"⚠️ [KEY] Error finding matching image key: {e}")
            return None
    
    def _match_images_to_options(self, question: Dict, question_num: int) -> Dict:
        """Match images to question options (for chemical structure options)"""
        try:
            if 'options' not in question or not isinstance(question['options'], dict):
                return question
            
            enhanced_options = {}
            
            for option_key, option_value in question['options'].items():
                if isinstance(option_value, str):
                    # Check if option contains image references
                    option_image_refs = self._find_image_references_in_text(option_value)
                    
                    if option_image_refs:
                        # Replace option with image
                        matching_img = option_image_refs[0]
                        if matching_img in self.extracted_images:
                            enhanced_options[option_key] = self.extracted_images[matching_img]['data']
                            log_print(f"🧪 [OPTION] Replaced option {option_key} in question {question_num} with chemical structure")
                        else:
                            enhanced_options[option_key] = option_value
                    else:
                        enhanced_options[option_key] = option_value
                else:
                    enhanced_options[option_key] = option_value
            
            question['options'] = enhanced_options
            return question
            
        except Exception as e:
            log_print(f"⚠️ [OPT] Error matching images to options: {e}")
            return question

    def _fix_image_references(self, questions: List[Dict]) -> List[Dict]:
        """Fix image references that show as ![img-X.jpeg](img-X.jpeg) instead of actual images"""
        try:
            fixed_questions = []

            for i, question in enumerate(questions):
                try:
                    fixed_q = question.copy()
                    question_num = i + 1

                    # Fix image references in question content
                    if 'content' in fixed_q:
                        fixed_q['content'] = self._replace_image_references_with_actual_images(
                            fixed_q['content'], question_num
                        )

                    if 'question' in fixed_q:
                        fixed_q['question'] = self._replace_image_references_with_actual_images(
                            fixed_q['question'], question_num
                        )

                    # Fix image references in options
                    if 'options' in fixed_q and isinstance(fixed_q['options'], dict):
                        fixed_options = {}
                        for opt_key, opt_value in fixed_q['options'].items():
                            if isinstance(opt_value, str):
                                fixed_options[opt_key] = self._replace_image_references_with_actual_images(
                                    opt_value, question_num, f"option_{opt_key}"
                                )
                            else:
                                fixed_options[opt_key] = opt_value
                        fixed_q['options'] = fixed_options

                    fixed_questions.append(fixed_q)

                except Exception as e:
                    log_print(f"⚠️ [FIX] Error fixing question {i+1}: {e}")
                    fixed_questions.append(question)

            return fixed_questions

        except Exception as e:
            log_print(f"❌ [FIX] Image reference fixing failed: {e}")
            return questions

    def _replace_image_references_with_actual_images(self, text: str, question_num: int, context: str = "question") -> str:
        """Replace markdown image references with actual image data"""
        try:
            if not text:
                return text

            # Pattern to match ![img-X.jpeg](img-X.jpeg) or similar
            markdown_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'

            def replace_with_image(match):
                alt_text = match.group(1)
                img_ref = match.group(2)

                # Find the actual image
                matching_img_key = self._find_matching_image_key(img_ref)

                if matching_img_key and matching_img_key in self.extracted_images:
                    # Add image to question's imageUrl if not already there
                    img_data = self.extracted_images[matching_img_key]['data']
                    log_print(f"🔧 [FIX] Replaced image reference {img_ref} with actual image in {context} of question {question_num}")

                    # Return a placeholder that will be handled by the frontend
                    return f"[CHEMICAL_IMAGE_{question_num}_{context}]"
                else:
                    log_print(f"⚠️ [FIX] Could not find image for reference {img_ref} in question {question_num}")
                    return f"[Missing Image: {img_ref}]"

            # Replace all markdown image references
            fixed_text = re.sub(markdown_pattern, replace_with_image, text)

            return fixed_text

        except Exception as e:
            log_print(f"⚠️ [REPLACE] Error replacing image references: {e}")
            return text

    def _validate_chemical_questions(self, questions: List[Dict]) -> List[Dict]:
        """Validate and improve chemical questions"""
        try:
            validated_questions = []

            for i, question in enumerate(questions):
                try:
                    # Basic validation
                    if not self._is_valid_chemical_question(question):
                        log_print(f"⚠️ [VALIDATE] Question {i+1} failed validation")
                        continue

                    # Enhance chemical question
                    enhanced_q = self._enhance_chemical_question(question, i+1)
                    validated_questions.append(enhanced_q)

                except Exception as e:
                    log_print(f"⚠️ [VALIDATE] Error validating question {i+1}: {e}")

            log_print(f"✅ [VALIDATE] Validated {len(validated_questions)} chemical questions")
            return validated_questions

        except Exception as e:
            log_print(f"❌ [VALIDATE] Validation failed: {e}")
            return questions

    def _is_valid_chemical_question(self, question: Dict) -> bool:
        """Check if chemical question is valid"""
        # Check content
        content = question.get('content', '') or question.get('question', '')
        if not content or len(content.strip()) < 10:
            return False

        # Check options
        options = question.get('options', {})
        if not options or len(options) < 2:
            return False

        # Check if at least 2 options have content
        valid_options = 0
        for opt in options.values():
            if opt and (isinstance(opt, str) and len(opt.strip()) > 0) or not isinstance(opt, str):
                valid_options += 1

        return valid_options >= 2

    def _enhance_chemical_question(self, question: Dict, question_num: int) -> Dict:
        """Enhance chemical question with proper formatting"""
        enhanced = question.copy()

        # Clean and format content
        content = question.get('content', '') or question.get('question', '')
        enhanced['content'] = self._clean_chemical_text(content)

        # Ensure proper structure
        if 'options' not in enhanced:
            enhanced['options'] = {}

        # Add metadata
        enhanced['id'] = f"chem_q_{question_num}"
        enhanced['type'] = 'mcq'
        enhanced['subject'] = 'chemistry'
        enhanced['extraction_method'] = 'chemical_specialized'

        return enhanced

    def _clean_chemical_text(self, text: str) -> str:
        """Clean chemical text while preserving chemical formulas"""
        if not text:
            return ''

        # Remove extra whitespace but preserve chemical formulas
        text = ' '.join(text.split())

        # Remove common OCR artifacts but preserve chemical symbols
        # Be careful not to remove chemical symbols like +, -, =, etc.
        import re

        # Remove only clearly problematic characters
        text = re.sub(r'[^\w\s\.\?\!\,\;\:\(\)\[\]\-\+\=\*\/\%\$\#\@\&\^\~\|\\]', '', text)

        return text.strip()

    def _parse_and_clean_questions(self, questions_json: str) -> List[Dict]:
        """Parse and clean malformed JSON from chemical extraction"""
        try:
            # Try to extract valid JSON objects
            import re

            # Find all JSON-like objects
            json_objects = re.findall(r'\{[^{}]*\}', questions_json)

            questions = []
            for obj_str in json_objects:
                try:
                    obj = json.loads(obj_str)
                    if isinstance(obj, dict) and ('content' in obj or 'question' in obj):
                        questions.append(obj)
                except:
                    continue

            return questions

        except Exception as e:
            log_print(f"❌ [PARSE] JSON parsing failed: {e}")
            return []

    def _fallback_extraction(self, pdf_path: str) -> Dict:
        """Fallback extraction method for chemical PDFs"""
        try:
            log_print("🔄 [FALLBACK] Using fallback extraction for chemical PDF...")

            questions_json = self.question_extractor.extract_questions_from_pdf_standard(pdf_path)
            questions = json.loads(questions_json) if questions_json else []

            return {
                'questions': json.dumps(questions, indent=2),
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'chemical_fallback',
                    'total_questions': len(questions),
                    'total_images': 0,
                    'questions_with_images': 0
                }
            }

        except Exception as e:
            log_print(f"❌ [FALLBACK] Fallback extraction failed: {e}")
            return {
                'questions': '[]',
                'extraction_metadata': {
                    'total_duration': 0,
                    'extraction_method': 'failed',
                    'total_questions': 0,
                    'total_images': 0,
                    'questions_with_images': 0,
                    'error': str(e)
                }
            }
