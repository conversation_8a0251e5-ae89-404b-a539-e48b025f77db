```json
[
  {
    "question": "For all positive integral values of $n, 3^{2 n}-2 n+1$ is divisible by",
    "options": {
      "A": "a) 2",
      "B": "b) 4",
      "C": "c) 8",
      "D": "d) 12"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Substitute n=2 into the expression $3^{2 n}-2 n+1$",
        "Step 2: Calculate $3^{2 \times 2}-2 \times 2+1 = 81 - 4 + 1 = 78$",
        "Step 3: Check divisibility of 78 by the given options"
      ],
      "methodology": "Direct substitution and verification",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "78 is divisible by 2, hence the correct answer is a) 2"
    },
    "hints": [
      "Hint 1: Start by testing small values of n to identify a pattern",
      "Hint 2: Check divisibility by the smallest option first"
    ]
  },
  {
    "question": "$3+13+29+51+79+\cdots$ to $n$ terms $=$",
    "options": {
      "A": "a) $2 n^{2}+7 n^{3}$",
      "B": "b) $n^{2}+5 n^{3}$",
      "C": "c) $n^{3}+2 n^{2}$",
      "D": "d) None of these"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Identify the pattern in the series: 3, 13, 29, 51, 79, ...",
        "Step 2: Recognize that the series can be written as $n^3 + 2n^2$",
        "Step 3: Verify by calculating the sum for n=1, n=2, and n=3"
      ],
      "methodology": "Pattern recognition and verification",
      "key_concepts": ["Series", "Pattern recognition"],
      "final_explanation": "The sum of the series matches the expression $n^3 + 2n^2$"
    },
    "hints": [
      "Hint 1: Look for a pattern in the differences between consecutive terms",
      "Hint 2: Consider the general form of the series and compare with given options"
    ]
  },
  {
    "question": "If $A=\\left[\\begin{array}{ll}1 & 0 \\ 1 & 1\\end{array}\\right]$ and $I=\\left[\\begin{array}{ll}1 & 0 \\ 0 & 1\\end{array}\\right]$, then which one of the following holds for all $n \\geq 1$, by the principle of mathematical induction?",
    "options": {
      "A": "a) $A^{n}=2^{n-1} A+(n-1) I$",
      "B": "b) $A^{n}=n A+(n-1) I$",
      "C": "c) $A^{n}=2^{n-1} A-(n-1) I$",
      "D": "d) $A^{n}=n A-(n-1) I$"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Compute $A^2$ and $A^3$ to identify a pattern",
        "Step 2: Verify the pattern for n=1, n=2, and n=3",
        "Step 3: Use mathematical induction to generalize the pattern"
      ],
      "methodology": "Pattern recognition and mathematical induction",
      "key_concepts": ["Matrix exponentiation", "Mathematical induction"],
      "final_explanation": "The pattern matches option d) $A^{n}=n A-(n-1) I$"
    },
    "hints": [
      "Hint 1: Start by computing the first few powers of A",
      "Hint 2: Look for a linear combination of A and I that matches the pattern"
    ]
  },
  {
    "question": "If $(n): 1+3+5+\\ldots+(2 n-1)=n^{2}$ is",
    "options": {
      "A": "a) True for all $n \\in N$",
      "B": "b) True for $n>1$",
      "C": "c) True for no $n$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Verify the statement for n=1: $1 = 1^2$",
        "Step 2: Assume the statement is true for n=k: $1+3+5+\\ldots+(2k-1)=k^2$",
        "Step 3: Prove the statement for n=k+1 using the induction hypothesis"
      ],
      "methodology": "Mathematical induction",
      "key_concepts": ["Sum of odd numbers", "Mathematical induction"],
      "final_explanation": "The statement is true for all positive integers n"
    },
    "hints": [
      "Hint 1: Start by verifying the base case n=1",
      "Hint 2: Use the induction hypothesis to prove the statement for n=k+1"
    ]
  },
  {
    "question": "For a positive integer $n$, Let $a(n)=1+\\frac{1}{2}+\\frac{1}{3}+\\frac{1}{4}+\\ldots+\\frac{1}{\\left(2^{n}\\right)-1}$. Then",
    "options": {
      "A": "a) $a(100) \\leq 100$",
      "B": "b) $a(100)>100$",
      "C": "c) $a(200) \\leq 100$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recognize that $a(n)$ is a partial sum of the harmonic series",
        "Step 2: Use the properties of the harmonic series to estimate $a(n)$",
        "Step 3: Compare the estimated value with the given options"
      ],
      "methodology": "Estimation and comparison",
      "key_concepts": ["Harmonic series", "Estimation"],
      "final_explanation": "The partial sum $a(100)$ is less than or equal to 100"
    },
    "hints": [
      "Hint 1: Recall that the harmonic series grows logarithmically",
      "Hint 2: Use known bounds for the harmonic series to estimate $a(n)$"
    ]
  },
  {
    "question": "If $A=\\left[\\begin{array}{rr}\\cos \\theta & \\sin \\theta \\\\ -\\sin \\theta & \\cos \\theta\\end{array}\\right]$, then for $n \\in N, A^{n}$ is equal to",
    "options": {
      "A": "a) $\\left[\\begin{array}{rr}\\cos ^{n} \\theta & \\sin ^{n} \\theta \\\\ -\\sin ^{n} \\theta & \\cos ^{n} \\theta\\end{array}\\right]$",
      "B": "b) $\\left[\\begin{array}{rr}\\cos n \\theta & \\sin n \\theta \\\\ -\\sin n \\theta & \\cos n \\theta\\end{array}\\right]$",
      "C": "c) $\\left[\\begin{array}{rr}n \\cos \\theta & n \\sin \\theta \\\\ -n \\sin \\theta & n \\cos \\theta\\end{array}\\right]$",
      "D": "d) None of the above"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Recognize that A is a rotation matrix",
        "Step 2: Compute $A^2$ to identify the pattern",
        "Step 3: Use mathematical induction to generalize the pattern"
      ],
      "methodology": "Pattern recognition and mathematical induction",
      "key_concepts": ["Rotation matrix", "Matrix exponentiation"],
      "final_explanation": "The pattern matches option b) $\\left[\\begin{array}{rr}\\cos n \\theta & \\sin n \\theta \\\\ -\\sin n \\theta & \\cos n \\theta\\end{array}\\right]$"
    },
    "hints": [
      "Hint 1: Start by computing the first few powers of A",
      "Hint 2: Look for a pattern in the angles and trigonometric functions"
    ]
  },
  {
    "question": "The sum of the cubes of three consecutive natural numbers is divisible by",
    "options": {
      "A": "a) 7",
      "B": "b) 9",
      "C": "c) 25",
      "D": "d) 26"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Let the three consecutive natural numbers be n, n+1, n+2",
        "Step 2: Compute the sum of their cubes: $n^3 + (n+1)^3 + (n+2)^3$",
        "Step 3: Simplify the expression and check divisibility by the given options"
      ],
      "methodology": "Algebraic manipulation and verification",
      "key_concepts": ["Sum of cubes", "Divisibility"],
      "final_explanation": "The sum of the cubes is divisible by 9"
    },
    "hints": [
      "Hint 1: Let the numbers be n, n+1, n+2",
      "Hint 2: Expand and simplify the sum of cubes"
    ]
  },
  {
    "question": "For all $n \\in N, \\sum n$",
    "options": {
      "A": "a) $<\\frac{(2 n+1)^{2}}{8}$",
      "B": "b) $>\\frac{(2 n+1)^{2}}{8}$",
      "C": "c) $=\\frac{(2 n+1)^{2}}{8}$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Interpret the sum $\\sum n$ as the sum of the first n natural numbers",
        "Step 2: Use the formula for the sum of the first n natural numbers: $\\frac{n(n+1)}{2}$",
        "Step 3: Compare the sum with $\\frac{(2n+1)^2}{8}$ for different values of n"
      ],
      "methodology": "Summation formula and comparison",
      "key_concepts": ["Sum of natural numbers", "Inequality"],
      "final_explanation": "The sum of the first n natural numbers is less than $\\frac{(2n+1)^2}{8}$ for all n"
    },
    "hints": [
      "Hint 1: Recall the formula for the sum of the first n natural numbers",
      "Hint 2: Compare the sum with the given expression for small values of n"
    ]
  },
  {
    "question": "The product of three consecutive natural numbers is divisible by",
    "options": {
      "A": "a) 5",
      "B": "b) 7",
      "C": "c) 6",
      "D": "d) 4"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Let the three consecutive natural numbers be n, n+1, n+2",
        "Step 2: Recognize that among any three consecutive natural numbers, there is a multiple of 2 and a multiple of 3",
        "Step 3: Conclude that the product is divisible by 6"
      ],
      "methodology": "Number theory",
      "key_concepts": ["Divisibility", "Consecutive numbers"],
      "final_explanation": "The product of three consecutive natural numbers is always divisible by 6"
    },
    "hints": [
      "Hint 1: Consider the properties of consecutive natural numbers",
      "Hint 2: Identify the presence of multiples of 2 and 3"
    ]
  },
  {
    "question": "For all $n \\in N, 5^{2 n}-1$ is divisible by",
    "options": {
      "A": "a) 6",
      "B": "b) 11",
      "C": "c) 24",
      "D": "d) 26"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Rewrite $5^{2n} - 1$ as $(5^2)^n - 1 = 25^n - 1$",
        "Step 2: Use the binomial expansion to express $25^n - 1$",
        "Step 3: Identify that the expansion includes a term divisible by 24"
      ],
      "methodology": "Binomial expansion and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "$5^{2n} - 1$ is divisible by 24 for all n"
    },
    "hints": [
      "Hint 1: Rewrite the expression using exponents",
      "Hint 2: Use the binomial theorem to expand the expression"
    ]
  },
  {
    "question": "$7^{2 n}+3^{n-1} \\cdot 2^{3 n-3}$ is divisible by",
    "options": {
      "A": "a) 24",
      "B": "b) 25",
      "C": "c) 9",
      "D": "d) 13"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Simplify the expression $7^{2n} + 3^{n-1} \\cdot 2^{3n-3}$",
        "Step 2: Factor out common terms",
        "Step 3: Check divisibility by the given options"
      ],
      "methodology": "Algebraic simplification and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "The expression is divisible by 25"
    },
    "hints": [
      "Hint 1: Simplify the expression by factoring out common terms",
      "Hint 2: Check divisibility for small values of n"
    ]
  },
  {
    "question": "For $n \\in N, 10^{n-2} \\geq 81 n$ is",
    "options": {
      "A": "a) $n>5$",
      "B": "b) $n \\geq 5$",
      "C": "c) $n<5$",
      "D": "d) $n>8$"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=4, n=5, and n=6",
        "Step 2: Verify that the inequality holds for n=5 and larger values",
        "Step 3: Conclude that the inequality is true for n \\geq 5"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Inequality", "Exponents"],
      "final_explanation": "The inequality $10^{n-2} \\geq 81n$ is true for n \\geq 5"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the smallest n for which the inequality holds"
    ]
  },
  {
    "question": "For all $n \\in N, \\frac{n^{5}}{5}+\\frac{n^{3}}{3}+\\frac{7}{15 n}$ is",
    "options": {
      "A": "a) An integer",
      "B": "b) A natural number",
      "C": "c) A positive fraction",
      "D": "d) None of these"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Combine the terms over a common denominator",
        "Step 2: Simplify the expression",
        "Step 3: Verify that the simplified expression is a natural number for all n"
      ],
      "methodology": "Algebraic simplification and verification",
      "key_concepts": ["Fractions", "Natural numbers"],
      "final_explanation": "The expression simplifies to a natural number for all n"
    },
    "hints": [
      "Hint 1: Combine the terms over a common denominator",
      "Hint 2: Simplify the expression and verify for small values of n"
    ]
  },
  {
    "question": "Let $S(k)=1+3+5 \\ldots+(2 k-1)=3+k^{2}$. Then, which of the following is true?",
    "options": {
      "A": "a) $S(1)$ is correct",
      "B": "b) $S(k) \\Rightarrow S(k+1)$",
      "C": "c) $S(k) \\nRightarrow S(k+1)$",
      "D": "d) Principle of mathematical induction can be used to prove the formula"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Verify the base case S(1) = 1 = 3 + 1^2 - 2 = 2 (not correct)",
        "Step 2: Assume S(k) is true and prove S(k+1) using the induction hypothesis",
        "Step 3: Conclude that S(k) implies S(k+1)"
      ],
      "methodology": "Mathematical induction",
      "key_concepts": ["Induction", "Sum of odd numbers"],
      "final_explanation": "The principle of mathematical induction can be used to prove the formula"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Use the induction hypothesis to prove the next case"
    ]
  },
  {
    "question": "The smallest positive integer $n$ for which $n!<\\left(\\frac{n+1}{2}\\right)^{n}$ holds, is",
    "options": {
      "A": "a) 1",
      "B": "b) 2",
      "C": "c) 3",
      "D": "d) 4"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=1, n=2, n=3, and n=4",
        "Step 2: Identify the smallest n for which the inequality holds",
        "Step 3: Conclude that n=2 is the smallest such integer"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Factorial", "Inequality"],
      "final_explanation": "The smallest positive integer n for which the inequality holds is 2"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the smallest n that satisfies the inequality"
    ]
  },
  {
    "question": "The remainder when $5^{99}$ is divided by 13 , is",
    "options": {
      "A": "a) 6",
      "B": "b) 8",
      "C": "c) 9",
      "D": "d) 10"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Use Fermat's Little Theorem to simplify $5^{99} \\mod 13$",
        "Step 2: Compute the remainder using properties of exponents and modular arithmetic",
        "Step 3: Conclude that the remainder is 8"
      ],
      "methodology": "Modular arithmetic and Fermat's Little Theorem",
      "key_concepts": ["Exponents", "Modular arithmetic"],
      "final_explanation": "The remainder when $5^{99}$ is divided by 13 is 8"
    },
    "hints": [
      "Hint 1: Use Fermat's Little Theorem to simplify the expression",
      "Hint 2: Compute the remainder using modular arithmetic"
    ]
  },
  {
    "question": "$10^{n}+3\\left(4^{n+2}\\right)+5$ is divisible by $(n \\in N)$",
    "options": {
      "A": "a) 7",
      "B": "b) 5",
      "C": "c) 9",
      "D": "d) 17"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $10 + 3(4^3) + 5 = 207$",
        "Step 2: Verify that 207 is divisible by 9",
        "Step 3: Conclude that the expression is divisible by 9 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression is divisible by 9 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "For all $n \\in N, n^{3}+2 n$ is divisible by",
    "options": {
      "A": "a) 3",
      "B": "b) 8",
      "C": "c) 9",
      "D": "d) 11"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Factor the expression $n^3 + 2n$ as $n(n^2 + 2)$",
        "Step 2: Recognize that for any integer n, $n^2 + 2$ is divisible by 3 when n is not divisible by 3",
        "Step 3: Conclude that the expression is divisible by 3 for all n"
      ],
      "methodology": "Factorization and divisibility",
      "key_concepts": ["Divisibility", "Factorization"],
      "final_explanation": "The expression $n^3 + 2n$ is divisible by 3 for all n"
    },
    "hints": [
      "Hint 1: Factor the expression",
      "Hint 2: Analyze the divisibility of the factors"
    ]
  },
  {
    "question": "For all $n \\in N, 7^{2 n}-48 n-1$ is divisible by",
    "options": {
      "A": "a) 25",
      "B": "b) 26",
      "C": "c) 1234",
      "D": "d) 2304"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Rewrite $7^{2n} - 48n - 1$ as $(1 + 48)^n - 48n - 1$",
        "Step 2: Use the binomial expansion to express the term",
        "Step 3: Identify that the expansion includes a term divisible by 2304"
      ],
      "methodology": "Binomial expansion and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "$7^{2n} - 48n - 1$ is divisible by 2304 for all n"
    },
    "hints": [
      "Hint 1: Rewrite the expression using exponents",
      "Hint 2: Use the binomial theorem to expand the expression"
    ]
  },
  {
    "question": "$10^{n}+3\\left(4^{n+2}\\right)+5$ is divisible by $(n \\in N)$",
    "options": {
      "A": "a) 7",
      "B": "b) 5",
      "C": "c) 9",
      "D": "d) 17"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $10 + 3(4^3) + 5 = 207$",
        "Step 2: Verify that 207 is divisible by 9",
        "Step 3: Conclude that the expression is divisible by 9 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression is divisible by 9 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "The $n$th term of the series $4+14+30+52+80+114+\\cdots$ is",
    "options": {
      "A": "a) $5 n-1$",
      "B": "b) $2 n^{2}+2 n$",
      "C": "c) $3 n^{2}+n$",
      "D": "d) $2 n^{2}+2$"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Identify the pattern in the series: 4, 14, 30, 52, 80, 114, ...",
        "Step 2: Recognize that the series can be written as $3n^2 + n$",
        "Step 3: Verify by calculating the nth term for n=1, n=2, and n=3"
      ],
      "methodology": "Pattern recognition and verification",
      "key_concepts": ["Series", "Pattern recognition"],
      "final_explanation": "The nth term of the series is $3n^2 + n$"
    },
    "hints": [
      "Hint 1: Look for a pattern in the differences between consecutive terms",
      "Hint 2: Consider the general form of the series and compare with given options"
    ]
  },
  {
    "question": "If $P(n)$ is a statement $(n \\in N)$ such that, if $P(k)$ is true, $P(k+1)$ is true for $k \\in N$, then $p(n)$ is true",
    "options": {
      "A": "a) For all $n$",
      "B": "b) For all $n>1$",
      "C": "c) For all $n>2$",
      "D": "d) Nothing can be said"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Understand the principle of mathematical induction",
        "Step 2: Recognize that the base case must also be true for the statement to hold for all n",
        "Step 3: Conclude that without the base case, nothing can be said about the truth of P(n)"
      ],
      "methodology": "Understanding of mathematical induction",
      "key_concepts": ["Mathematical induction", "Base case"],
      "final_explanation": "Without the base case, nothing can be said about the truth of P(n)"
    },
    "hints": [
      "Hint 1: Recall the principle of mathematical induction",
      "Hint 2: Understand the importance of the base case"
    ]
  },
  {
    "question": "Using mathematical induction, then numbers $a_{n}{ }^{\\prime} s$ are defined by $a_{0}=1, a_{n+1}=3 n^{2}+n+a_{n},(n \\geq 0)$ Then, $a_{n}$ is equal to",
    "options": {
      "A": "a) $n^{3}+n^{2}+1$",
      "B": "b) $n^{3}-n^{2}+1$",
      "C": "c) $n^{3}-n^{2}$",
      "D": "d) $n^{3}+n^{2}$"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Verify the base case a0 = 1",
        "Step 2: Compute a1, a2, and a3 using the recurrence relation",
        "Step 3: Compare the computed values with the given options"
      ],
      "methodology": "Recurrence relation and verification",
      "key_concepts": ["Recurrence relation", "Mathematical induction"],
      "final_explanation": "The correct expression for $a_n$ is $n^3 - n^2 + 1$"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Compute the first few terms using the recurrence relation"
    ]
  },
  {
    "question": "$\\frac{(n+2)!}{(n-1)!}$ is divisible by",
    "options": {
      "A": "a) 6",
      "B": "b) 11",
      "C": "c) 24",
      "D": "d) 26"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Simplify the expression $\\frac{(n+2)!}{(n-1)!}$",
        "Step 2: Recognize that the simplified expression is $(n+2)(n+1)n$",
        "Step 3: Conclude that the product of three consecutive integers is divisible by 6"
      ],
      "methodology": "Simplification and divisibility",
      "key_concepts": ["Factorial", "Divisibility"],
      "final_explanation": "The expression is divisible by 6"
    },
    "hints": [
      "Hint 1: Simplify the expression using factorial properties",
      "Hint 2: Recognize the product of three consecutive integers"
    ]
  },
  {
    "question": "If $P(n)=2+4+6+\\ldots+2 n, n \\in N$, then $P(k)=k(k+1)+2 \\Rightarrow P(k+1)=(k+1)(k+2)+2$ for all $k \\in N$. So, we can conclude that $P(n)=n(n+1)+2$ for",
    "options": {
      "A": "a) All $n \\in N$",
      "B": "b) $n>1$",
      "C": "c) $n>2$",
      "D": "d) Nothing can be said"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Verify the base case P(1) = 2",
        "Step 2: Recognize that P(1) does not satisfy the given formula P(n) = n(n+1) + 2",
        "Step 3: Conclude that the formula does not hold for n=1"
      ],
      "methodology": "Verification and analysis",
      "key_concepts": ["Mathematical induction", "Base case"],
      "final_explanation": "The formula does not hold for n=1, so nothing can be said about its validity for all n"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Check the validity of the formula for n=1"
    ]
  },
  {
    "question": "For all $n \\in N, 2 \\cdot 4^{2 n+1}+3^{3 n+1}$ is divisible by",
    "options": {
      "A": "a) 2",
      "B": "b) 9",
      "C": "c) 3",
      "D": "d) 11"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $2 \\cdot 4^{3} + 3^{4} = 128 + 81 = 209$",
        "Step 2: Verify that 209 is divisible by 11",
        "Step 3: Conclude that the expression is divisible by 11 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression is divisible by 11 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "For all $n \\in N, n^{4}$ is less than",
    "options": {
      "A": "a) $10^{n}$",
      "B": "b) $4^{n}$",
      "C": "c) $5^{n}$",
      "D": "d) $10^{10}$"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=1, n=2, n=3, and n=4",
        "Step 2: Verify that $n^4 < 10^n$ for these values",
        "Step 3: Conclude that the inequality holds for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Inequality", "Exponents"],
      "final_explanation": "The inequality $n^4 < 10^n$ holds for all n"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the pattern in the inequality"
    ]
  },
  {
    "question": "The number $a^{n}-b^{n}\\left(a, b\\right.$ are distinct rational numbers and $n \\in N$ ) is always divisible by",
    "options": {
      "A": "a) $a-b$",
      "B": "b) $a+b$",
      "C": "c) $2 a-b$",
      "D": "d) $a-2 b$"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recognize that $a^n - b^n$ can be factored as $(a - b)(a^{n-1} + a^{n-2}b + \\ldots + b^{n-1})$",
        "Step 2: Conclude that the expression is divisible by $a - b$",
        "Step 3: Verify for specific values of a, b, and n"
      ],
      "methodology": "Factorization and verification",
      "key_concepts": ["Factorization", "Divisibility"],
      "final_explanation": "The expression $a^n - b^n$ is always divisible by $a - b$"
    },
    "hints": [
      "Hint 1: Recall the factorization of $a^n - b^n$",
      "Hint 2: Verify the divisibility for specific values"
    ]
  },
  {
    "question": "If $n \\in N$, then $3^{2 n}+7$ is divisible by",
    "options": {
      "A": "a) 3",
      "B": "b) 8",
      "C": "c) 9",
      "D": "d) 11"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1, n=2, and n=3",
        "Step 2: Verify that $3^{2n} + 7$ is divisible by 8 for these values",
        "Step 3: Conclude that the expression is divisible by 8 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $3^{2n} + 7$ is divisible by 8 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Identify the pattern in the divisibility"
    ]
  },
  {
    "question": "For each, $n \\in N, 10^{2 n-1}+1$ is divisible by",
    "options": {
      "A": "a) 11",
      "B": "b) 13",
      "C": "c) 9",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $10^{1} + 1 = 11$",
        "Step 2: Verify that 11 is divisible by 11",
        "Step 3: Use mathematical induction to prove the statement for all n"
      ],
      "methodology": "Verification and mathematical induction",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $10^{2n-1} + 1$ is divisible by 11 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Use mathematical induction to generalize the result"
    ]
  },
  {
    "question": "If $10^{n}+3.4^{n+2}+\\lambda$ is exactly divisible by 9 for all $n \\in N$, then the least positive integral value of $\\lambda$ is",
    "options": {
      "A": "a) 5",
      "B": "b) 3",
      "C": "c) 7",
      "D": "d) 1"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $10 + 3 \\cdot 4^3 + \\lambda = 207 + \\lambda$",
        "Step 2: Find the smallest positive integer \\lambda such that 207 + \\lambda is divisible by 9",
        "Step 3: Conclude that \\lambda = 5 satisfies the condition"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The least positive integral value of \\lambda that satisfies the condition is 5"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Find the smallest \\lambda that makes the expression divisible by 9"
    ]
  },
  {
    "question": "For all $n \\in N, \\cos \\theta \\cos 2 \\theta \\cos 4 \\theta \\ldots \\cos 2^{n-1} \\theta$ equals to",
    "options": {
      "A": "a) $\\frac{\\sin 2^{n} \\theta}{2^{n} \\sin \\theta}$",
      "B": "b) $\\frac{\\sin 2^{n} \\theta}{\\sin \\theta}$",
      "C": "c) $\\frac{\\cos 2^{n} \\theta}{2^{n} \\cos 2 \\theta}$",
      "D": "d) $\\frac{\\cos 2^{n} \\theta}{2^{n} \\sin \\theta}$"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recognize the pattern in the product of cosines",
        "Step 2: Use trigonometric identities to simplify the product",
        "Step 3: Conclude that the simplified form matches option a)"
      ],
      "methodology": "Trigonometric identities and simplification",
      "key_concepts": ["Trigonometry", "Product of cosines"],
      "final_explanation": "The product of cosines simplifies to $\\frac{\\sin 2^n \\theta}{2^n \\sin \\theta}$"
    },
    "hints": [
      "Hint 1: Recognize the pattern in the product",
      "Hint 2: Use trigonometric identities to simplify the expression"
    ]
  },
  {
    "question": "The inequality $n!>2^{n-1}$ is true for",
    "options": {
      "A": "a) $n>2$",
      "B": "b) $n \\in N$",
      "C": "c) $n>3$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=1, n=2, n=3, and n=4",
        "Step 2: Verify that the inequality holds for n=3 and larger values",
        "Step 3: Conclude that the inequality is true for n>2"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Factorial", "Inequality"],
      "final_explanation": "The inequality $n! > 2^{n-1}$ is true for n>2"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the smallest n for which the inequality holds"
    ]
  },
  {
    "question": "If $P(n): 2+4+6 \\ldots+(2 n), n \\in N$, then $P(k)=k(k+1)+2$ implies $P(k)=(k+1)(k+2)+2$ is true for all $k \\in N$. So, statement $P(n)=n(n+1)+2$ is true for",
    "options": {
      "A": "a) $n \\geq 1$",
      "B": "b) $n \\geq 2$",
      "C": "c) $n \\geq 3$",
      "D": "d) None of these"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Verify the base case P(1) = 2",
        "Step 2: Recognize that P(1) does not satisfy the given formula P(n) = n(n+1) + 2",
        "Step 3: Conclude that the formula does not hold for n=1"
      ],
      "methodology": "Verification and analysis",
      "key_concepts": ["Mathematical induction", "Base case"],
      "final_explanation": "The formula does not hold for n=1, so nothing can be said about its validity for all n"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Check the validity of the formula for n=1"
    ]
  },
  {
    "question": "If $P(n): 3^{n}<n!, n \\in N$, then $P(n)$ is true",
    "options": {
      "A": "a) For $n \\geq 6$",
      "B": "b) For $n \\geq 7$",
      "C": "c) For $n \\geq 3$",
      "D": "d) For all $n$"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=1, n=2, n=3, n=4, n=5, n=6, and n=7",
        "Step 2: Verify that the inequality holds for n=7 and larger values",
        "Step 3: Conclude that the inequality is true for n \\geq 7"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Factorial", "Inequality"],
      "final_explanation": "The inequality $3^n < n!$ is true for n \\geq 7"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the smallest n for which the inequality holds"
    ]
  },
  {
    "question": "Let $P(n)$ denotes the statement that $n^{2}+n$ is odd. It is seen that $P(n) \\Rightarrow P(n+1), P(n)$ is true for all",
    "options": {
      "A": "a) $n>1$",
      "B": "b) $n$",
      "C": "c) $n>2$",
      "D": "d) None of these"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Analyze the statement $n^2 + n$ is odd",
        "Step 2: Recognize that $n^2 + n$ is always even for any integer n",
        "Step 3: Conclude that the statement is never true for any n"
      ],
      "methodology": "Analysis and verification",
      "key_concepts": ["Parity", "Even and odd numbers"],
      "final_explanation": "The statement $n^2 + n$ is odd is never true for any n"
    },
    "hints": [
      "Hint 1: Analyze the parity of $n^2 + n$",
      "Hint 2: Recognize that the sum of two odd or two even numbers is even"
    ]
  },
  {
    "question": "The sum to $n$ terms of the series $1^{3}+3^{3}+5^{3}+\\cdots$ is",
    "options": {
      "A": "a) $n^{2}\\left(n^{2}-1\\right)$",
      "B": "b) $n^{2}\\left(2 n^{2}-1\\right)$",
      "C": "c) $n^{2}\\left(2 n^{2}+1\\right)$",
      "D": "d) $n^{2}\\left(n^{2}+1\\right)$"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Recognize the series as the sum of cubes of odd numbers",
        "Step 2: Use the formula for the sum of cubes of the first n odd numbers",
        "Step 3: Conclude that the sum matches option b)"
      ],
      "methodology": "Series summation and formula application",
      "key_concepts": ["Sum of cubes", "Odd numbers"],
      "final_explanation": "The sum of the series is $n^2(2n^2 - 1)$"
    },
    "hints": [
      "Hint 1: Recognize the series as the sum of cubes of odd numbers",
      "Hint 2: Use the known formula for the sum of cubes of the first n odd numbers"
    ]
  },
  {
    "question": "If $n \\in N$, then $11^{n+2}+12^{2 n+1}$ is divisible by",
    "options": {
      "A": "a) 113",
      "B": "b) 123",
      "C": "c) 133",
      "D": "d) None of these"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $11^3 + 12^3 = 1331 + 1728 = 3059$",
        "Step 2: Verify that 3059 is divisible by 133",
        "Step 3: Conclude that the expression is divisible by 133 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $11^{n+2} + 12^{2n+1}$ is divisible by 133 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "For natural number $n, 2^{n}(n-1)!<n^{n}$, if",
    "options": {
      "A": "a) $n<2$",
      "B": "b) $n>2$",
      "C": "c) $n \\geq 2$",
      "D": "d) never"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Test the inequality for n=1, n=2, n=3, and n=4",
        "Step 2: Verify that the inequality holds for n=3 and larger values",
        "Step 3: Conclude that the inequality is true for n>2"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Factorial", "Inequality"],
      "final_explanation": "The inequality $2^n(n-1)! < n^n$ is true for n>2"
    },
    "hints": [
      "Hint 1: Test the inequality for small values of n",
      "Hint 2: Identify the smallest n for which the inequality holds"
    ]
  },
  {
    "question": "If $n \\in N$, then $n\\left(n^{2}-1\\right)$ is divisible by",
    "options": {
      "A": "a) 6",
      "B": "b) 16",
      "C": "c) 36",
      "D": "d) 24"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Factor the expression $n(n^2 - 1)$ as $n(n-1)(n+1)$",
        "Step 2: Recognize that the product of three consecutive integers is divisible by 6",
        "Step 3: Conclude that the expression is divisible by 6"
      ],
      "methodology": "Factorization and divisibility",
      "key_concepts": ["Divisibility", "Consecutive integers"],
      "final_explanation": "The expression $n(n^2 - 1)$ is divisible by 6 for all n"
    },
    "hints": [
      "Hint 1: Factor the expression",
      "Hint 2: Recognize the product of three consecutive integers"
    ]
  },
  {
    "question": "$\\left(2^{3 n}-1\\right)$ will be divisible by $(\\forall n \\in N)$",
    "options": {
      "A": "a) 25",
      "B": "b) 8",
      "C": "c) 7",
      "D": "d) 3"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Rewrite $2^{3n} - 1$ as $(2^3)^n - 1 = 8^n - 1$",
        "Step 2: Use the binomial expansion to express $8^n - 1$",
        "Step 3: Identify that the expansion includes a term divisible by 7"
      ],
      "methodology": "Binomial expansion and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "$2^{3n} - 1$ is divisible by 7 for all n"
    },
    "hints": [
      "Hint 1: Rewrite the expression using exponents",
      "Hint 2: Use the binomial theorem to expand the expression"
    ]
  },
  {
    "question": "If $n \\in N$, then $x^{2 n-1} y^{2 n-1}$ is divisible by",
    "options": {
      "A": "a) $x+y$",
      "B": "b) $x-y$",
      "C": "c) $x^{2}+y^{2}$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recognize that $x^{2n-1} y^{2n-1} = (xy)^{2n-1}$",
        "Step 2: Use the property that $a^{2n-1} + b^{2n-1}$ is divisible by $a + b$",
        "Step 3: Conclude that $(xy)^{2n-1}$ is divisible by $x + y$"
      ],
      "methodology": "Algebraic properties and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "The expression $x^{2n-1} y^{2n-1}$ is divisible by $x + y$ for all n"
    },
    "hints": [
      "Hint 1: Rewrite the expression using exponents",
      "Hint 2: Use the property of divisibility for odd exponents"
    ]
  },
  {
    "question": "If $x^{2 n-1}+y^{2 n-1}$ is divisible by $x+y$, if $n$ is",
    "options": {
      "A": "a) A positive integer",
      "B": "b) An even positive integer",
      "C": "c) An odd positive integer",
      "D": "d) None of the above"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recall the property that $x^{2n-1} + y^{2n-1}$ is divisible by $x + y$ for any positive integer n",
        "Step 2: Verify the property for n=1, n=2, and n=3",
        "Step 3: Conclude that the property holds for any positive integer n"
      ],
      "methodology": "Verification and algebraic properties",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $x^{2n-1} + y^{2n-1}$ is divisible by $x + y$ for any positive integer n"
    },
    "hints": [
      "Hint 1: Recall the property of divisibility for odd exponents",
      "Hint 2: Verify the property for small values of n"
    ]
  },
  {
    "question": "If $m, n$ are any two odd positive integer with $n<m$, then the largest positive integers which divides all the numbers of the type $m^{2}-n^{2}$ is",
    "options": {
      "A": "a) 4",
      "B": "b) 6",
      "C": "c) 8",
      "D": "d) 9"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Let m=2k+1 and n=2k-1 for some integer k",
        "Step 2: Compute $m^2 - n^2 = (2k+1)^2 - (2k-1)^2 = 8k$",
        "Step 3: Conclude that the largest integer dividing all such numbers is 8"
      ],
      "methodology": "Algebraic manipulation and verification",
      "key_concepts": ["Divisibility", "Odd numbers"],
      "final_explanation": "The largest positive integer that divides all numbers of the form $m^2 - n^2$ is 8"
    },
    "hints": [
      "Hint 1: Let m=2k+1 and n=2k-1",
      "Hint 2: Compute the difference of squares"
    ]
  },
  {
    "question": "If $x^{n}-1$ is divisible by $x-k$, then the least positive integral value of $k$ is",
    "options": {
      "A": "a) 1",
      "B": "b) 2",
      "C": "c) 3",
      "D": "d) 4"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recognize that $x^n - 1$ is divisible by $x - k$ if and only if $k^n = 1$",
        "Step 2: Identify the smallest positive integer k such that $k^n = 1$ for some n",
        "Step 3: Conclude that k=1 is the smallest such integer"
      ],
      "methodology": "Algebraic properties and verification",
      "key_concepts": ["Divisibility", "Roots of unity"],
      "final_explanation": "The least positive integral value of k is 1"
    },
    "hints": [
      "Hint 1: Recall the condition for divisibility",
      "Hint 2: Identify the smallest k that satisfies the condition"
    ]
  },
  {
    "question": "If $n$ is a positive integer, then $5^{2 n+2}-24 n-25$ is divisible by",
    "options": {
      "A": "a) 574",
      "B": "b) 575",
      "C": "c) 675",
      "D": "d) 576"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $5^4 - 24 - 25 = 625 - 24 - 25 = 576$",
        "Step 2: Verify that 576 is divisible by 576",
        "Step 3: Conclude that the expression is divisible by 576 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $5^{2n+2} - 24n - 25$ is divisible by 576 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "For all $n \\in N, 3^{3 n}-26^{n}-1$ is divisible by",
    "options": {
      "A": "a) 24",
      "B": "b) 64",
      "C": "c) 17",
      "D": "d) 676"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Rewrite $3^{3n} - 26^n - 1$ as $(27)^n - 26^n - 1$",
        "Step 2: Use the binomial expansion to express the term",
        "Step 3: Identify that the expansion includes a term divisible by 676"
      ],
      "methodology": "Binomial expansion and divisibility",
      "key_concepts": ["Exponents", "Divisibility"],
      "final_explanation": "$3^{3n} - 26^n - 1$ is divisible by 676 for all n"
    },
    "hints": [
      "Hint 1: Rewrite the expression using exponents",
      "Hint 2: Use the binomial theorem to expand the expression"
    ]
  },
  {
    "question": "Matrix $A$ is such that $A^{2}=2 A-I$ where $I$ is the identity matrix, then for $n \\geq 2, A^{n}$ is equal to",
    "options": {
      "A": "a) $n A-(n-1) I$",
      "B": "b) $n A-I$",
      "C": "c) $2^{n-1} A-(n-1) I$",
      "D": "d) $2^{n-1} A-I$"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Compute $A^2 = 2A - I$",
        "Step 2: Compute $A^3 = 3A - 2I$",
        "Step 3: Identify the pattern and generalize for $A^n$",
        "Step 4: Conclude that $A^n = nA - (n-1)I$"
      ],
      "methodology": "Pattern recognition and mathematical induction",
      "key_concepts": ["Matrix exponentiation", "Recurrence relation"],
      "final_explanation": "The pattern matches option a) $nA - (n-1)I$"
    },
    "hints": [
      "Hint 1: Compute the first few powers of A",
      "Hint 2: Identify the pattern in the coefficients"
    ]
  },
  {
    "question": "If $a_{1}=1$ and $a_{n}=n a_{n-1}$ for all positive integer $n \\geq 2$, then $a_{5}$ is equal to",
    "options": {
      "A": "a) 125",
      "B": "b) 120",
      "C": "c) 100",
      "D": "d) 24"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Compute $a_1 = 1$",
        "Step 2: Compute $a_2 = 2a_1 = 2$",
        "Step 3: Compute $a_3 = 3a_2 = 6$",
        "Step 4: Compute $a_4 = 4a_3 = 24$",
        "Step 5: Compute $a_5 = 5a_4 = 120$"
      ],
      "methodology": "Recurrence relation and computation",
      "key_concepts": ["Recurrence relation", "Factorial"],
      "final_explanation": "The value of $a_5$ is 120"
    },
    "hints": [
      "Hint 1: Compute the first few terms using the recurrence relation",
      "Hint 2: Recognize the pattern in the terms"
    ]
  },
  {
    "question": "For all $n \\in N, 1+\\frac{1}{\\sqrt{2}}+\\frac{1}{\\sqrt{3}}+\\cdots+\\frac{1}{\\sqrt{n}}$ is",
    "options": {
      "A": "a) Equal to $\\sqrt{n}$",
      "B": "b) Less than or equal to $\\sqrt{n}$",
      "C": "c) Greater than or equal to $\\sqrt{n}$",
      "D": "d) None of these"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Recognize the sum as a partial sum of the series of reciprocals of square roots",
        "Step 2: Use integral comparison to estimate the sum",
        "Step 3: Conclude that the sum is less than or equal to $\\sqrt{n}$"
      ],
      "methodology": "Estimation and comparison",
      "key_concepts": ["Series", "Integral comparison"],
      "final_explanation": "The sum is less than or equal to $\\sqrt{n}$ for all n"
    },
    "hints": [
      "Hint 1: Recognize the series as a partial sum of reciprocals of square roots",
      "Hint 2: Use integral comparison to estimate the sum"
    ]
  },
  {
    "question": "Let $P(n): n^{2}+n+1$ is an even integer. If $P(k)$ is assumed true $\\Rightarrow P(k+1)$ is true. Therefore, $P(n)$ is true",
    "options": {
      "A": "a) For $n>1$",
      "B": "b) For all $n \\in N$",
      "C": "c) For $n>2$",
      "D": "d) None of these"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Verify the base case P(1) = 1 + 1 + 1 = 3 (odd)",
        "Step 2: Recognize that P(1) is not true",
        "Step 3: Conclude that the statement cannot be proven using mathematical induction"
      ],
      "methodology": "Verification and analysis",
      "key_concepts": ["Mathematical induction", "Base case"],
      "final_explanation": "The statement cannot be proven using mathematical induction because the base case is not true"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Check the validity of the base case"
    ]
  },
  {
    "question": "$2^{3 n}-7 n-1$ is divisible by",
    "options": {
      "A": "a) 64",
      "B": "b) 36",
      "C": "c) 49",
      "D": "d) 25"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $2^3 - 7 - 1 = 0$",
        "Step 2: Test the expression for n=2: $2^6 - 14 - 1 = 49$",
        "Step 3: Verify that 49 is divisible by 49",
        "Step 4: Use mathematical induction to prove the statement for all n"
      ],
      "methodology": "Verification and mathematical induction",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $2^{3n} - 7n - 1$ is divisible by 49 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Use mathematical induction to generalize the result"
    ]
  },
  {
    "question": "For all $n \\in N, 3 n^{5}+5 n^{3}+7 n$ is divisible by",
    "options": {
      "A": "a) 3",
      "B": "b) 5",
      "C": "c) 10",
      "D": "d) 15"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Factor the expression $3n^5 + 5n^3 + 7n$ as $n(3n^4 + 5n^2 + 7)$",
        "Step 2: Verify that the expression is divisible by 15 for n=1, n=2, and n=3",
        "Step 3: Conclude that the expression is divisible by 15 for all n"
      ],
      "methodology": "Factorization and verification",
      "key_concepts": ["Divisibility", "Factorization"],
      "final_explanation": "The expression $3n^5 + 5n^3 + 7n$ is divisible by 15 for all n"
    },
    "hints": [
      "Hint 1: Factor the expression",
      "Hint 2: Verify divisibility for small values of n"
    ]
  },
  {
    "question": "If $n$ is a positive integer, then $n^{3}+2 n$ is divisible by",
    "options": {
      "A": "a) 2",
      "B": "b) 6",
      "C": "c) 15",
      "D": "d) 3"
    },
    "answer": "D",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $1 + 2 = 3$",
        "Step 2: Test the expression for n=2: $8 + 4 = 12$",
        "Step 3: Test the expression for n=3: $27 + 6 = 33$",
        "Step 4: Verify that all results are divisible by 3",
        "Step 5: Conclude that the expression is divisible by 3 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $n^3 + 2n$ is divisible by 3 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "For all $n \\in N, 49^{n}+16 n-1$ is divisible by",
    "options": {
      "A": "a) 64",
      "B": "b) 8",
      "C": "c) 16",
      "D": "d) 4"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $49 + 16 - 1 = 64$",
        "Step 2: Verify that 64 is divisible by 64",
        "Step 3: Conclude that the expression is divisible by 64 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $49^n + 16n - 1$ is divisible by 64 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  },
  {
    "question": "If $P(n)$ is a statement such that $P(3)$ is true. Assuming $P(k)$ is true $\\Rightarrow P(k+1)$ is true for all $k \\geq 3$, then $P(n)$ is true",
    "options": {
      "A": "a) For all $n$",
      "B": "b) For $n \\geq 3$",
      "C": "c) For $n>4$",
      "D": "d) None of these"
    },
    "answer": "B",
    "solution": {
      "steps": [
        "Step 1: Understand the principle of mathematical induction",
        "Step 2: Recognize that if P(3) is true and P(k) implies P(k+1) for all k \\geq 3, then P(n) is true for all n \\geq 3",
        "Step 3: Conclude that the statement is true for all n \\geq 3"
      ],
      "methodology": "Understanding of mathematical induction",
      "key_concepts": ["Mathematical induction", "Base case"],
      "final_explanation": "The statement P(n) is true for all n \\geq 3"
    },
    "hints": [
      "Hint 1: Recall the principle of mathematical induction",
      "Hint 2: Understand the importance of the base case and the induction step"
    ]
  },
  {
    "question": "If $n$ is an odd positive integer, then $a^{n}+b^{n}$ is divisible by",
    "options": {
      "A": "a) $a+b$",
      "B": "b) $a-b$",
      "C": "c) $a^{2}+b^{2}$",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recall the property that $a^n + b^n$ is divisible by $a + b$ when n is odd",
        "Step 2: Verify the property for n=1, n=3, and n=5",
        "Step 3: Conclude that the property holds for all odd positive integers n"
      ],
      "methodology": "Verification and algebraic properties",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $a^n + b^n$ is divisible by $a + b$ for all odd positive integers n"
    },
    "hints": [
      "Hint 1: Recall the property of divisibility for odd exponents",
      "Hint 2: Verify the property for small values of n"
    ]
  },
  {
    "question": "The $n$th terms of the series $3+7+13+21+\\cdots$ is",
    "options": {
      "A": "a) $4 n-1$",
      "B": "b) $n^{2}+2 n$",
      "C": "c) $n^{2}+n+1$",
      "D": "d) $n^{2}+2$"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Identify the pattern in the series: 3, 7, 13, 21, ...",
        "Step 2: Recognize that the series can be written as $n^2 + n + 1$",
        "Step 3: Verify by calculating the nth term for n=1, n=2, and n=3"
      ],
      "methodology": "Pattern recognition and verification",
      "key_concepts": ["Series", "Pattern recognition"],
      "final_explanation": "The nth term of the series is $n^2 + n + 1$"
    },
    "hints": [
      "Hint 1: Look for a pattern in the differences between consecutive terms",
      "Hint 2: Consider the general form of the series and compare with given options"
    ]
  },
  {
    "question": "If $a, b$ are distinct rational numbers, then for all $n \\in N$ the number $a^{n}-b^{n}$ is divisible by",
    "options": {
      "A": "a) $a-b$",
      "B": "b) $a+b$",
      "C": "c) $2 a-b$",
      "D": "d) $a-2 b$"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Recall the property that $a^n - b^n$ is divisible by $a - b$ for any positive integer n",
        "Step 2: Verify the property for n=1, n=2, and n=3",
        "Step 3: Conclude that the property holds for all positive integers n"
      ],
      "methodology": "Verification and algebraic properties",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $a^n - b^n$ is divisible by $a - b$ for all positive integers n"
    },
    "hints": [
      "Hint 1: Recall the property of divisibility for any exponents",
      "Hint 2: Verify the property for small values of n"
    ]
  },
  {
    "question": "$x\\left(x^{n-1}-n \\alpha^{n-1}\\right)+\\alpha^{n}(n-1)$ is divisible by $(x-\\alpha)^{2}$ for",
    "options": {
      "A": "a) $n>1$",
      "B": "b) $n>2$",
      "C": "c) All $n \\in N$",
      "D": "d) None of these"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Verify the base case n=1: $x(1 - \\alpha) + \\alpha(0) = x - \\alpha$",
        "Step 2: Assume the statement is true for n=k and prove for n=k+1 using the induction hypothesis",
        "Step 3: Conclude that the statement is true for all n \\in N"
      ],
      "methodology": "Mathematical induction",
      "key_concepts": ["Divisibility", "Polynomials"],
      "final_explanation": "The expression is divisible by $(x - \\alpha)^2$ for all n \\in N"
    },
    "hints": [
      "Hint 1: Verify the base case",
      "Hint 2: Use the induction hypothesis to prove the next case"
    ]
  },
  {
    "question": "$2^{3 n}-7 n-1$ is divisible by",
    "options": {
      "A": "a) 64",
      "B": "b) 36",
      "C": "c) 49",
      "D": "d) 25"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $2^3 - 7 - 1 = 0$",
        "Step 2: Test the expression for n=2: $2^6 - 14 - 1 = 49$",
        "Step 3: Verify that 49 is divisible by 49",
        "Step 4: Use mathematical induction to prove the statement for all n"
      ],
      "methodology": "Verification and mathematical induction",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $2^{3n} - 7n - 1$ is divisible by 49 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Use mathematical induction to generalize the result"
    ]
  },
  {
    "question": "For all $n \\in N, 3^{2 n}-1$ is divisible by",
    "options": {
      "A": "a) 8",
      "B": "b) 16",
      "C": "c) 32",
      "D": "d) None of these"
    },
    "answer": "A",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $3^2 - 1 = 8$",
        "Step 2: Verify that 8 is divisible by 8",
        "Step 3: Use mathematical induction to prove the statement for all n"
      ],
      "methodology": "Verification and mathematical induction",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $3^{2n} - 1$ is divisible by 8 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Use mathematical induction to generalize the result"
    ]
  },
  {
    "question": "For all $n \\in N, 4^{n}-3 n-1$ is divisible by",
    "options": {
      "A": "a) 3",
      "B": "b) 8",
      "C": "c) 9",
      "D": "d) 11"
    },
    "answer": "C",
    "solution": {
      "steps": [
        "Step 1: Test the expression for n=1: $4 - 3 - 1 = 0$",
        "Step 2: Test the expression for n=2: $16 - 6 - 1 = 9$",
        "Step 3: Verify that 9 is divisible by 9",
        "Step 4: Conclude that the expression is divisible by 9 for all n"
      ],
      "methodology": "Verification and testing",
      "key_concepts": ["Divisibility", "Exponents"],
      "final_explanation": "The expression $4^n - 3n - 1$ is divisible by 9 for all n"
    },
    "hints": [
      "Hint 1: Test the expression for small values of n",
      "Hint 2: Verify divisibility for the tested values"
    ]
  }
]
```