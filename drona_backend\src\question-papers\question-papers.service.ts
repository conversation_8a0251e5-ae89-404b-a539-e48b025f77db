import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Document, Types } from 'mongoose';
import { QuestionPaper } from '../schema/question-paper.schema';
import { Question } from '../schema/question.schema';
import { Subject } from '../schema/subject.schema';
import { College } from '../schema/college.schema';
import { TrackingService } from '../common/services/tracking.service';
import { QuestionUsageService } from '../question-usage/question-usage.service';
import {
  CreateQuestionPaperDto,
  SubjectShortCode,
  ExamType,
} from './dto/create-question-paper.dto';

import { SetQuestionLimitDto } from './dto/set-question-limit.dto';
import { UpdateQuestionPaperDto } from './dto/update-question-paper.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as PDFDocument from 'pdfkit';
// Note: The docx package needs to be installed with: npm install docx --save
import {
  Document as DocxDocument,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  AlignmentType,
  BorderStyle,
} from 'docx';

@Injectable()
export class QuestionPapersService {
  private readonly logger = new Logger(QuestionPapersService.name);

  constructor(
    @InjectModel(QuestionPaper.name)
    private questionPaperModel: Model<QuestionPaper>,
    @InjectModel(Question.name) private questionModel: Model<Question>,
    @InjectModel(Subject.name) private subjectModel: Model<Subject>,
    @InjectModel(College.name) private collegeModel: Model<College>,
    private trackingService: TrackingService,
    private questionUsageService: QuestionUsageService,
  ) {}

  /**
   * Unified question paper creation method
   * Supports both single and multi-subject generation
   */
  async createUnified(
    createQuestionPaperDto: CreateQuestionPaperDto,
    user: any,
  ): Promise<QuestionPaper> {
    try {
      // Only Teachers and Super Admins can create question papers
      if (user.role !== 'teacher' && user.role !== 'superAdmin') {
        throw new BadRequestException(
          'Only teachers and super admins can create question papers.',
        );
      }

      // For teachers, collegeId is required
      if (user.role === 'teacher' && !user.collegeId) {
        throw new BadRequestException(
          'Teacher must be associated with a college',
        );
      }

      // BYPASS STATUS CHECKS FOR QUESTION PAPER GENERATION
      // Question paper generation should automatically bypass "approved" and "active" status validation
      // to allow generation from all available questions in the database
      createQuestionPaperDto.bypassStatusChecks = true;

      // Check if this is multi-subject or single subject
      const isMultiSubject = createQuestionPaperDto.subjects && createQuestionPaperDto.subjects.length > 0;

      // Validation for single vs multi-subject
      if (isMultiSubject) {
        // Multi-subject validation
        if (createQuestionPaperDto.subjects!.length < 2) {
          throw new BadRequestException('Multi-subject papers must contain at least 2 subjects');
        }
        if (!createQuestionPaperDto.duration) {
          throw new BadRequestException('Duration is required for multi-subject papers');
        }
        if (createQuestionPaperDto.includeAnswers === undefined) {
          throw new BadRequestException('includeAnswers is required for multi-subject papers');
        }

        // Validate unique subjects
        const subjectNames = createQuestionPaperDto.subjects!.map(s => s.subject);
        const uniqueSubjects = new Set(subjectNames);
        if (uniqueSubjects.size !== subjectNames.length) {
          throw new BadRequestException('Duplicate subjects are not allowed in multi-subject papers');
        }

        // Validate each subject configuration
        for (const subjectConfig of createQuestionPaperDto.subjects!) {
          if (subjectConfig.numberOfQuestions <= 0) {
            throw new BadRequestException(`Number of questions must be greater than 0 for subject: ${subjectConfig.subject}`);
          }
          if (subjectConfig.totalMarks <= 0) {
            throw new BadRequestException(`Total marks must be greater than 0 for subject: ${subjectConfig.subject}`);
          }
        }
      } else {
        // Single subject validation
        if (!createQuestionPaperDto.subject) {
          throw new BadRequestException('Subject is required for single subject papers');
        }
        if (!createQuestionPaperDto.customise) {
          throw new BadRequestException(
            'Only custom question paper generation is allowed. Please provide customise configuration.',
          );
        }

        // Ensure subjects array is not provided for single subject
        if (createQuestionPaperDto.subjects && createQuestionPaperDto.subjects.length > 0) {
          throw new BadRequestException('Cannot provide both subject and subjects fields. Use subject for single-subject or subjects for multi-subject papers.');
        }
      }

      // Get college information for response
      let collegeInfo: any = null;
      if (user.collegeId) {
        collegeInfo = await this.collegeModel.findById(user.collegeId).exec();
        if (!collegeInfo) {
          throw new BadRequestException('College not found');
        }
      }

      // Route to appropriate generation method
      if (isMultiSubject) {
        return this.createMultiSubjectUnified(createQuestionPaperDto, user, collegeInfo);
      } else {
        return this.createSingleSubjectUnified(createQuestionPaperDto, user, collegeInfo);
      }
    } catch (error) {
      this.logger.error(
        `Error creating unified question paper: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Single subject generation method
   */
  private async createSingleSubjectUnified(
    createQuestionPaperDto: CreateQuestionPaperDto,
    user: any,
    collegeInfo: any,
  ): Promise<any> {

    // Resolve subject - can be either ObjectId or shortcode
    const subject = await this.resolveSubject(createQuestionPaperDto.subject!);
    if (!subject) {
      throw new BadRequestException('Invalid subject provided');
    }

    // Check generation limits
    await this.checkGenerationLimits(user, subject._id.toString());

    // Build question query - questions are global, not college-specific
    const questionQuery: any = {
      subjectId: subject._id,
    };

    // Add status checks unless bypassed
    // Note: bypassStatusChecks is automatically set to true for question paper generation
    // to allow access to all questions regardless of their approval/active status
    if (!createQuestionPaperDto.bypassStatusChecks) {
      questionQuery.status = 'active';
      questionQuery.reviewStatus = 'approved';
    }

    // Add topic filter if specified
    if (createQuestionPaperDto.topicId) {
      questionQuery.topicId = createQuestionPaperDto.topicId;
    }

    // Get available questions
    const availableQuestions = await this.questionModel.find(questionQuery);
    if (availableQuestions.length === 0) {
      throw new BadRequestException(
        'No questions available for the specified criteria',
      );
    }

    // Filter out recently used questions
    const unusedQuestions = await this.filterUnusedQuestions(
      availableQuestions,
      user,
      subject._id.toString(),
      createQuestionPaperDto.topicId?.toString(),
    );

    if (unusedQuestions.length === 0) {
      throw new BadRequestException(
        'No unused questions available. Please add more questions or wait for the duplicate prevention period to expire.',
      );
    }

    // Customized generation mode only
    const customConfig = createQuestionPaperDto.customise!;

    if (unusedQuestions.length < customConfig.numberOfQuestions) {
      throw new BadRequestException(
        `Only ${unusedQuestions.length} unused questions available. Requested: ${customConfig.numberOfQuestions}`,
      );
    }

    // Select questions based on custom difficulty
    const selectedQuestions = await this.selectQuestionsByDifficulty(
      unusedQuestions,
      customConfig.numberOfQuestions,
      'custom',
      customConfig.customDifficulty,
    );

    // Create a single section for customized papers
    const sections = [
      {
        name: 'Section A',
        description: 'All Questions',
        order: 1,
        sectionMarks: customConfig.totalMarks,
        questions: selectedQuestions.map((q, index) => ({
          questionId: q._id,
          order: index + 1,
        })),
      },
    ];

    const totalMarks = customConfig.totalMarks;
    const duration = customConfig.duration;
    const withAnswers = customConfig.includeAnswers;

    // Create question paper data
    const questionPaperData: any = {
      title: createQuestionPaperDto.title,
      description: createQuestionPaperDto.description,
      subjectId: subject._id,
      topicId: createQuestionPaperDto.topicId,
      totalMarks,
      duration,
      withAnswers,
      instructions: createQuestionPaperDto.instructions,
      sections,
      questions: selectedQuestions.map((q) => q._id),
      generatedBy: user._id,
      status: 'active',
      examType: createQuestionPaperDto.examType || ExamType.CUSTOM,
      difficultyMode: 'custom',
    };

    // Set collegeId for teachers
    if (user.collegeId) {
      questionPaperData.collegeId = user.collegeId;
    }

    const questionPaper = new this.questionPaperModel(questionPaperData);
    const savedPaper = await questionPaper.save();

    // Record question usage for permanent tracking (only for teachers with collegeId)
    if (user.role === 'teacher' && user.collegeId) {
      await this.recordQuestionUsage(savedPaper, selectedQuestions, user);
    }

    return this.populateAndFormatResponse(savedPaper, collegeInfo);
  }

  /**
   * Multi-subject generation method
   */
  private async createMultiSubjectUnified(
    createQuestionPaperDto: CreateQuestionPaperDto,
    user: any,
    collegeInfo: any,
  ): Promise<any> {
    // Validate difficulty percentages for each subject
    for (const subjectConfig of createQuestionPaperDto.subjects!) {
      const { easyPercentage, mediumPercentage, hardPercentage } = subjectConfig.customDifficulty;
      if (easyPercentage + mediumPercentage + hardPercentage !== 100) {
        throw new BadRequestException(
          `Difficulty percentages for subject ${subjectConfig.subject} must sum to 100`,
        );
      }
    }

    const sections: any[] = [];
    const allSelectedQuestions: any[] = [];
    let totalMarks = 0;
    let questionOrder = 1;

    // Process each subject
    for (let i = 0; i < createQuestionPaperDto.subjects!.length; i++) {
      const subjectConfig = createQuestionPaperDto.subjects![i];

      // Resolve subject
      const subject = await this.resolveSubject(subjectConfig.subject);
      if (!subject) {
        throw new BadRequestException(`Invalid subject provided: ${subjectConfig.subject}`);
      }

      // Check generation limits for this subject
      await this.checkGenerationLimits(user, subject._id.toString());

      // Build question query
      const questionQuery: any = {
        subjectId: subject._id,
      };

      // Add status checks unless bypassed
      // Note: bypassStatusChecks is automatically set to true for question paper generation
      // to allow access to all questions regardless of their approval/active status
      if (!createQuestionPaperDto.bypassStatusChecks) {
        questionQuery.status = 'active';
        questionQuery.reviewStatus = 'approved';
      }

      // Add topic filter if specified
      if (subjectConfig.topicId) {
        questionQuery.topicId = subjectConfig.topicId;
      }

      // Get available questions
      const availableQuestions = await this.questionModel.find(questionQuery);
      if (availableQuestions.length === 0) {
        throw new BadRequestException(
          `No questions available for subject: ${subjectConfig.subject}`,
        );
      }

      // Filter out recently used questions
      const unusedQuestions = await this.filterUnusedQuestions(
        availableQuestions,
        user,
        subject._id.toString(),
        subjectConfig.topicId?.toString(),
      );

      if (unusedQuestions.length < subjectConfig.numberOfQuestions) {
        throw new BadRequestException(
          `Only ${unusedQuestions.length} unused questions available for ${subjectConfig.subject}. Requested: ${subjectConfig.numberOfQuestions}`,
        );
      }

      // Select questions based on custom difficulty
      const selectedQuestions = await this.selectQuestionsByDifficulty(
        unusedQuestions,
        subjectConfig.numberOfQuestions,
        'custom',
        subjectConfig.customDifficulty,
      );

      // Create section for this subject
      const sectionName = `Section ${String.fromCharCode(65 + i)}`; // A, B, C, etc.
      const section = {
        name: sectionName,
        description: `${subject.name} Questions (${subjectConfig.numberOfQuestions} questions, ${subjectConfig.totalMarks} marks)`,
        order: i + 1,
        sectionMarks: subjectConfig.totalMarks,
        subjectId: subject._id, // Add subject ID for reference
        subjectName: subject.name, // Add subject name for reference
        questions: selectedQuestions.map((q) => ({
          questionId: q._id,
          order: questionOrder++,
        })),
      };

      sections.push(section);
      allSelectedQuestions.push(...selectedQuestions);
      totalMarks += subjectConfig.totalMarks;
    }

    // Create question paper data
    const subjectNames = createQuestionPaperDto.subjects!.map(s => s.subject).join(', ');
    const questionPaperData: any = {
      title: createQuestionPaperDto.title,
      description: createQuestionPaperDto.description || `Multi-subject question paper with ${createQuestionPaperDto.subjects!.length} subjects: ${subjectNames}`,
      totalMarks,
      duration: createQuestionPaperDto.duration,
      withAnswers: createQuestionPaperDto.includeAnswers,
      instructions: createQuestionPaperDto.instructions,
      sections,
      questions: allSelectedQuestions.map((q) => q._id),
      generatedBy: user._id,
      status: 'active',
      examType: createQuestionPaperDto.examType || ExamType.CUSTOM,
      difficultyMode: 'custom',
      isMultiSubject: true,
      subjectCount: createQuestionPaperDto.subjects!.length,
      subjectBreakdown: createQuestionPaperDto.subjects!.map(s => ({
        subject: s.subject,
        questionCount: s.numberOfQuestions,
        marks: s.totalMarks
      })),
    };

    // Set collegeId for teachers
    if (user.collegeId) {
      questionPaperData.collegeId = user.collegeId;
    }

    const questionPaper = new this.questionPaperModel(questionPaperData);
    const savedPaper = await questionPaper.save();

    // Record question usage for permanent tracking (only for teachers with collegeId)
    if (user.role === 'teacher' && user.collegeId) {
      await this.recordQuestionUsage(savedPaper, allSelectedQuestions, user);
    }

    return this.populateAndFormatResponse(savedPaper, collegeInfo);
  }

  /**
   * Common method to populate and format response
   */
  private async populateAndFormatResponse(savedPaper: any, collegeInfo: any): Promise<any> {
    // Populate the saved paper with full question details before returning
    const populatedPaper = await this.questionPaperModel
      .findById(savedPaper._id)
      .populate('subjectId', 'name description')
      .populate('topicId', 'name description')
      .populate('questions', 'content options answer difficulty type marks explanation imageUrls')
      .exec();

    // Transform sections to include full question objects instead of just IDs
    if (populatedPaper && populatedPaper.sections) {
      populatedPaper.sections = populatedPaper.sections.map(section => {
        const mappedQuestions = section.questions.map(sectionQuestion => {
          const fullQuestion = populatedPaper.questions.find(
            q => {
              // Add proper type checking and handling
              const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
              const qIdStr = qId ? qId.toString() : '';
              const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
              return qIdStr === sectionQuestionIdStr;
            }
          );
          return {
            questionId: sectionQuestion.questionId,
            order: sectionQuestion.order,
            question: fullQuestion // Add this as an additional property
          };
        });

        return {
          ...section,
          questions: mappedQuestions
        };
      });
    }

    // Prepare response with college information
    const response: any = {
      questionPaper: populatedPaper,
    };

    // Add college information if available
    if (collegeInfo) {
      response.college = {
        name: collegeInfo.name,
        logoUrl: collegeInfo.logoUrl,
        address: collegeInfo.address,
      };
    }

    this.logger.log(
      `Question paper created: ${savedPaper.title} (ID: ${savedPaper._id})`,
    );
    return response;
  }



  async findAll(user: any) {
    let query: any = {};
    const populateFields = ['subjectId', 'topicId', 'questions'];

    switch (user.role) {
      case 'superAdmin':
        // Super admins can see all question papers with college information
        query = {};
        populateFields.push('collegeId', 'generatedBy');
        break;

      case 'collegeAdmin':
        // College admins can see all papers from their college with teacher information
        query = { collegeId: user.collegeId };
        populateFields.push('generatedBy');
        break;

      case 'teacher':
        // Teachers can only see their own papers
        query = { generatedBy: user._id };
        break;

      default:
        throw new BadRequestException('Invalid user role');
    }

    const questionPapers = await this.questionPaperModel.find(query).populate(populateFields).exec();

    // Transform sections to include full question objects instead of just IDs
    return questionPapers.map(questionPaper => {
      if (questionPaper && questionPaper.sections) {
        questionPaper.sections = questionPaper.sections.map(section => {
          const mappedQuestions = section.questions.map(sectionQuestion => {
            const fullQuestion = questionPaper.questions.find(
              q => {
                // Add proper type checking and handling
                const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                const qIdStr = qId ? qId.toString() : '';
                const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                return qIdStr === sectionQuestionIdStr;
              }
            );
            return {
              questionId: sectionQuestion.questionId,
              order: sectionQuestion.order,
              question: fullQuestion // Add this as an additional property
            };
          });
          
          return {
            ...section,
            questions: mappedQuestions
          };
        });
      }
      return questionPaper;
    });
  }

  async findOne(id: string, user: any) {
    const query: any = { _id: id };
    const populateFields = ['subjectId', 'topicId', 'questions'];

    switch (user.role) {
      case 'superAdmin':
        // Super admins can access any question paper
        populateFields.push('collegeId', 'generatedBy');
        break;

      case 'collegeAdmin':
        // College admins can access papers from their college
        query.collegeId = user.collegeId;
        populateFields.push('generatedBy');
        break;

      case 'teacher':
        // Teachers can only access their own papers
        query.generatedBy = user._id;
        break;

      default:
        throw new BadRequestException('Invalid user role');
    }

    const questionPaper = await this.questionPaperModel
      .findOne(query)
      .populate(populateFields)
      .exec();

    if (!questionPaper) {
      throw new NotFoundException(
        `Question paper with ID ${id} not found or access denied`,
      );
    }

    // Transform sections to include full question objects instead of just IDs
    if (questionPaper && questionPaper.sections) {
      questionPaper.sections = questionPaper.sections.map(section => {
        const mappedQuestions = section.questions.map(sectionQuestion => {
          const fullQuestion = questionPaper.questions.find(
            q => {
              // Add proper type checking and handling
              const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
              const qIdStr = qId ? qId.toString() : '';
              const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
              return qIdStr === sectionQuestionIdStr;
            }
          );
          return {
            questionId: sectionQuestion.questionId,
            order: sectionQuestion.order,
            question: fullQuestion // Add this as an additional property
          };
        });
        
        return {
          ...section,
          questions: mappedQuestions
        };
      });
    }

    return questionPaper;
  }

  async update(
    id: string,
    updateQuestionPaperDto: UpdateQuestionPaperDto,
    user: any,
  ) {
    const questionPaper = await this.findOne(id, user);

    // Teachers can only update the title of their own question papers
    if (user.role === 'teacher') {
      // Only allow title updates for teachers
      const allowedFields = ['title'];
      const updateData: any = {};

      for (const field of allowedFields) {
        if (updateQuestionPaperDto[field] !== undefined) {
          updateData[field] = updateQuestionPaperDto[field];
        }
      }

      if (Object.keys(updateData).length === 0) {
        throw new BadRequestException(
          'Teachers can only update the title of question papers',
        );
      }

      return await this.questionPaperModel
        .findByIdAndUpdate(id, updateData, { new: true })
        .exec();
    }

    // Super admins can update any field
    return await this.questionPaperModel
      .findByIdAndUpdate(id, updateQuestionPaperDto, { new: true })
      .exec();
  }



  async checkDownloadLimits(user: any, questionPaper: any): Promise<void> {
    // Super admins are not subject to download limits
    if (user.role === 'superAdmin') {
      return;
    }

    // Check download limits for teachers and college admins
    if (user.collegeId) {
      let limitDoc: any = null;

      // For single-subject papers, check subject-specific limits first
      if (questionPaper.subjectId) {
        limitDoc = await this.questionPaperModel
          .findOne({
            collegeId: user.collegeId,
            subjectId: questionPaper.subjectId,
            type: 'limit',
          })
          .exec();
      }

      // If no subject-specific limits found (or multi-subject paper), check college-wide limits
      if (!limitDoc) {
        limitDoc = await this.questionPaperModel
          .findOne({
            collegeId: user.collegeId,
            type: 'limit',
            subjectId: { $exists: false },
          })
          .exec();
      }

      if (limitDoc?.maxDownloads) {
        // Use TrackingService to check download count
        const downloadStats =
          await this.trackingService.getTeacherDownloadStats(user._id, {
            startDate: new Date(
              new Date().getFullYear(),
              new Date().getMonth(),
              1,
            ), // Start of current month
            endDate: new Date(), // Current date
          });

        const totalDownloads = downloadStats.reduce(
          (sum, stat) => sum + stat.totalDownloads,
          0,
        );

        if (totalDownloads >= limitDoc.maxDownloads) {
          throw new BadRequestException(
            `You have reached the maximum number of downloads (${limitDoc.maxDownloads}) allowed for this month`,
          );
        }
      }
    }
  }

  async download(
    id: string,
    format: 'pdf' | 'docx' = 'pdf',
    user?: any,
  ): Promise<string> {
    try {
      // First, check if user has access to this question paper
      const questionPaper = await this.findOne(id, user);

      // Check download limits
      if (user) {
        await this.checkDownloadLimits(user, questionPaper);
      }

      // Ensure uploads directory exists
      const uploadsDir = path.join(process.cwd(), 'uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      // Generate the file
      const fileName = `${questionPaper.title.replace(/\s+/g, '_')}_${Date.now()}.${format}`;
      const filePath = path.join(uploadsDir, fileName);

      if (format === 'pdf') {
        await this.generatePDF(questionPaper, filePath);
      } else if (format === 'docx') {
        await this.generateDOCX(questionPaper, filePath);
      } else {
        throw new BadRequestException(`Unsupported format: ${format}`);
      }

      this.logger.log(`Generated ${format.toUpperCase()} file at: ${filePath}`);
      return filePath;
    } catch (error) {
      this.logger.error(
        `Error generating ${format} file: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async setQuestionLimit(
    setQuestionLimitDto: SetQuestionLimitDto,
  ): Promise<void> {
    const updateData: any = {};

    // Handle backward compatibility
    if (setQuestionLimitDto.maxQuestions) {
      updateData.maxQuestions = setQuestionLimitDto.maxQuestions;
    }

    if (setQuestionLimitDto.maxGeneration) {
      updateData.maxGeneration = setQuestionLimitDto.maxGeneration;
    }

    if (setQuestionLimitDto.maxDownloads) {
      updateData.maxDownloads = setQuestionLimitDto.maxDownloads;
    }

    const query: any = {
      collegeId: setQuestionLimitDto.collegeId,
      type: 'limit',
    };

    // If subjectId is provided, set subject-specific limits, otherwise college-wide limits
    if (setQuestionLimitDto.subjectId) {
      query.subjectId = setQuestionLimitDto.subjectId;
    }

    await this.questionPaperModel
      .updateOne(query, { $set: updateData }, { upsert: true })
      .exec();
  }

  /**
   * Helper method to detect if a string is a base64 image
   */
  private isBase64Image(str: string): boolean {
    if (!str || typeof str !== 'string') return false;

    // Check for data URL format
    const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
    if (dataUrlPattern.test(str)) return true;

    // Check for raw base64 (without data URL prefix)
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    return str.length > 100 && base64Pattern.test(str);
  }

  /**
   * Helper method to extract images from text content
   */
  private extractImagesFromText(text: string, addPlaceholders: boolean = true): {
    cleanText: string;
    images: Array<{ id: string; data: Buffer; format: string }>;
  } {
    if (!text) return { cleanText: text, images: [] };

    const images: Array<{ id: string; data: Buffer; format: string }> = [];
    let cleanText = text;
    let imageCounter = 0;

    // Look for data URLs
    const dataUrlPattern = /data:image\/([^;]+);base64,([A-Za-z0-9+/]+=*)/g;
    let match;

    while ((match = dataUrlPattern.exec(text)) !== null) {
      const [fullMatch, format, base64Data] = match;
      try {
        const buffer = Buffer.from(base64Data, 'base64');
        images.push({
          id: `image_${imageCounter++}`,
          data: buffer,
          format: format.toLowerCase()
        });

        // Replace the base64 string with a placeholder or remove it
        if (addPlaceholders) {
          cleanText = cleanText.replace(fullMatch, `[Image ${imageCounter}]`);
        } else {
          cleanText = cleanText.replace(fullMatch, '');
        }
      } catch (error) {
        this.logger.warn(`Failed to process base64 image: ${error.message}`);
      }
    }

    // Look for standalone base64 strings (without data URL prefix)
    const base64Pattern = /[A-Za-z0-9+/]{100,}={0,2}/g;
    const base64Matches = cleanText.match(base64Pattern);

    if (base64Matches) {
      base64Matches.forEach((base64String) => {
        if (this.isBase64Image(base64String)) {
          try {
            const buffer = Buffer.from(base64String, 'base64');
            // Try to determine format from buffer header
            let format = 'jpeg'; // default
            if (buffer[0] === 0x89 && buffer[1] === 0x50) format = 'png';
            else if (buffer[0] === 0xFF && buffer[1] === 0xD8) format = 'jpeg';
            else if (buffer[0] === 0x47 && buffer[1] === 0x49) format = 'gif';

            images.push({
              id: `image_${imageCounter++}`,
              data: buffer,
              format
            });

            // Replace the base64 string with a placeholder or remove it
            if (addPlaceholders) {
              cleanText = cleanText.replace(base64String, `[Image ${imageCounter}]`);
            } else {
              cleanText = cleanText.replace(base64String, '');
            }
          } catch (error) {
            this.logger.warn(`Failed to process standalone base64 image: ${error.message}`);
          }
        }
      });
    }

    // Clean up extra whitespace
    cleanText = cleanText.replace(/\s+/g, ' ').trim();

    return { cleanText, images };
  }

  private async generatePDF(
    questionPaper: QuestionPaper,
    filePath: string,
  ): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const stream = fs.createWriteStream(filePath);

        doc.pipe(stream);

        // Helper function to add header with college info and logos
        const addHeader = async (subjectName?: string) => {
          // Add Medicos logo (watermark)
          try {
            const medicosLogoPath = path.join(__dirname, '../../assets/medicos-logo.svg');
            if (fs.existsSync(medicosLogoPath)) {
              // Add as watermark with low opacity
              doc.save();
              doc.opacity(0.1);
              doc.image(medicosLogoPath, doc.page.width - 150, 50, { width: 100 });
              doc.restore();
            }
          } catch (error) {
            this.logger.warn(`Failed to add Medicos logo: ${error.message}`);
          }

          // Add college information if available
          if (questionPaper.collegeId && typeof questionPaper.collegeId === 'object') {
            const college = questionPaper.collegeId as any;

            // College logo
            if (college.logoUrl) {
              try {
                doc.image(college.logoUrl, 50, 50, { width: 60, height: 60 });
              } catch (error) {
                this.logger.warn(`Failed to add college logo: ${error.message}`);
              }
            }

            // College name
            if (college.name) {
              doc.fontSize(16).font('Times-Bold').text(college.name, { align: 'center' });
              doc.moveDown(0.5);
            }

            // College address
            if (college.address) {
              doc.fontSize(10).font('Times-Roman').text(college.address, { align: 'center' });
              doc.moveDown(0.5);
            }
          }

          // Add title
          doc.fontSize(18).font('Times-Bold').text(questionPaper.title, { align: 'center' });
          doc.moveDown(0.5);

          // Add subject name if provided (for multi-subject papers)
          if (subjectName) {
            doc.fontSize(16).font('Times-Bold').text(`Subject: ${subjectName}`, { align: 'center' });
            doc.moveDown(0.5);
          } else if (
            questionPaper.subjectId &&
            typeof questionPaper.subjectId === 'object' &&
            'name' in questionPaper.subjectId
          ) {
            doc.fontSize(16).font('Times-Bold').text(`Subject: ${(questionPaper.subjectId as any).name}`, { align: 'center' });
            doc.moveDown(0.5);
          }

          // Add exam info
          doc.fontSize(12).font('Times-Roman');
          doc.text(`Duration: ${questionPaper.duration} minutes`, { align: 'center' });
          doc.text(`Total Marks: ${questionPaper.totalMarks}`, { align: 'center' });
          doc.moveDown();

          // Add instructions if any
          if (questionPaper.instructions) {
            doc.fontSize(12).font('Times-Bold').text('Instructions:', { underline: true });
            doc.fontSize(10).font('Times-Roman').text(questionPaper.instructions);
            doc.moveDown();
          }

          // Add a line separator
          doc.moveTo(50, doc.y).lineTo(doc.page.width - 50, doc.y).stroke();
          doc.moveDown();
        };

        // Check if this is a multi-subject paper
        const isMultiSubject = questionPaper.sections && questionPaper.sections.length > 1;

        if (isMultiSubject) {
          // Handle multi-subject papers - separate page for each subject
          for (let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++) {
            const section = questionPaper.sections[sectionIndex];

            // Add new page for each subject (except the first one)
            if (sectionIndex > 0) {
              doc.addPage();
            }

            // Add header for this subject
            await addHeader(section.subjectName || section.name || `Subject ${sectionIndex + 1}`);

            // Add questions for this section
            if (section.questions && section.questions.length > 0) {
              this.addQuestionsToPage(doc, section.questions, questionPaper.withAnswers);
            }
          }
        } else {
          // Handle single-subject papers
          await addHeader();

          // Add questions for single-subject papers
          if (questionPaper.questions && questionPaper.questions.length > 0) {
            this.addQuestionsToPage(doc, questionPaper.questions, questionPaper.withAnswers);
          }
        }

        doc.end();

        stream.on('finish', () => {
          resolve();
        });

        stream.on('error', (err) => {
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // Helper method to add questions to a page
  private addQuestionsToPage(doc: any, questions: any[], withAnswers: boolean): void {
    questions.forEach((question: any, index: number) => {
      // Check if we need a new page
      if (doc.y > doc.page.height - 100) {
        doc.addPage();
      }

      // Process question content for images (no placeholders for PDF)
      const questionResult = this.extractImagesFromText(question.content, false);

      // Add question text with Times New Roman font
      doc.fontSize(11).font('Times-Roman').text(`${index + 1}. ${questionResult.cleanText}`);

      // Add question images if any
      questionResult.images.forEach((image) => {
        try {
          doc.moveDown(0.5);
          // Check if we need a new page for the image
          if (doc.y + 80 > doc.page.height - 50) {
            doc.addPage();
          }

          const currentY = doc.y;
          doc.image(image.data, doc.x, currentY, {
            fit: [120, 60]
          });

          // Move cursor below the image
          doc.y = currentY + 70; // Image height + padding
          doc.moveDown(0.5);
        } catch (error) {
          this.logger.warn(`Failed to add question image: ${error.message}`);
          doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
        }
      });

      // Also handle imageUrls field if present
      if (question.imageUrls && Array.isArray(question.imageUrls) && question.imageUrls.length > 0) {
        question.imageUrls.forEach((imageUrl: string) => {
          if (this.isBase64Image(imageUrl)) {
            try {
              const imageResult = this.extractImagesFromText(imageUrl, false);
              imageResult.images.forEach((image) => {
                doc.moveDown(0.5);
                // Check if we need a new page for the image
                if (doc.y + 80 > doc.page.height - 50) {
                  doc.addPage();
                }

                const currentY = doc.y;
                doc.image(image.data, doc.x, currentY, {
                  fit: [120, 60]
                });

                // Move cursor below the image
                doc.y = currentY + 70; // Image height + padding
                doc.moveDown(0.5);
              });
            } catch (error) {
              this.logger.warn(`Failed to add imageUrl image: ${error.message}`);
              doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
            }
          } else {
            // For regular URLs, just show the URL
            doc.fontSize(8).fillColor('blue').text(`Image: ${imageUrl}`).fillColor('black');
            doc.moveDown(0.2);
          }
        });
      }

      // Add options if available
      if (
        question.options &&
        Array.isArray(question.options) &&
        question.options.length > 0
      ) {
        doc.moveDown(0.5);
        question.options.forEach((option: string, optIndex: number) => {
          const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d...

          // Process option content for images (no placeholders for PDF)
          const optionResult = this.extractImagesFromText(option, false);

          // Add option text
          doc.fontSize(10).font('Times-Roman').text(`    ${optionLabel}) ${optionResult.cleanText}`);

          // Add option images if any
          optionResult.images.forEach((image) => {
            try {
              doc.moveDown(0.3);
              // Check if we need a new page for the image
              if (doc.y + 70 > doc.page.height - 50) {
                doc.addPage();
              }

              // Calculate position with indent
              const currentX = doc.x + 20;
              const currentY = doc.y;

              doc.image(image.data, currentX, currentY, {
                fit: [100, 50]
              });

              // Move cursor below the image
              doc.y = currentY + 60; // Image height + padding
              doc.moveDown(0.3);
            } catch (error) {
              this.logger.warn(`Failed to add option image: ${error.message}`);
              doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
            }
          });
        });
      }

      // Add answer if withAnswers is true
      if (withAnswers && question.answer) {
        doc.moveDown(0.3);

        // Process answer content for images
        const answerResult = this.extractImagesFromText(question.answer);

        doc
          .fillColor('black')
          .fontSize(10)
          .font('Times-Bold')
          .text(`    Answer: ${answerResult.cleanText}`)
          .font('Times-Roman')
          .fillColor('black');

        // Add answer images if any
        answerResult.images.forEach((image) => {
          try {
            doc.moveDown(0.3);
            // Check if we need a new page for the image
            if (doc.y + 70 > doc.page.height - 50) {
              doc.addPage();
            }

            // Calculate position with indent
            const currentX = doc.x + 20;
            const currentY = doc.y;

            doc.image(image.data, currentX, currentY, {
              fit: [100, 50]
            });

            // Move cursor below the image
            doc.y = currentY + 60; // Image height + padding
            doc.moveDown(0.3);
          } catch (error) {
            this.logger.warn(`Failed to add answer image: ${error.message}`);
            doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
          }
        });
      }

      doc.moveDown();
    });
  }

  private async generateDOCX(
    questionPaper: QuestionPaper,
    filePath: string,
  ): Promise<void> {
    try {
      // Create document sections
      const sections: Array<{
        properties: Record<string, unknown>;
        children: Paragraph[];
      }> = [];

      // Title section
      sections.push({
        properties: {},
        children: [
          new Paragraph({
            text: questionPaper.title,
            heading: HeadingLevel.HEADING_1,
            alignment: AlignmentType.CENTER,
          }),
        ],
      });

      // Subject information if available
      if (
        questionPaper.subjectId &&
        typeof questionPaper.subjectId === 'object' &&
        'name' in questionPaper.subjectId
      ) {
        sections[0].children.push(
          new Paragraph({
            text: `Subject: ${(questionPaper.subjectId as any).name}`,
            alignment: AlignmentType.CENTER,
          }),
        );
      } else if (questionPaper.isMultiSubject && questionPaper.sections) {
        // For multi-subject papers, show subject breakdown
        const subjectInfo = questionPaper.sections
          .map((section: any) => section.subjectName || section.name)
          .filter(Boolean)
          .join(', ');
        if (subjectInfo) {
          sections[0].children.push(
            new Paragraph({
              text: `Subjects: ${subjectInfo}`,
              alignment: AlignmentType.CENTER,
            }),
          );
        }
      }

      // Metadata section (duration, marks)
      sections[0].children.push(
        new Paragraph({
          text: `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`,
          alignment: AlignmentType.LEFT,
          spacing: {
            before: 400,
            after: 200,
          },
        }),
      );

      // Instructions section if available
      if (questionPaper.instructions) {
        sections[0].children.push(
          new Paragraph({
            text: 'Instructions:',
            heading: HeadingLevel.HEADING_2,
          }),
          new Paragraph({
            text: questionPaper.instructions,
            spacing: {
              after: 200,
            },
          }),
        );
      }

      // Questions section
      sections[0].children.push(
        new Paragraph({
          text: 'Questions:',
          heading: HeadingLevel.HEADING_2,
          spacing: {
            before: 200,
            after: 200,
          },
        }),
      );

      // Add each question
      questionPaper.questions.forEach((question: any, index: number) => {
        // Process question content for images
        const questionResult = this.extractImagesFromText(question.content);

        // Question text
        sections[0].children.push(
          new Paragraph({
            text: `${index + 1}. ${questionResult.cleanText}`,
            spacing: {
              before: 200,
              after: 100,
            },
          }),
        );

        // Add note about images if any were found
        if (questionResult.images.length > 0) {
          sections[0].children.push(
            new Paragraph({
              text: `    [${questionResult.images.length} image(s) - see PDF version for images]`,
              spacing: {
                after: 100,
              },
            }),
          );
        }

        // Add options if available
        if (
          question.options &&
          Array.isArray(question.options) &&
          question.options.length > 0
        ) {
          question.options.forEach((option: string, optIndex: number) => {
            const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d...

            // Process option content for images
            const optionResult = this.extractImagesFromText(option);

            sections[0].children.push(
              new Paragraph({
                text: `    ${optionLabel}) ${optionResult.cleanText}`,
                indent: {
                  left: 720, // 0.5 inches in twips
                },
              }),
            );

            // Add note about images if any were found
            if (optionResult.images.length > 0) {
              sections[0].children.push(
                new Paragraph({
                  text: `        [${optionResult.images.length} image(s) - see PDF version for images]`,
                  indent: {
                    left: 720,
                  },
                }),
              );
            }
          });
        }

        // Add answer if withAnswers is true
        if (questionPaper.withAnswers && question.answer) {
          // Process answer content for images (no placeholders for PDF)
          const answerResult = this.extractImagesFromText(question.answer, false);

          sections[0].children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `    Answer: ${answerResult.cleanText}`,
                  color: '0000FF', // Blue color
                  bold: true,
                }),
              ],
              indent: {
                left: 720, // 0.5 inches in twips
              },
            }),
          );

          // Add note about images if any were found
          if (answerResult.images.length > 0) {
            sections[0].children.push(
              new Paragraph({
                text: `        [${answerResult.images.length} image(s) - see PDF version for images]`,
                indent: {
                  left: 720,
                },
              }),
            );
          }
        }
      });

      // Create the document
      const doc = new DocxDocument({
        sections,
      });

      // Write the document to a file
      const buffer = await Packer.toBuffer(doc);
      fs.writeFileSync(filePath, buffer);
    } catch (error) {
      this.logger.error(`Error generating DOCX: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Resolve subject from either ObjectId or shortcode
   */
  public async resolveSubject(subjectInput: string): Promise<any> {
    // First try to find by ObjectId (if it's a valid ObjectId)
    if (Types.ObjectId.isValid(subjectInput)) {
      const subject = await this.subjectModel.findById(subjectInput);
      if (subject) return subject;
    }

    // Try to find by shortcode
    const subject = await this.findSubjectByShortCode(
      subjectInput as SubjectShortCode,
    );
    if (subject) return subject;

    // Try to find by exact name match
    const exactMatch = await this.subjectModel.findOne({
      name: { $regex: new RegExp(`^${subjectInput}$`, 'i') },
    });
    if (exactMatch) return exactMatch;

    return null;
  }

  /**
   * Find subject by short code (phy, chem, bio, math, etc.)
   */
  private async findSubjectByShortCode(
    shortCode: SubjectShortCode,
  ): Promise<any> {
    const subjectMappings = {
      [SubjectShortCode.PHYSICS]: ['physics', 'phy'],
      [SubjectShortCode.CHEMISTRY]: ['chemistry', 'chem'],
      [SubjectShortCode.BIOLOGY]: ['biology', 'bio'],
      [SubjectShortCode.MATHEMATICS]: ['mathematics', 'math'],
      [SubjectShortCode.MATH]: ['mathematics', 'math'],
      [SubjectShortCode.PHY]: ['physics', 'phy'],
      [SubjectShortCode.CHEM]: ['chemistry', 'chem'],
      [SubjectShortCode.BIO]: ['biology', 'bio'],
    };

    const searchTerms = subjectMappings[shortCode] || [shortCode];

    for (const term of searchTerms) {
      const subject = await this.subjectModel.findOne({
        name: { $regex: new RegExp(term, 'i') },
      });
      if (subject) return subject;
    }

    return null;
  }

  /**
   * Record question usage for permanent tracking
   */
  private async recordQuestionUsage(
    questionPaper: any,
    selectedQuestions: any[],
    user: any,
  ): Promise<void> {
    try {
      const usageData = selectedQuestions.map((question) => ({
        collegeId: user.collegeId,
        questionId: question._id.toString(),
        questionPaperId: questionPaper._id.toString(),
        usedBy: user._id,
        subjectId: questionPaper.subjectId ? questionPaper.subjectId.toString() : question.subjectId?.toString(),
        topicId: questionPaper.topicId?.toString() || question.topicId?.toString(),
        metadata: {
          examType: questionPaper.examType,
          difficulty: question.difficulty,
          marks: question.marks || 1,
        },
      }));

      const result =
        await this.questionUsageService.recordMultipleQuestionUsage(usageData);
      this.logger.log(
        `Recorded question usage: ${result.recorded} recorded, ${result.skipped} skipped for paper ${questionPaper._id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to record question usage for paper ${questionPaper._id}: ${error.message}`,
        error.stack,
      );
      // Don't throw error here to avoid breaking question paper creation
    }
  }

  /**
   * Check generation limits for teachers
   */
  private async checkGenerationLimits(
    user: any,
    subjectId: string,
  ): Promise<void> {
    const limitDoc = await this.questionPaperModel
      .findOne({
        collegeId: user.collegeId,
        subjectId,
        type: 'limit',
      })
      .exec();

    if (limitDoc?.maxQuestions) {
      const existingPapers = await this.questionPaperModel.countDocuments({
        generatedBy: user._id,
        collegeId: user.collegeId,
        subjectId,
        type: { $ne: 'limit' },
      });

      if (existingPapers >= limitDoc.maxQuestions) {
        throw new BadRequestException(
          `You have reached the maximum number of question papers (${limitDoc.maxQuestions}) allowed for this subject`,
        );
      }
    }
  }

  /**
   * Filter out permanently used questions for the college
   */
  private async filterUnusedQuestions(
    questions: any[],
    user: any,
    subjectId: string,
    topicId?: string,
  ): Promise<any[]> {
    // For super admins, don't apply permanent filtering (they can reuse questions)
    if (user.role === 'superAdmin') {
      return questions;
    }

    // For teachers and college admins, use permanent question usage tracking
    if (user.collegeId) {
      const availableQuestionIds = questions.map((q) => q._id.toString());

      const unusedQuestionIds =
        await this.questionUsageService.getUnusedQuestions(
          user.collegeId,
          availableQuestionIds,
          {
            subjectId,
            ...(topicId && { topicId }),
          },
        );

      return questions.filter((q) =>
        unusedQuestionIds.includes(q._id.toString()),
      );
    }

    // Fallback to old logic if no collegeId (shouldn't happen for teachers)
    const usedQuestions = await this.questionPaperModel.distinct('questions', {
      collegeId: user.collegeId,
      subjectId,
      ...(topicId && { topicId }),
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
    });

    return questions.filter(
      (q) =>
        !usedQuestions.map((id) => id.toString()).includes(q._id.toString()),
    );
  }

  /**
   * Select questions based on difficulty mode
   */
  private async selectQuestionsByDifficulty(
    questions: any[],
    numberOfQuestions: number,
    difficultyMode: string,
    customDifficulty?: any,
  ): Promise<any[]> {
    let easyPercentage = 30;
    let mediumPercentage = 50;
    let hardPercentage = 20;

    if (difficultyMode === 'custom' && customDifficulty) {
      easyPercentage = customDifficulty.easyPercentage;
      mediumPercentage = customDifficulty.mediumPercentage;
      hardPercentage = customDifficulty.hardPercentage;
    }

    // Calculate number of questions for each difficulty
    const easyCount = Math.round((numberOfQuestions * easyPercentage) / 100);
    const mediumCount = Math.round(
      (numberOfQuestions * mediumPercentage) / 100,
    );
    const hardCount = numberOfQuestions - easyCount - mediumCount;

    // Separate questions by difficulty
    const easyQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'easy',
    );
    const mediumQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'medium',
    );
    const hardQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'hard',
    );

    // Randomly select questions from each difficulty level
    const selectedQuestions: any[] = [];

    // Select easy questions
    const shuffledEasy = easyQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledEasy.slice(0, Math.min(easyCount, shuffledEasy.length)),
    );

    // Select medium questions
    const shuffledMedium = mediumQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledMedium.slice(0, Math.min(mediumCount, shuffledMedium.length)),
    );

    // Select hard questions
    const shuffledHard = hardQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledHard.slice(0, Math.min(hardCount, shuffledHard.length)),
    );

    // If we don't have enough questions of specific difficulties, fill with any available
    if (selectedQuestions.length < numberOfQuestions) {
      const remainingQuestions = questions.filter(
        (q) =>
          !selectedQuestions
            .map((sq) => sq._id.toString())
            .includes(q._id.toString()),
      );
      const shuffledRemaining = remainingQuestions.sort(
        () => Math.random() - 0.5,
      );
      selectedQuestions.push(
        ...shuffledRemaining.slice(
          0,
          numberOfQuestions - selectedQuestions.length,
        ),
      );
    }

    return selectedQuestions.slice(0, numberOfQuestions);
  }
}
