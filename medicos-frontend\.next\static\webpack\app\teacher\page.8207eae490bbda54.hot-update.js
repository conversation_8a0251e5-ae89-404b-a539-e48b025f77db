"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* harmony import */ var _steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/multi-subject-config-step */ \"(app-pages-browser)/./src/components/teacher/steps/multi-subject-config-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    paperMode: \"single\",\n    course: \"\",\n    subject: \"\",\n    subjects: [],\n    subjectConfigs: {},\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easyPercentage: 30,\n        mediumPercentage: 50,\n        hardPercentage: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const goToFirstStep = ()=>{\n        setCurrentStep(0);\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            // Validate based on paper mode\n            if (formData.paperMode === \"single\") {\n                if (!formData.subject) {\n                    setIsGenerating(false);\n                    alert(\"Please select a subject\");\n                    return;\n                }\n            } else {\n                if (formData.subjects.length === 0) {\n                    setIsGenerating(false);\n                    alert(\"Please select at least one subject\");\n                    return;\n                }\n            }\n            // Prepare the API payload\n            let apiPayload;\n            if (formData.paperMode === \"single\") {\n                // Single subject mode\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    subject: formData.subject,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    topicId: formData.topicId\n                };\n                // Add customization if not auto mode\n                if (formData.difficultyMode === \"custom\") {\n                    apiPayload.customise = {\n                        customDifficulty: formData.difficultyLevels,\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                } else {\n                    // For auto mode, still include customization with default values\n                    apiPayload.customise = {\n                        customDifficulty: {\n                            easyPercentage: 30,\n                            mediumPercentage: 50,\n                            hardPercentage: 20\n                        },\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                }\n            } else {\n                // Multi-subject mode\n                const subjects = formData.subjects.map((subjectName)=>{\n                    const config = formData.subjectConfigs[subjectName];\n                    return {\n                        subject: subjectName,\n                        numberOfQuestions: config.numberOfQuestions,\n                        totalMarks: config.totalMarks,\n                        customDifficulty: config.difficultyLevels,\n                        topicId: config.topicId\n                    };\n                });\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    subjects: subjects,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Calling createQuestionPaper API...\");\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"API result:\", result);\n            // Check if the request was successful\n            if (!result.success) {\n                console.log(\"API returned error:\", result.error);\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                console.log(\"Staying on current step due to error\");\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            console.log(\"API success! Proceeding with download...\");\n            console.log(\"Full API response:\", result);\n            // After the API fix, result.data should contain the question paper directly\n            const questionPaper = result.data;\n            console.log(\"Question paper data:\", questionPaper);\n            // Validate that we have a question paper ID\n            if (!questionPaper || !questionPaper._id) {\n                console.error(\"No question paper ID found in response. Full response:\", result);\n                throw new Error(\"Question paper was created but no ID was returned. Please check the console for details and try again.\");\n            }\n            // Fetch complete paper with college/subject info for PDF payload\n            const fullData = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.getQuestionPaperForPDF)(questionPaper._id);\n            const qp = fullData.questionPaper;\n            const college = fullData.college || {};\n            // Flatten questions with subject names and image data\n            const flattenQuestions = ()=>{\n                if (qp.isMultiSubject && qp.sections) {\n                    const arr = [];\n                    qp.sections.forEach((sec)=>{\n                        const subjectName = sec.subjectName || sec.name || \"\";\n                        sec.questions.forEach((qwrap)=>{\n                            const q = qwrap.question || qwrap;\n                            arr.push({\n                                question: q.content || q.question || \"\",\n                                options: q.options || [],\n                                answer: q.answer || \"\",\n                                subject: subjectName,\n                                imageUrls: q.imageUrls || [],\n                                solution: q.solution || null,\n                                hints: q.hints || []\n                            });\n                        });\n                    });\n                    return arr;\n                }\n                return (qp.questions || []).map((q)=>{\n                    var _qp_subjectId;\n                    return {\n                        question: q.content || q.question || \"\",\n                        options: q.options || [],\n                        answer: q.answer || \"\",\n                        subject: ((_qp_subjectId = qp.subjectId) === null || _qp_subjectId === void 0 ? void 0 : _qp_subjectId.name) || \"\",\n                        imageUrls: q.imageUrls || [],\n                        solution: q.solution || null,\n                        hints: q.hints || []\n                    };\n                });\n            };\n            const pdfPayload = {\n                title: qp.title,\n                description: qp.description || \"\",\n                duration: qp.duration,\n                totalMarks: qp.totalMarks,\n                instructions: qp.instructions || \"\",\n                includeAnswers: formData.includeAnswers,\n                questions: flattenQuestions(),\n                filename: \"\".concat(qp.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\"),\n                collegeName: (college === null || college === void 0 ? void 0 : college.name) || \"\",\n                collegeLogoUrl: (college === null || college === void 0 ? void 0 : college.logoUrl) || \"\"\n            };\n            // Call internal API route to generate PDF via Puppeteer\n            const pdfRes = await fetch(\"/api/generate-paper-pdf\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(pdfPayload)\n            });\n            if (!pdfRes.ok) {\n                throw new Error(\"Server-side PDF generation failed\");\n            }\n            const pdfBlob = await pdfRes.blob();\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = pdfPayload.filename;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Build steps array dynamically based on paper mode\n    const buildSteps = ()=>{\n        const baseSteps = [\n            {\n                title: \"Question Type\",\n                icon: \"HelpCircle\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Paper Details\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Course & Subject Selection\",\n                icon: \"BookOpen\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, this)\n            }\n        ];\n        // Add multi-subject configuration step if in multi-subject mode\n        if (formData.paperMode === \"multi\") {\n            baseSteps.push({\n                title: \"Configure Subjects\",\n                icon: \"Settings\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__.MultiSubjectConfigStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, this)\n            });\n        }\n        // Add remaining steps only for single subject mode\n        if (formData.paperMode === \"single\") {\n            baseSteps.push({\n                title: \"Select Difficulty Level\",\n                icon: \"BarChart2\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Question Selection Criteria\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Paper Customization\",\n                icon: \"FileEdit\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 13\n                }, this)\n            });\n        }\n        // Add final steps for both modes\n        baseSteps.push({\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 468,\n                columnNumber: 11\n            }, this)\n        }, {\n            title: \"Generate Paper\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating,\n                onBack: goToFirstStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 481,\n                columnNumber: 20\n            }, this)\n        });\n        return baseSteps;\n    };\n    const steps = buildSteps();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 493,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3RlYWNoZXIvcXVlc3Rpb24tcGFwZXItd2l6YXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUM0SDtBQUMvRjtBQUNFO0FBQ0k7QUFDSTtBQUNFO0FBQ1I7QUFDZjtBQUNDO0FBQ3NDO0FBQ2Y7QUFzQzFFLE1BQU1hLGtCQUE0QjtJQUNoQ0MsY0FBYztJQUNkQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsV0FBVztJQUNYQyxRQUFRO0lBQ1JDLFNBQVM7SUFDVEMsVUFBVSxFQUFFO0lBQ1pDLGdCQUFnQixDQUFDO0lBQ2pCQyxnQkFBZ0I7SUFDaEJDLGtCQUFrQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGdCQUFnQjtJQUNsQjtJQUNBQyxtQkFBbUI7SUFDbkJDLFlBQVk7SUFDWkMsZ0JBQWdCO0lBQ2hCQyxVQUFVO0lBQ1ZDLGNBQWM7SUFDZEMsU0FBU0M7QUFDWDtBQUVPLFNBQVNDOztJQUNkLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUMsVUFBVUMsWUFBWSxHQUFHdEMsK0NBQVFBLENBQVdhO0lBQ25ELE1BQU0sQ0FBQzBCLGNBQWNDLGdCQUFnQixHQUFHeEMsK0NBQVFBLENBQUM7SUFFakQsTUFBTXlDLGlCQUFpQixDQUFDQztRQUN0QkosWUFBWSxDQUFDSyxPQUFVO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsR0FBR0QsSUFBSTtZQUFDO0lBQzVDO0lBRUEsTUFBTUUsV0FBVztRQUNmUixlQUFlLENBQUNPLE9BQVNFLEtBQUtDLEdBQUcsQ0FBQ0gsT0FBTyxHQUFHSSxNQUFNQyxNQUFNLEdBQUc7SUFDN0Q7SUFFQSxNQUFNQyxXQUFXO1FBQ2ZiLGVBQWUsQ0FBQ08sT0FBU0UsS0FBS0ssR0FBRyxDQUFDUCxPQUFPLEdBQUc7SUFDOUM7SUFFQSxNQUFNUSxXQUFXO1FBQ2ZQO0lBQ0Y7SUFFQSxNQUFNUSxnQkFBZ0I7UUFDcEJoQixlQUFlO0lBQ2pCO0lBRUEsTUFBTWlCLGVBQWU7UUFDbkIsSUFBSWQsY0FBYyxRQUFRLCtCQUErQjtRQUV6RCxJQUFJO2dCQWdCR0Y7WUFmTEcsZ0JBQWdCO1lBQ2hCYyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CbEI7WUFFaEMsZ0NBQWdDO1lBQ2hDLE1BQU1tQixlQUFlQyxhQUFhQyxPQUFPLENBQUM7WUFDMUMsTUFBTUMsZ0JBQWdCRixhQUFhQyxPQUFPLENBQUM7WUFDM0MsTUFBTUUsUUFBUUgsYUFBYUMsT0FBTyxDQUFDO1lBRW5DSixRQUFRQyxHQUFHLENBQUMscUJBQXFCO2dCQUMvQkMsY0FBY0EsZUFBZSxHQUFpQyxPQUE5QkEsYUFBYUssU0FBUyxDQUFDLEdBQUcsS0FBSSxTQUFPO2dCQUNyRUYsZUFBZUEsZ0JBQWdCLEdBQWtDLE9BQS9CQSxjQUFjRSxTQUFTLENBQUMsR0FBRyxLQUFJLFNBQU87Z0JBQ3hFRCxPQUFPQSxRQUFRLEdBQTBCLE9BQXZCQSxNQUFNQyxTQUFTLENBQUMsR0FBRyxLQUFJLFNBQU87WUFDbEQ7WUFFQSwyQkFBMkI7WUFDM0IsSUFBSSxHQUFDeEIsa0JBQUFBLFNBQVN0QixLQUFLLGNBQWRzQixzQ0FBQUEsZ0JBQWdCeUIsSUFBSSxLQUFJO2dCQUMzQnRCLGdCQUFnQjtnQkFDaEJ1QixNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJLENBQUMxQixTQUFTdkIsWUFBWSxFQUFFO2dCQUMxQjBCLGdCQUFnQjtnQkFDaEJ1QixNQUFNO2dCQUNOO1lBQ0Y7WUFFQSwrQkFBK0I7WUFDL0IsSUFBSTFCLFNBQVNwQixTQUFTLEtBQUssVUFBVTtnQkFDbkMsSUFBSSxDQUFDb0IsU0FBU2xCLE9BQU8sRUFBRTtvQkFDckJxQixnQkFBZ0I7b0JBQ2hCdUIsTUFBTTtvQkFDTjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsSUFBSTFCLFNBQVNqQixRQUFRLENBQUM0QixNQUFNLEtBQUssR0FBRztvQkFDbENSLGdCQUFnQjtvQkFDaEJ1QixNQUFNO29CQUNOO2dCQUNGO1lBQ0Y7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSUM7WUFFSixJQUFJM0IsU0FBU3BCLFNBQVMsS0FBSyxVQUFVO2dCQUNuQyxzQkFBc0I7Z0JBQ3RCK0MsYUFBYTtvQkFDWGpELE9BQU9zQixTQUFTdEIsS0FBSztvQkFDckJDLGFBQWFxQixTQUFTckIsV0FBVztvQkFDakNHLFNBQVNrQixTQUFTbEIsT0FBTztvQkFDekJTLFlBQVlTLFNBQVNULFVBQVU7b0JBQy9CRSxVQUFVTyxTQUFTUCxRQUFRO29CQUMzQm1DLFVBQVU1QixTQUFTdkIsWUFBWTtvQkFDL0JpQixjQUFjTSxTQUFTTixZQUFZO29CQUNuQ0MsU0FBU0ssU0FBU0wsT0FBTztnQkFDM0I7Z0JBRUEscUNBQXFDO2dCQUNyQyxJQUFJSyxTQUFTZixjQUFjLEtBQUssVUFBVTtvQkFDeEMwQyxXQUFXRSxTQUFTLEdBQUc7d0JBQ3JCQyxrQkFBa0I5QixTQUFTZCxnQkFBZ0I7d0JBQzNDSSxtQkFBbUJVLFNBQVNWLGlCQUFpQjt3QkFDN0NDLFlBQVlTLFNBQVNULFVBQVU7d0JBQy9CRSxVQUFVTyxTQUFTUCxRQUFRO3dCQUMzQkQsZ0JBQWdCUSxTQUFTUixjQUFjO29CQUN6QztnQkFDRixPQUFPO29CQUNMLGlFQUFpRTtvQkFDakVtQyxXQUFXRSxTQUFTLEdBQUc7d0JBQ3JCQyxrQkFBa0I7NEJBQ2hCM0MsZ0JBQWdCOzRCQUNoQkMsa0JBQWtCOzRCQUNsQkMsZ0JBQWdCO3dCQUNsQjt3QkFDQUMsbUJBQW1CVSxTQUFTVixpQkFBaUI7d0JBQzdDQyxZQUFZUyxTQUFTVCxVQUFVO3dCQUMvQkUsVUFBVU8sU0FBU1AsUUFBUTt3QkFDM0JELGdCQUFnQlEsU0FBU1IsY0FBYztvQkFDekM7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLHFCQUFxQjtnQkFDckIsTUFBTVQsV0FBbUNpQixTQUFTakIsUUFBUSxDQUFDZ0QsR0FBRyxDQUFDQyxDQUFBQTtvQkFDN0QsTUFBTUMsU0FBU2pDLFNBQVNoQixjQUFjLENBQUNnRCxZQUFZO29CQUNuRCxPQUFPO3dCQUNMbEQsU0FBU2tEO3dCQUNUMUMsbUJBQW1CMkMsT0FBTzNDLGlCQUFpQjt3QkFDM0NDLFlBQVkwQyxPQUFPMUMsVUFBVTt3QkFDN0J1QyxrQkFBa0JHLE9BQU8vQyxnQkFBZ0I7d0JBQ3pDUyxTQUFTc0MsT0FBT3RDLE9BQU87b0JBQ3pCO2dCQUNGO2dCQUVBZ0MsYUFBYTtvQkFDWGpELE9BQU9zQixTQUFTdEIsS0FBSztvQkFDckJDLGFBQWFxQixTQUFTckIsV0FBVztvQkFDakNjLFVBQVVPLFNBQVNQLFFBQVE7b0JBQzNCbUMsVUFBVTVCLFNBQVN2QixZQUFZO29CQUMvQmlCLGNBQWNNLFNBQVNOLFlBQVk7b0JBQ25DWCxVQUFVQTtvQkFDVlMsZ0JBQWdCUSxTQUFTUixjQUFjO2dCQUN6QztZQUNGO1lBRUEsNEJBQTRCO1lBQzVCeUIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTWdCLFNBQVMsTUFBTXRFLDRFQUFtQkEsQ0FBQytEO1lBQ3pDVixRQUFRQyxHQUFHLENBQUMsZUFBZWdCO1lBRTNCLHNDQUFzQztZQUN0QyxJQUFJLENBQUNBLE9BQU9DLE9BQU8sRUFBRTtnQkFDbkJsQixRQUFRQyxHQUFHLENBQUMsdUJBQXVCZ0IsT0FBT0UsS0FBSztnQkFDL0NqQyxnQkFBZ0I7Z0JBRWhCLElBQUlrQyxlQUFlSCxPQUFPRSxLQUFLO2dCQUUvQixtREFBbUQ7Z0JBQ25ELElBQUlDLGFBQWFDLFFBQVEsQ0FBQyw4QkFBOEJELGFBQWFDLFFBQVEsQ0FBQyxpQkFBaUI7b0JBQzdGRCxlQUFlO2dCQUNqQixPQUFPLElBQUlBLGFBQWFDLFFBQVEsQ0FBQyxjQUFjRCxhQUFhQyxRQUFRLENBQUMsVUFBVTtvQkFDN0VELGVBQWU7Z0JBQ2pCLE9BQU8sSUFBSUEsYUFBYUMsUUFBUSxDQUFDLCtCQUErQjtvQkFDOUQsbUVBQW1FO29CQUNuRSxNQUFNQyxRQUFRRixhQUFhRSxLQUFLLENBQUM7b0JBQ2pDLElBQUlBLE9BQU87d0JBQ1QsTUFBTUMsWUFBWUQsS0FBSyxDQUFDLEVBQUU7d0JBQzFCLE1BQU1FLFlBQVlGLEtBQUssQ0FBQyxFQUFFO3dCQUMxQkYsZUFBZSxRQUF1RkksT0FBL0VELFdBQVUsdUVBQStFLE9BQVZDLFdBQVU7b0JBQ2xIO2dCQUNGO2dCQUVBLDhCQUE4QjtnQkFDOUJmLE1BQU0sVUFBdUIsT0FBYlc7Z0JBQ2hCcEIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLFFBQVEsc0JBQXNCO1lBQ2hDO1lBRUEsa0NBQWtDO1lBQ2xDRCxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQmdCO1lBRWxDLDRFQUE0RTtZQUM1RSxNQUFNUSxnQkFBZ0JSLE9BQU83QixJQUFJO1lBQ2pDWSxRQUFRQyxHQUFHLENBQUMsd0JBQXdCd0I7WUFFcEMsNENBQTRDO1lBQzVDLElBQUksQ0FBQ0EsaUJBQWlCLENBQUNBLGNBQWNDLEdBQUcsRUFBRTtnQkFDeEMxQixRQUFRbUIsS0FBSyxDQUFDLDBEQUEwREY7Z0JBQ3hFLE1BQU0sSUFBSVUsTUFBTTtZQUNsQjtZQUVBLGlFQUFpRTtZQUNqRSxNQUFNQyxXQUFXLE1BQU1oRiwrRUFBc0JBLENBQUM2RSxjQUFjQyxHQUFHO1lBQy9ELE1BQU1HLEtBQUtELFNBQVNILGFBQWE7WUFDakMsTUFBTUssVUFBVUYsU0FBU0UsT0FBTyxJQUFJLENBQUM7WUFFckMsc0RBQXNEO1lBQ3RELE1BQU1DLG1CQUFtQjtnQkFDdkIsSUFBSUYsR0FBR0csY0FBYyxJQUFJSCxHQUFHSSxRQUFRLEVBQUU7b0JBQ3BDLE1BQU1DLE1BQWEsRUFBRTtvQkFDckJMLEdBQUdJLFFBQVEsQ0FBQ0UsT0FBTyxDQUFDLENBQUNDO3dCQUNuQixNQUFNckIsY0FBY3FCLElBQUlyQixXQUFXLElBQUlxQixJQUFJQyxJQUFJLElBQUk7d0JBQ25ERCxJQUFJRSxTQUFTLENBQUNILE9BQU8sQ0FBQyxDQUFDSTs0QkFDckIsTUFBTUMsSUFBSUQsTUFBTUUsUUFBUSxJQUFJRjs0QkFDNUJMLElBQUlRLElBQUksQ0FBQztnQ0FDUEQsVUFBVUQsRUFBRUcsT0FBTyxJQUFJSCxFQUFFQyxRQUFRLElBQUk7Z0NBQ3JDRyxTQUFTSixFQUFFSSxPQUFPLElBQUksRUFBRTtnQ0FDeEJDLFFBQVFMLEVBQUVLLE1BQU0sSUFBSTtnQ0FDcEJoRixTQUFTa0Q7Z0NBQ1QrQixXQUFXTixFQUFFTSxTQUFTLElBQUksRUFBRTtnQ0FDNUJDLFVBQVVQLEVBQUVPLFFBQVEsSUFBSTtnQ0FDeEJDLE9BQU9SLEVBQUVRLEtBQUssSUFBSSxFQUFFOzRCQUN0Qjt3QkFDRjtvQkFDRjtvQkFDQSxPQUFPZDtnQkFDVDtnQkFDQSxPQUFPLENBQUNMLEdBQUdTLFNBQVMsSUFBSSxFQUFFLEVBQUV4QixHQUFHLENBQUMsQ0FBQzBCO3dCQUl0Qlg7MkJBSmtDO3dCQUMzQ1ksVUFBVUQsRUFBRUcsT0FBTyxJQUFJSCxFQUFFQyxRQUFRLElBQUk7d0JBQ3JDRyxTQUFTSixFQUFFSSxPQUFPLElBQUksRUFBRTt3QkFDeEJDLFFBQVFMLEVBQUVLLE1BQU0sSUFBSTt3QkFDcEJoRixTQUFTZ0UsRUFBQUEsZ0JBQUFBLEdBQUdvQixTQUFTLGNBQVpwQixvQ0FBQUEsY0FBY1EsSUFBSSxLQUFJO3dCQUMvQlMsV0FBV04sRUFBRU0sU0FBUyxJQUFJLEVBQUU7d0JBQzVCQyxVQUFVUCxFQUFFTyxRQUFRLElBQUk7d0JBQ3hCQyxPQUFPUixFQUFFUSxLQUFLLElBQUksRUFBRTtvQkFDdEI7O1lBQ0Y7WUFFQSxNQUFNRSxhQUFhO2dCQUNqQnpGLE9BQU9vRSxHQUFHcEUsS0FBSztnQkFDZkMsYUFBYW1FLEdBQUduRSxXQUFXLElBQUk7Z0JBQy9CYyxVQUFVcUQsR0FBR3JELFFBQVE7Z0JBQ3JCRixZQUFZdUQsR0FBR3ZELFVBQVU7Z0JBQ3pCRyxjQUFjb0QsR0FBR3BELFlBQVksSUFBSTtnQkFDakNGLGdCQUFnQlEsU0FBU1IsY0FBYztnQkFDdkMrRCxXQUFXUDtnQkFDWG9CLFVBQVUsR0FBb0NDLE9BQWpDdkIsR0FBR3BFLEtBQUssQ0FBQzRGLE9BQU8sQ0FBQyxRQUFRLE1BQUssS0FBYyxPQUFYRCxLQUFLRSxHQUFHLElBQUc7Z0JBQ3pEQyxhQUFhekIsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTTyxJQUFJLEtBQUk7Z0JBQzlCbUIsZ0JBQWdCMUIsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTMkIsT0FBTyxLQUFJO1lBQ3RDO1lBRUEsd0RBQXdEO1lBQ3hELE1BQU1DLFNBQVMsTUFBTUMsTUFBTSwyQkFBMkI7Z0JBQ3BEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNkO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDUSxPQUFPTyxFQUFFLEVBQUU7Z0JBQ2QsTUFBTSxJQUFJdEMsTUFBTTtZQUNsQjtZQUVBLE1BQU11QyxVQUFVLE1BQU1SLE9BQU9TLElBQUk7WUFFakMsdUJBQXVCO1lBQ3ZCLE1BQU1DLE1BQU1DLE9BQU9DLEdBQUcsQ0FBQ0MsZUFBZSxDQUFDTDtZQUN2QyxNQUFNTSxPQUFPQyxTQUFTQyxhQUFhLENBQUM7WUFDcENGLEtBQUtHLElBQUksR0FBR1A7WUFDWkksS0FBS0ksUUFBUSxHQUFHMUIsV0FBV0MsUUFBUTtZQUNuQ3NCLFNBQVNYLElBQUksQ0FBQ2UsV0FBVyxDQUFDTDtZQUMxQkEsS0FBS00sS0FBSztZQUNWTCxTQUFTWCxJQUFJLENBQUNpQixXQUFXLENBQUNQO1lBQzFCSCxPQUFPQyxHQUFHLENBQUNVLGVBQWUsQ0FBQ1o7WUFFM0IzRCxNQUFNO1lBRU5ULFFBQVFDLEdBQUcsQ0FBQztZQUVaLGdGQUFnRjtZQUNoRmdGLFdBQVc7Z0JBQ1RqRixRQUFRQyxHQUFHLENBQUM7Z0JBQ1puQixlQUFlO2dCQUNmRSxZQUFZekI7Z0JBQ1oyQixnQkFBZ0I7Z0JBQ2hCYyxRQUFRQyxHQUFHLENBQUM7WUFDZCxHQUFHLE1BQU0sNENBQTRDOztRQUN2RCxFQUFFLE9BQU9rQixPQUFPO1lBQ2RqQyxnQkFBZ0I7WUFFaEIscURBQXFEO1lBQ3JEdUIsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTXlFLGFBQWE7UUFDakIsTUFBTUMsWUFBWTtZQUNoQjtnQkFDRTFILE9BQU87Z0JBQ1AySCxNQUFNO2dCQUNOQyx5QkFDRSw4REFBQ3hJLHVFQUFnQkE7b0JBQ2ZrQyxVQUFVQTtvQkFDVkksZ0JBQWdCQTtvQkFDaEJtRyxRQUFRaEc7b0JBQ1JpRyxRQUFRMUY7b0JBQ1IyRixRQUFRN0Y7b0JBQ1I4RixjQUFjNUcsZ0JBQWdCOzs7Ozs7WUFHcEM7WUFDQTtnQkFDRXBCLE9BQU87Z0JBQ1AySCxNQUFNO2dCQUNOQyx5QkFDRSw4REFBQ2hJLG9HQUErQkE7b0JBQzlCMEIsVUFBVUE7b0JBQ1ZJLGdCQUFnQkE7b0JBQ2hCbUcsUUFBUWhHO29CQUNSaUcsUUFBUTFGO29CQUNSMkYsUUFBUTdGO29CQUNSOEYsY0FBYzVHLGdCQUFnQjs7Ozs7O1lBR3BDO1lBQ0E7Z0JBQ0VwQixPQUFPO2dCQUNQMkgsTUFBTTtnQkFDTkMseUJBQ0UsOERBQUN2SSx5RUFBaUJBO29CQUNoQmlDLFVBQVVBO29CQUNWSSxnQkFBZ0JBO29CQUNoQm1HLFFBQVFoRztvQkFDUmlHLFFBQVExRjtvQkFDUjJGLFFBQVE3RjtvQkFDUjhGLGNBQWM1RyxnQkFBZ0I7Ozs7OztZQUdwQztTQUNEO1FBRUQsZ0VBQWdFO1FBQ2hFLElBQUlFLFNBQVNwQixTQUFTLEtBQUssU0FBUztZQUNsQ3dILFVBQVV6QyxJQUFJLENBQUM7Z0JBQ2JqRixPQUFPO2dCQUNQMkgsTUFBTTtnQkFDTkMseUJBQ0UsOERBQUMvSCxxRkFBc0JBO29CQUNyQnlCLFVBQVVBO29CQUNWSSxnQkFBZ0JBO29CQUNoQm1HLFFBQVFoRztvQkFDUmlHLFFBQVExRjtvQkFDUjJGLFFBQVE3RjtvQkFDUjhGLGNBQWM1RyxnQkFBZ0I7Ozs7OztZQUdwQztRQUNGO1FBRUEsbURBQW1EO1FBQ25ELElBQUlFLFNBQVNwQixTQUFTLEtBQUssVUFBVTtZQUNuQ3dILFVBQVV6QyxJQUFJLENBQ1o7Z0JBQ0VqRixPQUFPO2dCQUNQMkgsTUFBTTtnQkFDTkMseUJBQ0UsOERBQUN0SSw2RUFBbUJBO29CQUNsQmdDLFVBQVVBO29CQUNWSSxnQkFBZ0JBO29CQUNoQm1HLFFBQVFoRztvQkFDUmlHLFFBQVExRjtvQkFDUjJGLFFBQVE3RjtvQkFDUjhGLGNBQWM1RyxnQkFBZ0I7Ozs7OztZQUdwQyxHQUNBO2dCQUNFcEIsT0FBTztnQkFDUDJILE1BQU07Z0JBQ05DLHlCQUNFLDhEQUFDckksaUZBQXFCQTtvQkFDcEIrQixVQUFVQTtvQkFDVkksZ0JBQWdCQTtvQkFDaEJtRyxRQUFRaEc7b0JBQ1JpRyxRQUFRMUY7b0JBQ1IyRixRQUFRN0Y7b0JBQ1I4RixjQUFjNUcsZ0JBQWdCOzs7Ozs7WUFHcEMsR0FDQTtnQkFDRXBCLE9BQU87Z0JBQ1AySCxNQUFNO2dCQUNOQyx5QkFDRSw4REFBQ3BJLG1GQUFzQkE7b0JBQ3JCOEIsVUFBVUE7b0JBQ1ZJLGdCQUFnQkE7b0JBQ2hCbUcsUUFBUWhHO29CQUNSaUcsUUFBUTFGO29CQUNSMkYsUUFBUTdGO29CQUNSOEYsY0FBYzVHLGdCQUFnQjs7Ozs7O1lBR3BDO1FBRUo7UUFFQSxpQ0FBaUM7UUFDakNzRyxVQUFVekMsSUFBSSxDQUNaO1lBQ0VqRixPQUFPO1lBQ1AySCxNQUFNO1lBQ05DLHlCQUNFLDhEQUFDbkksMkVBQWtCQTtnQkFDakI2QixVQUFVQTtnQkFDVkksZ0JBQWdCQTtnQkFDaEJtRyxRQUFRaEc7Z0JBQ1JpRyxRQUFRMUY7Z0JBQ1IyRixRQUFRN0Y7Z0JBQ1I4RixjQUFjNUcsZ0JBQWdCOzs7Ozs7UUFHcEMsR0FDQTtZQUNFcEIsT0FBTztZQUNQMkgsTUFBTTtZQUNOQyx5QkFBVyw4REFBQ2xJLDREQUFXQTtnQkFBQzRCLFVBQVVBO2dCQUFVMkcsVUFBVTNGO2dCQUFjNEYsV0FBVzFHO2dCQUFjdUcsUUFBUTFGOzs7Ozs7UUFDdkc7UUFHRixPQUFPcUY7SUFDVDtJQUVBLE1BQU0xRixRQUFReUY7SUFJZCxxQkFDRSw4REFBQ1U7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUN6SSw4REFBYUE7Z0JBQUN5QixhQUFhQTtnQkFBYVksT0FBT0EsTUFBTXFCLEdBQUcsQ0FBQyxDQUFDZ0YsT0FBVTt3QkFBRXJJLE9BQU9xSSxLQUFLckksS0FBSzt3QkFBRTJILE1BQU1VLEtBQUtWLElBQUk7b0JBQUM7Ozs7OztZQUN6RzNGLEtBQUssQ0FBQ1osWUFBWSxDQUFDd0csU0FBUzs7Ozs7OztBQUduQztHQXZhZ0J6RztLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdGVhY2hlclxccXVlc3Rpb24tcGFwZXItd2l6YXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBjcmVhdGVRdWVzdGlvblBhcGVyLCBDcmVhdGVRdWVzdGlvblBhcGVyRHRvLCBTdWJqZWN0Q29uZmlndXJhdGlvbiwgQ3VzdG9tRGlmZmljdWx0eUNvbmZpZywgZ2V0UXVlc3Rpb25QYXBlckZvclBERiB9IGZyb20gXCJAL2xpYi9hcGkvcXVlc3Rpb25QYXBlcnNcIlxyXG5pbXBvcnQgeyBRdWVzdGlvblR5cGVTdGVwIH0gZnJvbSBcIi4vc3RlcHMvcXVlc3Rpb24tdHlwZS1zdGVwXCJcclxuaW1wb3J0IHsgQ291cnNlU3ViamVjdFN0ZXAgfSBmcm9tIFwiLi9zdGVwcy9jb3Vyc2Utc3ViamVjdC1zdGVwXCJcclxuaW1wb3J0IHsgRGlmZmljdWx0eUxldmVsU3RlcCB9IGZyb20gXCIuL3N0ZXBzL2RpZmZpY3VsdHktbGV2ZWwtc3RlcFwiXHJcbmltcG9ydCB7IFF1ZXN0aW9uU2VsZWN0aW9uU3RlcCB9IGZyb20gXCIuL3N0ZXBzL3F1ZXN0aW9uLXNlbGVjdGlvbi1zdGVwXCJcclxuaW1wb3J0IHsgUGFwZXJDdXN0b21pemF0aW9uU3RlcCB9IGZyb20gXCIuL3N0ZXBzL3BhcGVyLWN1c3RvbWl6YXRpb24tc3RlcFwiXHJcbmltcG9ydCB7IEluY2x1ZGVBbnN3ZXJzU3RlcCB9IGZyb20gXCIuL3N0ZXBzL2luY2x1ZGUtYW5zd2Vycy1zdGVwXCJcclxuaW1wb3J0IHsgQWN0aW9uc1N0ZXAgfSBmcm9tIFwiLi9zdGVwcy9hY3Rpb25zLXN0ZXBcIlxyXG5pbXBvcnQgeyBTdGVwSW5kaWNhdG9yIH0gZnJvbSBcIi4vdWkvc3RlcC1pbmRpY2F0b3JcIlxyXG5pbXBvcnQgeyBRdWVzdGlvblRpdGxlQW5kRGVzY3JpcHRpb25TdGVwIH0gZnJvbSBcIi4vc3RlcHMvcXVlc3Rpb24tdGl0bGUtZGVzY3JpcHRpb24tc3RlcFwiXHJcbmltcG9ydCB7IE11bHRpU3ViamVjdENvbmZpZ1N0ZXAgfSBmcm9tIFwiLi9zdGVwcy9tdWx0aS1zdWJqZWN0LWNvbmZpZy1zdGVwXCJcclxuXHJcbmV4cG9ydCB0eXBlIFN1YmplY3RDb25maWcgPSB7XHJcbiAgc3ViamVjdDogc3RyaW5nXHJcbiAgZGlmZmljdWx0eU1vZGU6IFwiYXV0b1wiIHwgXCJjdXN0b21cIlxyXG4gIGRpZmZpY3VsdHlMZXZlbHM6IEN1c3RvbURpZmZpY3VsdHlDb25maWdcclxuICBudW1iZXJPZlF1ZXN0aW9uczogbnVtYmVyXHJcbiAgdG90YWxNYXJrczogbnVtYmVyXHJcbiAgdG9waWNJZD86IHN0cmluZ1xyXG59XHJcblxyXG5leHBvcnQgdHlwZSBGb3JtRGF0YSA9IHtcclxuICAvLyBRdWVzdGlvblR5cGVTdGVwIC0gbWFwcyB0byBleGFtVHlwZVxyXG4gIHF1ZXN0aW9uVHlwZTogc3RyaW5nXHJcbiAgLy8gUXVlc3Rpb25UaXRsZUFuZERlc2NyaXB0aW9uU3RlcCAtIG1hcHMgdG8gdGl0bGUsIGRlc2NyaXB0aW9uXHJcbiAgdGl0bGU6IHN0cmluZ1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcclxuICAvLyBDb3Vyc2VTdWJqZWN0U3RlcCAtIG1hcHMgdG8gc3ViamVjdCBvciBzdWJqZWN0c1xyXG4gIHBhcGVyTW9kZTogXCJzaW5nbGVcIiB8IFwibXVsdGlcIiAvLyBOZXcgZmllbGQgdG8gZGV0ZXJtaW5lIHNpbmdsZSB2cyBtdWx0aS1zdWJqZWN0XHJcbiAgY291cnNlOiBzdHJpbmdcclxuICBzdWJqZWN0OiBzdHJpbmcgLy8gRm9yIHNpbmdsZSBzdWJqZWN0IG1vZGVcclxuICBzdWJqZWN0czogc3RyaW5nW10gLy8gRm9yIG11bHRpLXN1YmplY3QgbW9kZSAtIHNlbGVjdGVkIHN1YmplY3QgbmFtZXNcclxuICBzdWJqZWN0Q29uZmlnczogUmVjb3JkPHN0cmluZywgU3ViamVjdENvbmZpZz4gLy8gUGVyLXN1YmplY3QgY29uZmlndXJhdGlvbnNcclxuICAvLyBEaWZmaWN1bHR5TGV2ZWxTdGVwIC0gbWFwcyB0byBjdXN0b21pc2UuY3VzdG9tRGlmZmljdWx0eSBvciBhdXRvIChmb3Igc2luZ2xlIHN1YmplY3QpXHJcbiAgZGlmZmljdWx0eU1vZGU6IFwiYXV0b1wiIHwgXCJjdXN0b21cIlxyXG4gIGRpZmZpY3VsdHlMZXZlbHM6IEN1c3RvbURpZmZpY3VsdHlDb25maWdcclxuICAvLyBRdWVzdGlvblNlbGVjdGlvblN0ZXAgLSBtYXBzIHRvIGN1c3RvbWlzZS5udW1iZXJPZlF1ZXN0aW9ucyAoZm9yIHNpbmdsZSBzdWJqZWN0KVxyXG4gIG51bWJlck9mUXVlc3Rpb25zOiBudW1iZXJcclxuICAvLyBQYXBlckN1c3RvbWl6YXRpb25TdGVwIC0gbWFwcyB0byB0b3RhbE1hcmtzIGFuZCBjdXN0b21pc2UudG90YWxNYXJrcyAoZm9yIHNpbmdsZSBzdWJqZWN0KVxyXG4gIHRvdGFsTWFya3M6IG51bWJlclxyXG4gIC8vIEluY2x1ZGVBbnN3ZXJzU3RlcCAtIG1hcHMgdG8gY3VzdG9taXNlLmluY2x1ZGVBbnN3ZXJzIG9yIGluY2x1ZGVBbnN3ZXJzXHJcbiAgaW5jbHVkZUFuc3dlcnM6IGJvb2xlYW5cclxuICAvLyBBZGRpdGlvbmFsIGZpZWxkcyBmb3IgQVBJXHJcbiAgZHVyYXRpb246IG51bWJlciAvLyBEZWZhdWx0IDYwIG1pbnV0ZXMgKDEgaG91cilcclxuICBpbnN0cnVjdGlvbnM6IHN0cmluZ1xyXG4gIHRvcGljSWQ/OiBzdHJpbmdcclxufVxyXG5cclxuY29uc3QgaW5pdGlhbEZvcm1EYXRhOiBGb3JtRGF0YSA9IHtcclxuICBxdWVzdGlvblR5cGU6IFwiXCIsXHJcbiAgdGl0bGU6IFwiXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiXCIsXHJcbiAgcGFwZXJNb2RlOiBcInNpbmdsZVwiLFxyXG4gIGNvdXJzZTogXCJcIixcclxuICBzdWJqZWN0OiBcIlwiLFxyXG4gIHN1YmplY3RzOiBbXSxcclxuICBzdWJqZWN0Q29uZmlnczoge30sXHJcbiAgZGlmZmljdWx0eU1vZGU6IFwiYXV0b1wiLFxyXG4gIGRpZmZpY3VsdHlMZXZlbHM6IHtcclxuICAgIGVhc3lQZXJjZW50YWdlOiAzMCxcclxuICAgIG1lZGl1bVBlcmNlbnRhZ2U6IDUwLFxyXG4gICAgaGFyZFBlcmNlbnRhZ2U6IDIwLFxyXG4gIH0sXHJcbiAgbnVtYmVyT2ZRdWVzdGlvbnM6IDEsXHJcbiAgdG90YWxNYXJrczogMTAwLFxyXG4gIGluY2x1ZGVBbnN3ZXJzOiBmYWxzZSwgLy8gRGVmYXVsdCB0byBmYWxzZSBhcyBwZXIgcmVxdWlyZW1lbnRzXHJcbiAgZHVyYXRpb246IDYwLCAvLyBEZWZhdWx0IDEgaG91ciBpbiBtaW51dGVzXHJcbiAgaW5zdHJ1Y3Rpb25zOiBcIlwiLFxyXG4gIHRvcGljSWQ6IHVuZGVmaW5lZCxcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFF1ZXN0aW9uUGFwZXJXaXphcmQoKSB7XHJcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgwKVxyXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8Rm9ybURhdGE+KGluaXRpYWxGb3JtRGF0YSlcclxuICBjb25zdCBbaXNHZW5lcmF0aW5nLCBzZXRJc0dlbmVyYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpXHJcblxyXG4gIGNvbnN0IHVwZGF0ZUZvcm1EYXRhID0gKGRhdGE6IFBhcnRpYWw8Rm9ybURhdGE+KSA9PiB7XHJcbiAgICBzZXRGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgLi4uZGF0YSB9KSlcclxuICB9XHJcblxyXG4gIGNvbnN0IG5leHRTdGVwID0gKCkgPT4ge1xyXG4gICAgc2V0Q3VycmVudFN0ZXAoKHByZXYpID0+IE1hdGgubWluKHByZXYgKyAxLCBzdGVwcy5sZW5ndGggLSAxKSlcclxuICB9XHJcblxyXG4gIGNvbnN0IHByZXZTdGVwID0gKCkgPT4ge1xyXG4gICAgc2V0Q3VycmVudFN0ZXAoKHByZXYpID0+IE1hdGgubWF4KHByZXYgLSAxLCAwKSlcclxuICB9XHJcblxyXG4gIGNvbnN0IHNraXBTdGVwID0gKCkgPT4ge1xyXG4gICAgbmV4dFN0ZXAoKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZ29Ub0ZpcnN0U3RlcCA9ICgpID0+IHtcclxuICAgIHNldEN1cnJlbnRTdGVwKDApXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoaXNHZW5lcmF0aW5nKSByZXR1cm47IC8vIFByZXZlbnQgbXVsdGlwbGUgc3VibWlzc2lvbnNcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0dlbmVyYXRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiU3VibWl0dGluZyBkYXRhOlwiLCBmb3JtRGF0YSlcclxuXHJcbiAgICAgIC8vIERlYnVnOiBDaGVjayBhdmFpbGFibGUgdG9rZW5zXHJcbiAgICAgIGNvbnN0IGJhY2tlbmRUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xyXG4gICAgICBjb25zdCBmaXJlYmFzZVRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJmaXJlYmFzZVRva2VuXCIpO1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidG9rZW5cIik7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkF2YWlsYWJsZSB0b2tlbnM6XCIsIHtcclxuICAgICAgICBiYWNrZW5kVG9rZW46IGJhY2tlbmRUb2tlbiA/IGAke2JhY2tlbmRUb2tlbi5zdWJzdHJpbmcoMCwgMjApfS4uLmAgOiAnTm9uZScsXHJcbiAgICAgICAgZmlyZWJhc2VUb2tlbjogZmlyZWJhc2VUb2tlbiA/IGAke2ZpcmViYXNlVG9rZW4uc3Vic3RyaW5nKDAsIDIwKX0uLi5gIDogJ05vbmUnLFxyXG4gICAgICAgIHRva2VuOiB0b2tlbiA/IGAke3Rva2VuLnN1YnN0cmluZygwLCAyMCl9Li4uYCA6ICdOb25lJ1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xyXG4gICAgICBpZiAoIWZvcm1EYXRhLnRpdGxlPy50cmltKCkpIHtcclxuICAgICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xyXG4gICAgICAgIGFsZXJ0KFwiUGxlYXNlIGVudGVyIGEgdGl0bGUgZm9yIHRoZSBxdWVzdGlvbiBwYXBlclwiKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghZm9ybURhdGEucXVlc3Rpb25UeXBlKSB7XHJcbiAgICAgICAgc2V0SXNHZW5lcmF0aW5nKGZhbHNlKTtcclxuICAgICAgICBhbGVydChcIlBsZWFzZSBzZWxlY3QgYW4gZXhhbSB0eXBlXCIpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmFsaWRhdGUgYmFzZWQgb24gcGFwZXIgbW9kZVxyXG4gICAgICBpZiAoZm9ybURhdGEucGFwZXJNb2RlID09PSBcInNpbmdsZVwiKSB7XHJcbiAgICAgICAgaWYgKCFmb3JtRGF0YS5zdWJqZWN0KSB7XHJcbiAgICAgICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xyXG4gICAgICAgICAgYWxlcnQoXCJQbGVhc2Ugc2VsZWN0IGEgc3ViamVjdFwiKTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgaWYgKGZvcm1EYXRhLnN1YmplY3RzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgc2V0SXNHZW5lcmF0aW5nKGZhbHNlKTtcclxuICAgICAgICAgIGFsZXJ0KFwiUGxlYXNlIHNlbGVjdCBhdCBsZWFzdCBvbmUgc3ViamVjdFwiKTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFByZXBhcmUgdGhlIEFQSSBwYXlsb2FkXHJcbiAgICAgIGxldCBhcGlQYXlsb2FkOiBDcmVhdGVRdWVzdGlvblBhcGVyRHRvO1xyXG5cclxuICAgICAgaWYgKGZvcm1EYXRhLnBhcGVyTW9kZSA9PT0gXCJzaW5nbGVcIikge1xyXG4gICAgICAgIC8vIFNpbmdsZSBzdWJqZWN0IG1vZGVcclxuICAgICAgICBhcGlQYXlsb2FkID0ge1xyXG4gICAgICAgICAgdGl0bGU6IGZvcm1EYXRhLnRpdGxlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGZvcm1EYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgc3ViamVjdDogZm9ybURhdGEuc3ViamVjdCxcclxuICAgICAgICAgIHRvdGFsTWFya3M6IGZvcm1EYXRhLnRvdGFsTWFya3MsXHJcbiAgICAgICAgICBkdXJhdGlvbjogZm9ybURhdGEuZHVyYXRpb24sXHJcbiAgICAgICAgICBleGFtVHlwZTogZm9ybURhdGEucXVlc3Rpb25UeXBlLFxyXG4gICAgICAgICAgaW5zdHJ1Y3Rpb25zOiBmb3JtRGF0YS5pbnN0cnVjdGlvbnMsXHJcbiAgICAgICAgICB0b3BpY0lkOiBmb3JtRGF0YS50b3BpY0lkLFxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQWRkIGN1c3RvbWl6YXRpb24gaWYgbm90IGF1dG8gbW9kZVxyXG4gICAgICAgIGlmIChmb3JtRGF0YS5kaWZmaWN1bHR5TW9kZSA9PT0gXCJjdXN0b21cIikge1xyXG4gICAgICAgICAgYXBpUGF5bG9hZC5jdXN0b21pc2UgPSB7XHJcbiAgICAgICAgICAgIGN1c3RvbURpZmZpY3VsdHk6IGZvcm1EYXRhLmRpZmZpY3VsdHlMZXZlbHMsXHJcbiAgICAgICAgICAgIG51bWJlck9mUXVlc3Rpb25zOiBmb3JtRGF0YS5udW1iZXJPZlF1ZXN0aW9ucyxcclxuICAgICAgICAgICAgdG90YWxNYXJrczogZm9ybURhdGEudG90YWxNYXJrcyxcclxuICAgICAgICAgICAgZHVyYXRpb246IGZvcm1EYXRhLmR1cmF0aW9uLFxyXG4gICAgICAgICAgICBpbmNsdWRlQW5zd2VyczogZm9ybURhdGEuaW5jbHVkZUFuc3dlcnMsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIEZvciBhdXRvIG1vZGUsIHN0aWxsIGluY2x1ZGUgY3VzdG9taXphdGlvbiB3aXRoIGRlZmF1bHQgdmFsdWVzXHJcbiAgICAgICAgICBhcGlQYXlsb2FkLmN1c3RvbWlzZSA9IHtcclxuICAgICAgICAgICAgY3VzdG9tRGlmZmljdWx0eToge1xyXG4gICAgICAgICAgICAgIGVhc3lQZXJjZW50YWdlOiAzMCxcclxuICAgICAgICAgICAgICBtZWRpdW1QZXJjZW50YWdlOiA1MCxcclxuICAgICAgICAgICAgICBoYXJkUGVyY2VudGFnZTogMjAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG51bWJlck9mUXVlc3Rpb25zOiBmb3JtRGF0YS5udW1iZXJPZlF1ZXN0aW9ucyxcclxuICAgICAgICAgICAgdG90YWxNYXJrczogZm9ybURhdGEudG90YWxNYXJrcyxcclxuICAgICAgICAgICAgZHVyYXRpb246IGZvcm1EYXRhLmR1cmF0aW9uLFxyXG4gICAgICAgICAgICBpbmNsdWRlQW5zd2VyczogZm9ybURhdGEuaW5jbHVkZUFuc3dlcnMsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIE11bHRpLXN1YmplY3QgbW9kZVxyXG4gICAgICAgIGNvbnN0IHN1YmplY3RzOiBTdWJqZWN0Q29uZmlndXJhdGlvbltdID0gZm9ybURhdGEuc3ViamVjdHMubWFwKHN1YmplY3ROYW1lID0+IHtcclxuICAgICAgICAgIGNvbnN0IGNvbmZpZyA9IGZvcm1EYXRhLnN1YmplY3RDb25maWdzW3N1YmplY3ROYW1lXTtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHN1YmplY3Q6IHN1YmplY3ROYW1lLFxyXG4gICAgICAgICAgICBudW1iZXJPZlF1ZXN0aW9uczogY29uZmlnLm51bWJlck9mUXVlc3Rpb25zLFxyXG4gICAgICAgICAgICB0b3RhbE1hcmtzOiBjb25maWcudG90YWxNYXJrcyxcclxuICAgICAgICAgICAgY3VzdG9tRGlmZmljdWx0eTogY29uZmlnLmRpZmZpY3VsdHlMZXZlbHMsXHJcbiAgICAgICAgICAgIHRvcGljSWQ6IGNvbmZpZy50b3BpY0lkLFxyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgYXBpUGF5bG9hZCA9IHtcclxuICAgICAgICAgIHRpdGxlOiBmb3JtRGF0YS50aXRsZSxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBmb3JtRGF0YS5kZXNjcmlwdGlvbixcclxuICAgICAgICAgIGR1cmF0aW9uOiBmb3JtRGF0YS5kdXJhdGlvbixcclxuICAgICAgICAgIGV4YW1UeXBlOiBmb3JtRGF0YS5xdWVzdGlvblR5cGUsXHJcbiAgICAgICAgICBpbnN0cnVjdGlvbnM6IGZvcm1EYXRhLmluc3RydWN0aW9ucyxcclxuICAgICAgICAgIHN1YmplY3RzOiBzdWJqZWN0cyxcclxuICAgICAgICAgIGluY2x1ZGVBbnN3ZXJzOiBmb3JtRGF0YS5pbmNsdWRlQW5zd2VycyxcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENyZWF0ZSB0aGUgcXVlc3Rpb24gcGFwZXJcclxuICAgICAgY29uc29sZS5sb2coXCJDYWxsaW5nIGNyZWF0ZVF1ZXN0aW9uUGFwZXIgQVBJLi4uXCIpO1xyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjcmVhdGVRdWVzdGlvblBhcGVyKGFwaVBheWxvYWQpXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQVBJIHJlc3VsdDpcIiwgcmVzdWx0KTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIHRoZSByZXF1ZXN0IHdhcyBzdWNjZXNzZnVsXHJcbiAgICAgIGlmICghcmVzdWx0LnN1Y2Nlc3MpIHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIkFQSSByZXR1cm5lZCBlcnJvcjpcIiwgcmVzdWx0LmVycm9yKTtcclxuICAgICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xyXG5cclxuICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gcmVzdWx0LmVycm9yO1xyXG5cclxuICAgICAgICAvLyBIYW5kbGUgc3BlY2lmaWMgZXJyb3IgdHlwZXMgd2l0aCBiZXR0ZXIgbWVzc2FnZXNcclxuICAgICAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIikgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiVW5hdXRob3JpemVkXCIpKSB7XHJcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIlBsZWFzZSBsb2cgaW4gYWdhaW4gdG8gY29udGludWUuIFlvdXIgc2Vzc2lvbiBtYXkgaGF2ZSBleHBpcmVkLlwiO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiTmV0d29ya1wiKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJmZXRjaFwiKSkge1xyXG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gXCJQbGVhc2UgY2hlY2sgeW91ciBpbnRlcm5ldCBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uXCI7XHJcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJ1bnVzZWQgcXVlc3Rpb25zIGF2YWlsYWJsZVwiKSkge1xyXG4gICAgICAgICAgLy8gRXh0cmFjdCBudW1iZXJzIGZyb20gdGhlIGVycm9yIG1lc3NhZ2UgZm9yIGEgY2xlYXJlciBleHBsYW5hdGlvblxyXG4gICAgICAgICAgY29uc3QgbWF0Y2ggPSBlcnJvck1lc3NhZ2UubWF0Y2goL09ubHkgKFxcZCspIHVudXNlZCBxdWVzdGlvbnMgYXZhaWxhYmxlXFwuIFJlcXVlc3RlZDogKFxcZCspLyk7XHJcbiAgICAgICAgICBpZiAobWF0Y2gpIHtcclxuICAgICAgICAgICAgY29uc3QgYXZhaWxhYmxlID0gbWF0Y2hbMV07XHJcbiAgICAgICAgICAgIGNvbnN0IHJlcXVlc3RlZCA9IG1hdGNoWzJdO1xyXG4gICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBgT25seSAke2F2YWlsYWJsZX0gcXVlc3Rpb25zIGFyZSBhdmFpbGFibGUgZm9yIHRoaXMgc3ViamVjdC90b3BpYywgYnV0IHlvdSByZXF1ZXN0ZWQgJHtyZXF1ZXN0ZWR9IHF1ZXN0aW9ucy4gUGxlYXNlIHJlZHVjZSB0aGUgbnVtYmVyIG9mIHF1ZXN0aW9ucyBvciBhZGQgbW9yZSBxdWVzdGlvbnMgdG8gdGhlIGRhdGFiYXNlLmA7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBTaG93IGVycm9yIG1lc3NhZ2UgaW4gYWxlcnRcclxuICAgICAgICBhbGVydChgRXJyb3I6ICR7ZXJyb3JNZXNzYWdlfWApO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiU3RheWluZyBvbiBjdXJyZW50IHN0ZXAgZHVlIHRvIGVycm9yXCIpO1xyXG4gICAgICAgIHJldHVybjsgLy8gRXhpdCBlYXJseSBvbiBlcnJvclxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTdWNjZXNzIC0gcHJvY2VlZCB3aXRoIGRvd25sb2FkXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQVBJIHN1Y2Nlc3MhIFByb2NlZWRpbmcgd2l0aCBkb3dubG9hZC4uLlwiKTtcclxuICAgICAgY29uc29sZS5sb2coXCJGdWxsIEFQSSByZXNwb25zZTpcIiwgcmVzdWx0KTtcclxuXHJcbiAgICAgIC8vIEFmdGVyIHRoZSBBUEkgZml4LCByZXN1bHQuZGF0YSBzaG91bGQgY29udGFpbiB0aGUgcXVlc3Rpb24gcGFwZXIgZGlyZWN0bHlcclxuICAgICAgY29uc3QgcXVlc3Rpb25QYXBlciA9IHJlc3VsdC5kYXRhO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlF1ZXN0aW9uIHBhcGVyIGRhdGE6XCIsIHF1ZXN0aW9uUGFwZXIpO1xyXG5cclxuICAgICAgLy8gVmFsaWRhdGUgdGhhdCB3ZSBoYXZlIGEgcXVlc3Rpb24gcGFwZXIgSURcclxuICAgICAgaWYgKCFxdWVzdGlvblBhcGVyIHx8ICFxdWVzdGlvblBhcGVyLl9pZCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJObyBxdWVzdGlvbiBwYXBlciBJRCBmb3VuZCBpbiByZXNwb25zZS4gRnVsbCByZXNwb25zZTpcIiwgcmVzdWx0KTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJRdWVzdGlvbiBwYXBlciB3YXMgY3JlYXRlZCBidXQgbm8gSUQgd2FzIHJldHVybmVkLiBQbGVhc2UgY2hlY2sgdGhlIGNvbnNvbGUgZm9yIGRldGFpbHMgYW5kIHRyeSBhZ2Fpbi5cIik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZldGNoIGNvbXBsZXRlIHBhcGVyIHdpdGggY29sbGVnZS9zdWJqZWN0IGluZm8gZm9yIFBERiBwYXlsb2FkXHJcbiAgICAgIGNvbnN0IGZ1bGxEYXRhID0gYXdhaXQgZ2V0UXVlc3Rpb25QYXBlckZvclBERihxdWVzdGlvblBhcGVyLl9pZCk7XHJcbiAgICAgIGNvbnN0IHFwID0gZnVsbERhdGEucXVlc3Rpb25QYXBlcjtcclxuICAgICAgY29uc3QgY29sbGVnZSA9IGZ1bGxEYXRhLmNvbGxlZ2UgfHwge307XHJcblxyXG4gICAgICAvLyBGbGF0dGVuIHF1ZXN0aW9ucyB3aXRoIHN1YmplY3QgbmFtZXMgYW5kIGltYWdlIGRhdGFcclxuICAgICAgY29uc3QgZmxhdHRlblF1ZXN0aW9ucyA9ICgpID0+IHtcclxuICAgICAgICBpZiAocXAuaXNNdWx0aVN1YmplY3QgJiYgcXAuc2VjdGlvbnMpIHtcclxuICAgICAgICAgIGNvbnN0IGFycjogYW55W10gPSBbXTtcclxuICAgICAgICAgIHFwLnNlY3Rpb25zLmZvckVhY2goKHNlYzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHN1YmplY3ROYW1lID0gc2VjLnN1YmplY3ROYW1lIHx8IHNlYy5uYW1lIHx8IFwiXCI7XHJcbiAgICAgICAgICAgIHNlYy5xdWVzdGlvbnMuZm9yRWFjaCgocXdyYXA6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHEgPSBxd3JhcC5xdWVzdGlvbiB8fCBxd3JhcDtcclxuICAgICAgICAgICAgICBhcnIucHVzaCh7XHJcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogcS5jb250ZW50IHx8IHEucXVlc3Rpb24gfHwgXCJcIixcclxuICAgICAgICAgICAgICAgIG9wdGlvbnM6IHEub3B0aW9ucyB8fCBbXSxcclxuICAgICAgICAgICAgICAgIGFuc3dlcjogcS5hbnN3ZXIgfHwgXCJcIixcclxuICAgICAgICAgICAgICAgIHN1YmplY3Q6IHN1YmplY3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgaW1hZ2VVcmxzOiBxLmltYWdlVXJscyB8fCBbXSwgLy8gSW5jbHVkZSBpbWFnZSBkYXRhIGZvciBQREYgZ2VuZXJhdGlvblxyXG4gICAgICAgICAgICAgICAgc29sdXRpb246IHEuc29sdXRpb24gfHwgbnVsbCxcclxuICAgICAgICAgICAgICAgIGhpbnRzOiBxLmhpbnRzIHx8IFtdLFxyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgcmV0dXJuIGFycjtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIChxcC5xdWVzdGlvbnMgfHwgW10pLm1hcCgocTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgcXVlc3Rpb246IHEuY29udGVudCB8fCBxLnF1ZXN0aW9uIHx8IFwiXCIsXHJcbiAgICAgICAgICBvcHRpb25zOiBxLm9wdGlvbnMgfHwgW10sXHJcbiAgICAgICAgICBhbnN3ZXI6IHEuYW5zd2VyIHx8IFwiXCIsXHJcbiAgICAgICAgICBzdWJqZWN0OiBxcC5zdWJqZWN0SWQ/Lm5hbWUgfHwgXCJcIixcclxuICAgICAgICAgIGltYWdlVXJsczogcS5pbWFnZVVybHMgfHwgW10sIC8vIEluY2x1ZGUgaW1hZ2UgZGF0YSBmb3IgUERGIGdlbmVyYXRpb25cclxuICAgICAgICAgIHNvbHV0aW9uOiBxLnNvbHV0aW9uIHx8IG51bGwsXHJcbiAgICAgICAgICBoaW50czogcS5oaW50cyB8fCBbXSxcclxuICAgICAgICB9KSk7XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBwZGZQYXlsb2FkID0ge1xyXG4gICAgICAgIHRpdGxlOiBxcC50aXRsZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogcXAuZGVzY3JpcHRpb24gfHwgXCJcIixcclxuICAgICAgICBkdXJhdGlvbjogcXAuZHVyYXRpb24sXHJcbiAgICAgICAgdG90YWxNYXJrczogcXAudG90YWxNYXJrcyxcclxuICAgICAgICBpbnN0cnVjdGlvbnM6IHFwLmluc3RydWN0aW9ucyB8fCBcIlwiLFxyXG4gICAgICAgIGluY2x1ZGVBbnN3ZXJzOiBmb3JtRGF0YS5pbmNsdWRlQW5zd2VycyxcclxuICAgICAgICBxdWVzdGlvbnM6IGZsYXR0ZW5RdWVzdGlvbnMoKSxcclxuICAgICAgICBmaWxlbmFtZTogYCR7cXAudGl0bGUucmVwbGFjZSgvXFxzKy9nLCAnXycpfV8ke0RhdGUubm93KCl9LnBkZmAsXHJcbiAgICAgICAgY29sbGVnZU5hbWU6IGNvbGxlZ2U/Lm5hbWUgfHwgXCJcIixcclxuICAgICAgICBjb2xsZWdlTG9nb1VybDogY29sbGVnZT8ubG9nb1VybCB8fCBcIlwiLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gQ2FsbCBpbnRlcm5hbCBBUEkgcm91dGUgdG8gZ2VuZXJhdGUgUERGIHZpYSBQdXBwZXRlZXJcclxuICAgICAgY29uc3QgcGRmUmVzID0gYXdhaXQgZmV0Y2goXCIvYXBpL2dlbmVyYXRlLXBhcGVyLXBkZlwiLCB7XHJcbiAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcclxuICAgICAgICBoZWFkZXJzOiB7IFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocGRmUGF5bG9hZCksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFwZGZSZXMub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJTZXJ2ZXItc2lkZSBQREYgZ2VuZXJhdGlvbiBmYWlsZWRcIik7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHBkZkJsb2IgPSBhd2FpdCBwZGZSZXMuYmxvYigpO1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIGRvd25sb2FkIGxpbmtcclxuICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwocGRmQmxvYilcclxuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKVxyXG4gICAgICBsaW5rLmhyZWYgPSB1cmxcclxuICAgICAgbGluay5kb3dubG9hZCA9IHBkZlBheWxvYWQuZmlsZW5hbWVcclxuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKVxyXG4gICAgICBsaW5rLmNsaWNrKClcclxuICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKVxyXG4gICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpXHJcblxyXG4gICAgICBhbGVydChcIlF1ZXN0aW9uIHBhcGVyIGdlbmVyYXRlZCBhbmQgZG93bmxvYWRlZCBzdWNjZXNzZnVsbHkhXCIpO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coXCJTdWNjZXNzISBBYm91dCB0byByZWRpcmVjdCB0byBmaXJzdCBzdGVwIGluIDEgc2Vjb25kLi4uXCIpO1xyXG5cclxuICAgICAgLy8gUmVzZXQgdG8gZmlyc3Qgc3RlcCBhbmQgY2xlYXIgZm9ybSBkYXRhIGFmdGVyIGEgc2hvcnQgZGVsYXkgKG9ubHkgb24gc3VjY2VzcylcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJSZWRpcmVjdGluZyB0byBmaXJzdCBzdGVwIG5vdy4uLlwiKTtcclxuICAgICAgICBzZXRDdXJyZW50U3RlcCgwKVxyXG4gICAgICAgIHNldEZvcm1EYXRhKGluaXRpYWxGb3JtRGF0YSlcclxuICAgICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpXHJcbiAgICAgICAgY29uc29sZS5sb2coXCJSZWRpcmVjdCBjb21wbGV0ZWQuIEN1cnJlbnQgc3RlcCBzaG91bGQgYmUgMFwiKTtcclxuICAgICAgfSwgMTAwMCkgLy8gMSBzZWNvbmQgZGVsYXkgdG8gZW5zdXJlIGFsZXJ0IGlzIHZpc2libGVcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHNldElzR2VuZXJhdGluZyhmYWxzZSlcclxuXHJcbiAgICAgIC8vIEhhbmRsZSBhbnkgdW5leHBlY3RlZCBlcnJvcnMgKGxpa2UgbmV0d29yayBpc3N1ZXMpXHJcbiAgICAgIGFsZXJ0KFwiRXJyb3I6IEFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQuIFBsZWFzZSB0cnkgYWdhaW4uXCIpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQnVpbGQgc3RlcHMgYXJyYXkgZHluYW1pY2FsbHkgYmFzZWQgb24gcGFwZXIgbW9kZVxyXG4gIGNvbnN0IGJ1aWxkU3RlcHMgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBiYXNlU3RlcHMgPSBbXHJcbiAgICAgIHtcclxuICAgICAgICB0aXRsZTogXCJRdWVzdGlvbiBUeXBlXCIsXHJcbiAgICAgICAgaWNvbjogXCJIZWxwQ2lyY2xlXCIsXHJcbiAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICA8UXVlc3Rpb25UeXBlU3RlcFxyXG4gICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XHJcbiAgICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX1cclxuICAgICAgICAgICAgb25OZXh0PXtuZXh0U3RlcH1cclxuICAgICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgICAgYmFja0Rpc2FibGVkPXtjdXJyZW50U3RlcCA9PT0gMH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHRpdGxlOiBcIlBhcGVyIERldGFpbHNcIixcclxuICAgICAgICBpY29uOiBcIkZpbGVUZXh0XCIsXHJcbiAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICA8UXVlc3Rpb25UaXRsZUFuZERlc2NyaXB0aW9uU3RlcFxyXG4gICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XHJcbiAgICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX1cclxuICAgICAgICAgICAgb25OZXh0PXtuZXh0U3RlcH1cclxuICAgICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgICAgYmFja0Rpc2FibGVkPXtjdXJyZW50U3RlcCA9PT0gMH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKSxcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHRpdGxlOiBcIkNvdXJzZSAmIFN1YmplY3QgU2VsZWN0aW9uXCIsXHJcbiAgICAgICAgaWNvbjogXCJCb29rT3BlblwiLFxyXG4gICAgICAgIGNvbXBvbmVudDogKFxyXG4gICAgICAgICAgPENvdXJzZVN1YmplY3RTdGVwXHJcbiAgICAgICAgICAgIGZvcm1EYXRhPXtmb3JtRGF0YX1cclxuICAgICAgICAgICAgdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfVxyXG4gICAgICAgICAgICBvbk5leHQ9e25leHRTdGVwfVxyXG4gICAgICAgICAgICBvblNraXA9e3NraXBTdGVwfVxyXG4gICAgICAgICAgICBvbkJhY2s9e3ByZXZTdGVwfVxyXG4gICAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICApLFxyXG4gICAgICB9LFxyXG4gICAgXVxyXG5cclxuICAgIC8vIEFkZCBtdWx0aS1zdWJqZWN0IGNvbmZpZ3VyYXRpb24gc3RlcCBpZiBpbiBtdWx0aS1zdWJqZWN0IG1vZGVcclxuICAgIGlmIChmb3JtRGF0YS5wYXBlck1vZGUgPT09IFwibXVsdGlcIikge1xyXG4gICAgICBiYXNlU3RlcHMucHVzaCh7XHJcbiAgICAgICAgdGl0bGU6IFwiQ29uZmlndXJlIFN1YmplY3RzXCIsXHJcbiAgICAgICAgaWNvbjogXCJTZXR0aW5nc1wiLFxyXG4gICAgICAgIGNvbXBvbmVudDogKFxyXG4gICAgICAgICAgPE11bHRpU3ViamVjdENvbmZpZ1N0ZXBcclxuICAgICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxyXG4gICAgICAgICAgICB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9XHJcbiAgICAgICAgICAgIG9uTmV4dD17bmV4dFN0ZXB9XHJcbiAgICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICAgIG9uQmFjaz17cHJldlN0ZXB9XHJcbiAgICAgICAgICAgIGJhY2tEaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICksXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8gQWRkIHJlbWFpbmluZyBzdGVwcyBvbmx5IGZvciBzaW5nbGUgc3ViamVjdCBtb2RlXHJcbiAgICBpZiAoZm9ybURhdGEucGFwZXJNb2RlID09PSBcInNpbmdsZVwiKSB7XHJcbiAgICAgIGJhc2VTdGVwcy5wdXNoKFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHRpdGxlOiBcIlNlbGVjdCBEaWZmaWN1bHR5IExldmVsXCIsXHJcbiAgICAgICAgICBpY29uOiBcIkJhckNoYXJ0MlwiLFxyXG4gICAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICAgIDxEaWZmaWN1bHR5TGV2ZWxTdGVwXHJcbiAgICAgICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxyXG4gICAgICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX1cclxuICAgICAgICAgICAgICBvbk5leHQ9e25leHRTdGVwfVxyXG4gICAgICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHRpdGxlOiBcIlF1ZXN0aW9uIFNlbGVjdGlvbiBDcml0ZXJpYVwiLFxyXG4gICAgICAgICAgaWNvbjogXCJGaWxlVGV4dFwiLFxyXG4gICAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICAgIDxRdWVzdGlvblNlbGVjdGlvblN0ZXBcclxuICAgICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XHJcbiAgICAgICAgICAgICAgdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfVxyXG4gICAgICAgICAgICAgIG9uTmV4dD17bmV4dFN0ZXB9XHJcbiAgICAgICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgICAgICBvbkJhY2s9e3ByZXZTdGVwfVxyXG4gICAgICAgICAgICAgIGJhY2tEaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdGl0bGU6IFwiUGFwZXIgQ3VzdG9taXphdGlvblwiLFxyXG4gICAgICAgICAgaWNvbjogXCJGaWxlRWRpdFwiLFxyXG4gICAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICAgIDxQYXBlckN1c3RvbWl6YXRpb25TdGVwXHJcbiAgICAgICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxyXG4gICAgICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX1cclxuICAgICAgICAgICAgICBvbk5leHQ9e25leHRTdGVwfVxyXG4gICAgICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICB9XHJcbiAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICAvLyBBZGQgZmluYWwgc3RlcHMgZm9yIGJvdGggbW9kZXNcclxuICAgIGJhc2VTdGVwcy5wdXNoKFxyXG4gICAgICB7XHJcbiAgICAgICAgdGl0bGU6IFwiSW5jbHVkZSBBbnN3ZXJzP1wiLFxyXG4gICAgICAgIGljb246IFwiQ2hlY2tTcXVhcmVcIixcclxuICAgICAgICBjb21wb25lbnQ6IChcclxuICAgICAgICAgIDxJbmNsdWRlQW5zd2Vyc1N0ZXBcclxuICAgICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxyXG4gICAgICAgICAgICB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9XHJcbiAgICAgICAgICAgIG9uTmV4dD17bmV4dFN0ZXB9XHJcbiAgICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICAgIG9uQmFjaz17cHJldlN0ZXB9XHJcbiAgICAgICAgICAgIGJhY2tEaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICksXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICB0aXRsZTogXCJHZW5lcmF0ZSBQYXBlclwiLFxyXG4gICAgICAgIGljb246IFwiRmlsZU91dHB1dFwiLFxyXG4gICAgICAgIGNvbXBvbmVudDogPEFjdGlvbnNTdGVwIGZvcm1EYXRhPXtmb3JtRGF0YX0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gaXNMb2FkaW5nPXtpc0dlbmVyYXRpbmd9IG9uQmFjaz17Z29Ub0ZpcnN0U3RlcH0gLz4sXHJcbiAgICAgIH1cclxuICAgIClcclxuXHJcbiAgICByZXR1cm4gYmFzZVN0ZXBzXHJcbiAgfVxyXG5cclxuICBjb25zdCBzdGVwcyA9IGJ1aWxkU3RlcHMoKVxyXG5cclxuXHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICA8U3RlcEluZGljYXRvciBjdXJyZW50U3RlcD17Y3VycmVudFN0ZXB9IHN0ZXBzPXtzdGVwcy5tYXAoKHN0ZXApID0+ICh7IHRpdGxlOiBzdGVwLnRpdGxlLCBpY29uOiBzdGVwLmljb24gfSkpfSAvPlxyXG4gICAgICB7c3RlcHNbY3VycmVudFN0ZXBdLmNvbXBvbmVudH1cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJjcmVhdGVRdWVzdGlvblBhcGVyIiwiZ2V0UXVlc3Rpb25QYXBlckZvclBERiIsIlF1ZXN0aW9uVHlwZVN0ZXAiLCJDb3Vyc2VTdWJqZWN0U3RlcCIsIkRpZmZpY3VsdHlMZXZlbFN0ZXAiLCJRdWVzdGlvblNlbGVjdGlvblN0ZXAiLCJQYXBlckN1c3RvbWl6YXRpb25TdGVwIiwiSW5jbHVkZUFuc3dlcnNTdGVwIiwiQWN0aW9uc1N0ZXAiLCJTdGVwSW5kaWNhdG9yIiwiUXVlc3Rpb25UaXRsZUFuZERlc2NyaXB0aW9uU3RlcCIsIk11bHRpU3ViamVjdENvbmZpZ1N0ZXAiLCJpbml0aWFsRm9ybURhdGEiLCJxdWVzdGlvblR5cGUiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicGFwZXJNb2RlIiwiY291cnNlIiwic3ViamVjdCIsInN1YmplY3RzIiwic3ViamVjdENvbmZpZ3MiLCJkaWZmaWN1bHR5TW9kZSIsImRpZmZpY3VsdHlMZXZlbHMiLCJlYXN5UGVyY2VudGFnZSIsIm1lZGl1bVBlcmNlbnRhZ2UiLCJoYXJkUGVyY2VudGFnZSIsIm51bWJlck9mUXVlc3Rpb25zIiwidG90YWxNYXJrcyIsImluY2x1ZGVBbnN3ZXJzIiwiZHVyYXRpb24iLCJpbnN0cnVjdGlvbnMiLCJ0b3BpY0lkIiwidW5kZWZpbmVkIiwiUXVlc3Rpb25QYXBlcldpemFyZCIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwidXBkYXRlRm9ybURhdGEiLCJkYXRhIiwicHJldiIsIm5leHRTdGVwIiwiTWF0aCIsIm1pbiIsInN0ZXBzIiwibGVuZ3RoIiwicHJldlN0ZXAiLCJtYXgiLCJza2lwU3RlcCIsImdvVG9GaXJzdFN0ZXAiLCJoYW5kbGVTdWJtaXQiLCJjb25zb2xlIiwibG9nIiwiYmFja2VuZFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImZpcmViYXNlVG9rZW4iLCJ0b2tlbiIsInN1YnN0cmluZyIsInRyaW0iLCJhbGVydCIsImFwaVBheWxvYWQiLCJleGFtVHlwZSIsImN1c3RvbWlzZSIsImN1c3RvbURpZmZpY3VsdHkiLCJtYXAiLCJzdWJqZWN0TmFtZSIsImNvbmZpZyIsInJlc3VsdCIsInN1Y2Nlc3MiLCJlcnJvciIsImVycm9yTWVzc2FnZSIsImluY2x1ZGVzIiwibWF0Y2giLCJhdmFpbGFibGUiLCJyZXF1ZXN0ZWQiLCJxdWVzdGlvblBhcGVyIiwiX2lkIiwiRXJyb3IiLCJmdWxsRGF0YSIsInFwIiwiY29sbGVnZSIsImZsYXR0ZW5RdWVzdGlvbnMiLCJpc011bHRpU3ViamVjdCIsInNlY3Rpb25zIiwiYXJyIiwiZm9yRWFjaCIsInNlYyIsIm5hbWUiLCJxdWVzdGlvbnMiLCJxd3JhcCIsInEiLCJxdWVzdGlvbiIsInB1c2giLCJjb250ZW50Iiwib3B0aW9ucyIsImFuc3dlciIsImltYWdlVXJscyIsInNvbHV0aW9uIiwiaGludHMiLCJzdWJqZWN0SWQiLCJwZGZQYXlsb2FkIiwiZmlsZW5hbWUiLCJEYXRlIiwicmVwbGFjZSIsIm5vdyIsImNvbGxlZ2VOYW1lIiwiY29sbGVnZUxvZ29VcmwiLCJsb2dvVXJsIiwicGRmUmVzIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsInBkZkJsb2IiLCJibG9iIiwidXJsIiwid2luZG93IiwiVVJMIiwiY3JlYXRlT2JqZWN0VVJMIiwibGluayIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImhyZWYiLCJkb3dubG9hZCIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInJldm9rZU9iamVjdFVSTCIsInNldFRpbWVvdXQiLCJidWlsZFN0ZXBzIiwiYmFzZVN0ZXBzIiwiaWNvbiIsImNvbXBvbmVudCIsIm9uTmV4dCIsIm9uU2tpcCIsIm9uQmFjayIsImJhY2tEaXNhYmxlZCIsIm9uU3VibWl0IiwiaXNMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3RlcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});