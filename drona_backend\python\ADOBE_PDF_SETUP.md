# Adobe PDF Extract API Integration Guide (REST API)

This guide explains how to integrate Adobe PDF Extract API using REST API calls with your existing PDF Question Extraction system for enhanced image and text extraction.

## Overview

The integration adds Adobe PDF Extract API as the **primary extraction method** for images and structured text, while keeping Mistral AI OCR and Gemini AI as fallback and analysis providers.

### Extraction Pipeline

```
PDF Upload → Adobe PDF Extract REST API → Mistral OCR (fallback) → Gemini AI Analysis → JSON Response
```

## Prerequisites

### 1. Adobe PDF Services Account

1. **Sign up** for Adobe PDF Services at [Adobe Developer Console](https://developer.adobe.com/document-services/apis/pdf-extract/)
2. **Create a new project** and add PDF Extract API
3. **Download credentials**:
   - `pdfservices-api-credentials.json` (your credentials file)
   - Note: No private key file needed for REST API approach

### 2. Existing Requirements

Ensure you have the existing setup:
- Gemini AI API key (environment variable `GEMINI_API_KEY`)
- Mistral AI API key (in `key.txt` file)

## Installation

### 1. Install Required Dependencies

```bash
cd drona_backend/python
pip install requests  # For REST API calls
```

### 2. Place Adobe Credentials

Place your Adobe credentials in the Python backend directory:

```bash
# Copy your downloaded credentials file to the backend directory
cp /path/to/your/pdfservices-api-credentials.json drona_backend/python/
# Note: No private key file needed for REST API approach
```

### 3. Install Updated Dependencies

```bash
pip install -r requirements.txt
```

## Configuration

### File Structure

Your `drona_backend/python/` directory should now contain:

```
drona_backend/python/
├── pdfservices-api-credentials.json  # Adobe credentials (NEW)
├── gemini_key.txt                    # Gemini API key
├── key.txt                          # Mistral API key
├── adobe_pdf_extractor.py           # Adobe REST API integration (NEW)
├── enhanced_question_extractor.py   # Enhanced extractor (NEW)
├── question_extractor.py            # Original extractor
├── api_server.py                    # Updated API server
└── requirements.txt                 # Updated dependencies
```

### Environment Variables

Set up your environment variables:

```bash
# Windows
set GEMINI_API_KEY=your_gemini_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_gemini_api_key_here
```

## Usage

### API Server

Start the enhanced API server:

```bash
python api_server.py
```

### API Requests

The API now supports enhanced extraction:

```bash
# Use enhanced extraction (default - includes Adobe PDF Extract API)
curl -X POST -F "file=@document.pdf" http://localhost:5000/api/extract

# Use enhanced extraction explicitly
curl -X POST -F "file=@document.pdf" -F "use_enhanced=true" http://localhost:5000/api/extract

# Use standard extraction (Mistral OCR + Gemini AI only)
curl -X POST -F "file=@document.pdf" -F "use_enhanced=false" http://localhost:5000/api/extract

# Specify AI provider with enhanced extraction
curl -X POST -F "file=@document.pdf" -F "ai_provider=gemini" -F "use_enhanced=true" http://localhost:5000/api/extract
```

### Direct Usage

```python
from enhanced_question_extractor import EnhancedQuestionExtractor

# Create enhanced extractor
extractor = EnhancedQuestionExtractor(
    ai_provider='gemini',
    adobe_credentials_file='pdfservices-api-credentials.json'
)

# Extract with enhanced method
result = extractor.extract_comprehensive_data_enhanced('document.pdf')
questions = result['questions']
metadata = result['extraction_metadata']

print(f"Adobe used: {metadata['adobe_used']}")
print(f"Mistral used: {metadata['mistral_used']}")
print(f"Extraction method: {metadata['extraction_method']}")
```

## Features

### Enhanced Image Extraction

- **High-Quality Images**: Adobe PDF Extract API provides superior image extraction
- **Structured Data**: Images are mapped to their locations and context
- **Multiple Formats**: Supports PNG, JPEG, and other image formats
- **Base64 Conversion**: Automatic conversion to base64 for frontend compatibility

### Intelligent Fallback System

1. **Primary**: Adobe PDF Extract API (best quality)
2. **Fallback**: Mistral AI OCR (if Adobe fails)
3. **Supplement**: Combines both when needed

### Multi-Provider Analysis

- **Adobe**: Structured text and image extraction
- **Mistral**: OCR fallback and supplementary text
- **Gemini**: AI-powered question analysis and JSON generation

## Extraction Methods

### Method Selection Logic

```python
# The system automatically chooses the best extraction method:

if adobe_credentials_available:
    try:
        # Use Adobe PDF Extract API
        adobe_data = extract_with_adobe(pdf)
        
        if adobe_data_insufficient:
            # Supplement with Mistral OCR
            mistral_data = extract_with_mistral(pdf)
            combined_data = combine(adobe_data, mistral_data)
        else:
            combined_data = adobe_data
            
    except adobe_error:
        # Fallback to Mistral OCR
        combined_data = extract_with_mistral(pdf)
else:
    # Use standard Mistral OCR
    combined_data = extract_with_mistral(pdf)

# Analyze with Gemini AI
questions = analyze_with_gemini(combined_data)
```

### Data Combination Strategy

- **Text**: Prefer Adobe text, fallback to Mistral
- **Images**: Combine Adobe and Mistral images (no duplicates)
- **Metadata**: Track which providers were used

## Performance Comparison

| Feature | Adobe PDF Extract | Mistral OCR | Combined |
|---------|------------------|-------------|----------|
| **Image Quality** | ✅ Excellent | ✅ Good | ✅ Best |
| **Text Accuracy** | ✅ High | ✅ High | ✅ Highest |
| **Structured Data** | ✅ Yes | ❌ No | ✅ Yes |
| **Processing Speed** | ⚡ Fast | ⚡ Fast | ⚡ Fast |
| **Cost** | 💰 Per API call | 💰 Per API call | 💰 Combined |
| **Reliability** | ✅ High | ✅ High | ✅ Highest |

## Troubleshooting

### Common Issues

1. **Adobe Credentials Not Found**:
   ```
   Error: Adobe credentials file not found: pdfservices-api-credentials.json
   ```
   **Solution**: Ensure `pdfservices-api-credentials.json` is in the correct directory.

2. **Adobe SDK Not Installed**:
   ```
   ImportError: No module named 'adobe.pdfservices'
   ```
   **Solution**: Install the SDK with `pip install pdfservices-sdk`.

3. **Private Key Issues**:
   ```
   Error: Could not load private key
   ```
   **Solution**: Ensure `private.key` file is in the same directory as credentials.

4. **API Quota Exceeded**:
   ```
   Error: Adobe API quota exceeded
   ```
   **Solution**: Check your Adobe Developer Console for usage limits.

### Verification

Test the integration:

```bash
# Test enhanced extractor
python -c "
from enhanced_question_extractor import EnhancedQuestionExtractor
extractor = EnhancedQuestionExtractor()
caps = extractor.get_extraction_capabilities()
print('Capabilities:', caps)
"

# Test API server
curl http://localhost:5000/
```

### Logs and Monitoring

The enhanced system provides detailed logging:

```
🔧 [ENHANCED_INIT] Initializing Adobe PDF Extractor...
✅ [ENHANCED_INIT] Adobe PDF Extractor initialized successfully
📄 [ENHANCED_EXTRACT] Attempting Adobe PDF extraction...
✅ [ENHANCED_EXTRACT] Adobe extraction completed in 3.45s
📝 [COMBINE] Using Adobe text: 15,234 characters
🖼️ [COMBINE] Added 12 Adobe images
📊 [COMBINE] Sources used: adobe_text, adobe_images
```

## Migration from Standard Extraction

### Gradual Migration

1. **Test Phase**: Use `use_enhanced=true` parameter for testing
2. **Validation**: Compare results with standard extraction
3. **Full Migration**: Make enhanced extraction the default

### Backward Compatibility

- Standard extraction remains available with `use_enhanced=false`
- All existing API endpoints work unchanged
- Fallback to standard extraction if Adobe fails

## Cost Optimization

### Adobe API Usage

- Adobe PDF Extract API charges per document processed
- Monitor usage in Adobe Developer Console
- Consider caching results for frequently processed documents

### Hybrid Strategy

```python
# Use Adobe for complex documents, Mistral for simple ones
if document_complexity > threshold:
    use_enhanced = True  # Adobe + Mistral + Gemini
else:
    use_enhanced = False  # Mistral + Gemini only
```

## Support

For issues:

1. **Adobe Issues**: Check Adobe Developer Console and documentation
2. **Integration Issues**: Check server logs for detailed error messages
3. **API Issues**: Verify all credentials and API keys are correctly configured

## Next Steps

1. **Test the integration** with your existing PDFs
2. **Monitor performance** and extraction quality
3. **Adjust settings** based on your specific use cases
4. **Scale up** once satisfied with results

The enhanced system provides the best of all worlds: Adobe's superior extraction capabilities, Mistral's reliable OCR fallback, and Gemini's excellent AI analysis.
