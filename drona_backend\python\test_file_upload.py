#!/usr/bin/env python3
"""
Test file upload functionality for the clean API
"""

import requests
import os

def test_file_upload():
    """Test file upload to the clean API"""
    
    # Test health check first
    try:
        print("🔍 Testing API health...")
        response = requests.get("http://localhost:5000/")
        if response.status_code == 200:
            print("✅ API is running")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False
    
    # Check if test PDF exists
    test_pdf = "python/maths.pdf"
    if not os.path.exists(test_pdf):
        print(f"❌ Test PDF not found: {test_pdf}")
        print("Please add a PDF file to test with")
        return False
    
    print(f"📄 Testing file upload with: {test_pdf}")
    
    try:
        # Test file upload
        with open(test_pdf, 'rb') as f:
            files = {'file': (os.path.basename(test_pdf), f, 'application/pdf')}
            print("🔄 Uploading file...")
            response = requests.post("http://localhost:5000/api/extract", files=files)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ File upload and processing successful!")
            print(f"📝 Questions extracted: {result.get('questions_count', 0)}")
            print(f"⏱️ Processing time: {result.get('processing_time', 0)}s")
            
            # Show sample question
            questions = result.get('data', [])
            if questions and len(questions) > 0:
                q = questions[0]
                print(f"\n📋 Sample question:")
                print(f"  Text: {q.get('question', '')[:100]}...")
                print(f"  Options: {len(q.get('options', {}))}")
                print(f"  Answer: {q.get('answer', 'N/A')}")
                print(f"  Images: {'✓' if q.get('imageUrl') else '✗'}")
                print(f"  Solution: {'✓' if q.get('solution', {}).get('steps') else '✗'}")
                print(f"  Hints: {'✓' if q.get('hints') else '✗'}")
            
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test failed: {e}")
        return False

def test_api_endpoints():
    """Test all API endpoints"""
    print("🧪 Testing Clean PDF Question Extraction API")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get("http://localhost:5000/")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {data.get('status')}")
            print(f"   Message: {data.get('message')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test file upload
    print("\n2. Testing file upload...")
    success = test_file_upload()
    
    # Test invalid requests
    print("\n3. Testing error handling...")
    
    # Test no file
    try:
        response = requests.post("http://localhost:5000/api/extract")
        if response.status_code == 400:
            print("✅ No file error handled correctly")
        else:
            print(f"⚠️ Unexpected response for no file: {response.status_code}")
    except Exception as e:
        print(f"❌ No file test error: {e}")
    
    # Test invalid file type
    try:
        files = {'file': ('test.txt', b'test content', 'text/plain')}
        response = requests.post("http://localhost:5000/api/extract", files=files)
        if response.status_code == 400:
            print("✅ Invalid file type error handled correctly")
        else:
            print(f"⚠️ Unexpected response for invalid file: {response.status_code}")
    except Exception as e:
        print(f"❌ Invalid file test error: {e}")
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS PASSED - File upload is working!")
    else:
        print("❌ Some tests failed - Check the issues above")
    print("=" * 50)

if __name__ == "__main__":
    test_api_endpoints()
