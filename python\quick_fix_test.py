#!/usr/bin/env python3
"""
Quick Fix Test Script

This script provides immediate fixes for the PDF extraction issue
and tests them to ensure they work properly.
"""

import os
import sys
import json
import time
import requests

def test_api_with_enhanced_settings(pdf_path, server_url="http://localhost:5000"):
    """
    Test the API with enhanced settings for better extraction
    """
    print(f"🚀 Testing enhanced PDF extraction...")
    print(f"📄 File: {os.path.basename(pdf_path)}")
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return False
    
    file_size = os.path.getsize(pdf_path)
    file_size_mb = file_size / (1024 * 1024)
    print(f"📊 Size: {file_size_mb:.1f}MB")
    
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
            data = {'ai_provider': 'gemini'}  # Use Gemini AI
            
            print(f"🔄 Uploading and processing with enhanced extraction...")
            
            # Extended timeout for large files
            timeout = max(1800, file_size_mb * 30)  # At least 30 minutes, or 30 seconds per MB
            print(f"⏰ Timeout set to: {timeout} seconds ({timeout/60:.1f} minutes)")
            
            start_time = time.time()
            
            response = requests.post(
                f"{server_url}/api/extract",
                files=files,
                data=data,
                timeout=timeout
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                questions_count = result.get('questions_count', 0)
                if 'data' in result:
                    actual_count = len(result['data'])
                    print(f"✅ SUCCESS! Extracted {actual_count} questions in {processing_time:.1f}s")
                    
                    if actual_count < 50:
                        print(f"⚠️ WARNING: Only {actual_count} questions extracted")
                        print(f"   This may indicate extraction issues")
                        
                        # Show diagnostic info
                        if 'performance_metrics' in result:
                            metrics = result['performance_metrics']
                            print(f"📊 Performance Metrics:")
                            print(f"   OCR duration: {metrics.get('ocr_duration', 0):.1f}s")
                            print(f"   AI duration: {metrics.get('ai_duration', 0):.1f}s")
                            print(f"   Text length: {metrics.get('ocr_text_chars', 0):,} chars")
                    
                    # Show sample questions
                    print(f"\n📝 Sample Questions:")
                    for i, question in enumerate(result['data'][:3]):
                        q_text = question.get('question', question.get('content', 'No text'))[:100]
                        print(f"   {i+1}. {q_text}...")
                    
                    return True
                else:
                    print(f"❌ No questions data in response")
                    return False
            else:
                print(f"❌ API Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   Response: {response.text[:200]}...")
                return False
                
    except requests.exceptions.Timeout:
        print(f"⏰ Request timed out after {timeout} seconds")
        print(f"   For very large files, this may be normal")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def apply_quick_fixes():
    """
    Apply quick fixes to the extraction system
    """
    print("🔧 Applying quick fixes to improve extraction...")
    
    fixes_applied = []
    
    # Fix 1: Update chunking threshold in question_extractor.py
    try:
        extractor_path = "question_extractor.py"
        if os.path.exists(extractor_path):
            with open(extractor_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if enhanced chunking is already applied
            if '_process_large_text_in_chunks_enhanced' in content:
                print("✅ Enhanced chunking already applied")
                fixes_applied.append("Enhanced chunking")
            else:
                print("⚠️ Enhanced chunking not found - manual update needed")
        
    except Exception as e:
        print(f"❌ Error checking extractor: {e}")
    
    # Fix 2: Check API server timeout settings
    try:
        api_path = "api_server.py"
        if os.path.exists(api_path):
            print("✅ API server file found")
            fixes_applied.append("API server available")
        
    except Exception as e:
        print(f"❌ Error checking API server: {e}")
    
    print(f"🎯 Fixes applied: {len(fixes_applied)}")
    for fix in fixes_applied:
        print(f"   ✅ {fix}")
    
    return len(fixes_applied) > 0

def provide_immediate_recommendations():
    """
    Provide immediate recommendations for fixing the extraction issue
    """
    print("\n💡 IMMEDIATE RECOMMENDATIONS:")
    print("=" * 50)
    
    print("1. 🔧 QUICK FIXES TO TRY:")
    print("   a) Restart the Python API server (python api_server.py)")
    print("   b) Use Gemini AI instead of Mistral (ai_provider='gemini')")
    print("   c) Increase timeout to 30+ minutes for large files")
    print("   d) Check PDF quality - ensure it contains actual text, not just images")
    
    print("\n2. 📊 DIAGNOSTIC STEPS:")
    print("   a) Run: python pdf_diagnostic.py your_file.pdf")
    print("   b) Run: python chunking_test.py your_file.pdf")
    print("   c) Check server logs for chunking progress")
    
    print("\n3. 🚨 COMMON ISSUES:")
    print("   a) PDF contains scanned images without OCR text")
    print("   b) Questions are in non-standard format")
    print("   c) AI model hitting token limits")
    print("   d) Chunking strategy too conservative")
    print("   e) Deduplication removing valid questions")
    
    print("\n4. 🎯 IMMEDIATE ACTIONS:")
    print("   a) Test with a smaller PDF first (10-20 pages)")
    print("   b) Check if questions are in recognizable format")
    print("   c) Verify OCR is extracting text properly")
    print("   d) Use enhanced chunking with smaller chunks")
    
    print("\n5. 🔄 IF STILL FAILING:")
    print("   a) Try different AI providers")
    print("   b) Implement custom pattern matching")
    print("   c) Preprocess PDF with better OCR")
    print("   d) Split large PDF into smaller sections")

def main():
    """Main function"""
    print("🚀 Quick Fix Test Script for PDF Extraction")
    print("=" * 50)
    
    if len(sys.argv) != 2:
        print("Usage: python quick_fix_test.py <path_to_pdf>")
        print("Example: python quick_fix_test.py large_document.pdf")
        print("\nThis script will:")
        print("1. Apply quick fixes to the extraction system")
        print("2. Test the extraction with your PDF")
        print("3. Provide specific recommendations")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # Step 1: Apply quick fixes
    print("🔧 Step 1: Applying quick fixes...")
    fixes_applied = apply_quick_fixes()
    
    # Step 2: Test extraction
    print("\n🧪 Step 2: Testing extraction...")
    success = test_api_with_enhanced_settings(pdf_path)
    
    # Step 3: Provide recommendations
    provide_immediate_recommendations()
    
    # Summary
    print(f"\n🎯 SUMMARY:")
    if success:
        print("✅ Extraction test PASSED")
        print("   The fixes appear to be working")
    else:
        print("❌ Extraction test FAILED")
        print("   Additional troubleshooting needed")
        print("   Follow the recommendations above")
    
    print(f"\n📞 NEXT STEPS:")
    if not success:
        print("1. Run diagnostic tools: python pdf_diagnostic.py your_file.pdf")
        print("2. Test chunking strategies: python chunking_test.py your_file.pdf")
        print("3. Check server logs for detailed error information")
        print("4. Try with a smaller test PDF first")
    else:
        print("1. Test with your full 300-400 question PDF")
        print("2. Monitor extraction progress in server logs")
        print("3. Verify all questions are properly formatted")

if __name__ == "__main__":
    main()
