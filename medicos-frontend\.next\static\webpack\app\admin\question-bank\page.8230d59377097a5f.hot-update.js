"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/ui/text-with-images.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/text-with-images.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextWithImages: () => (/* binding */ TextWithImages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _base64_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _math_text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./math-text */ \"(app-pages-browser)/./src/components/ui/math-text.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\nfunction TextWithImages(param) {\n    let { text, className, imageClassName, maxImageWidth = 300, maxImageHeight = 200, questionImages } = param;\n    const { cleanText, images } = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.extractImagesFromText)(text, questionImages);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"space-y-3\", className),\n        children: [\n            cleanText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-base\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_math_text__WEBPACK_IMPORTED_MODULE_4__.MathText, {\n                    text: cleanText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this),\n            images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                            src: image.src,\n                            alt: image.alt,\n                            className: imageClassName,\n                            maxWidth: maxImageWidth,\n                            maxHeight: maxImageHeight\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, this)\n                    }, image.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\text-with-images.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c = TextWithImages;\nvar _c;\n$RefreshReg$(_c, \"TextWithImages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/text-with-images.tsx\n"));

/***/ })

});