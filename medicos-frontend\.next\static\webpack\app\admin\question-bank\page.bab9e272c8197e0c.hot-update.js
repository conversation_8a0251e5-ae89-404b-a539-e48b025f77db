"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Used to force refetch when a question is deleted\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Helper function to process question content and replace image references with actual images\n    const processQuestionContent = (content, imageUrls)=>{\n        if (!content || !imageUrls || imageUrls.length === 0) {\n            return content || '';\n        }\n        let processedContent = content;\n        // Replace image tags or references with actual image URLs\n        // Pattern 1: Replace <img> tags with src placeholders\n        processedContent = processedContent.replace(/<img[^>]*src=[\"']([^\"']*)[\"'][^>]*>/gi, (match, src)=>{\n            // Try to find matching image in imageUrls array\n            const matchingImage = imageUrls.find((url)=>url.includes(src) || src.includes(url) || // Try to match by index if src is a number or index reference\n                src.match(/\\d+/) && imageUrls[parseInt(src.match(/\\d+/)[0]) - 1]);\n            if (matchingImage) {\n                return '<img src=\"'.concat(matchingImage, '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 2: Replace image references like ![image](reference) with actual images\n        processedContent = processedContent.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (match, alt, ref)=>{\n            const matchingImage = imageUrls.find((url)=>url.includes(ref) || ref.includes(url) || // Try to match by index\n                ref.match(/\\d+/) && imageUrls[parseInt(ref.match(/\\d+/)[0]) - 1]);\n            if (matchingImage) {\n                return '<img src=\"'.concat(matchingImage, '\" alt=\"').concat(alt || 'Question Image', '\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 3: If no specific references found but we have images, append the first image\n        if (!processedContent.includes('<img') && !processedContent.includes('![') && imageUrls.length > 0) {\n            // Check if content has any image-related keywords or if it's likely missing an image\n            const hasImageKeywords = /image|figure|diagram|chart|graph|picture/i.test(processedContent);\n            if (hasImageKeywords || processedContent.length < 100) {\n                processedContent += '\\n<img src=\"'.concat(imageUrls[0], '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n        }\n        return processedContent;\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        try {\n            var _q_topicId;\n            let parsedOptions = [];\n            // Ensure options is an array and filter out null/undefined values\n            const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n            if (safeOptions.length > 0) {\n                if (typeof safeOptions[0] === 'string') {\n                    // Check if it's a single comma-separated string or an array of individual strings\n                    if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                        // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                        const optionTexts = safeOptions[0].split(',');\n                        parsedOptions = optionTexts.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    } else {\n                        // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                        parsedOptions = safeOptions.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    }\n                } else {\n                    // If options is already an array of objects\n                    parsedOptions = safeOptions.map((opt, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                            imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                        }));\n                }\n            } else {\n                // Log warning for questions without valid options\n                console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n                // Fallback: create empty options if none exist\n                parsedOptions = [\n                    {\n                        label: 'a',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'b',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'c',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'd',\n                        text: 'No options available'\n                    }\n                ];\n            }\n            return {\n                id: q._id,\n                subject: q.subjectId.name,\n                topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n                text: processQuestionContent(q.content, q.imageUrls || []),\n                options: parsedOptions,\n                difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n                correctAnswer: q.answer,\n                reviewStatus: q.reviewStatus,\n                solution: q.solution,\n                hints: q.hints\n            };\n        } catch (error) {\n            var _q_subjectId, _q_topicId1;\n            console.error(\"Error formatting question \".concat(q._id, \":\"), error, q);\n            // Return a fallback question structure\n            return {\n                id: q._id || 'unknown',\n                subject: ((_q_subjectId = q.subjectId) === null || _q_subjectId === void 0 ? void 0 : _q_subjectId.name) || 'Unknown Subject',\n                topic: ((_q_topicId1 = q.topicId) === null || _q_topicId1 === void 0 ? void 0 : _q_topicId1.name) || 'No Topic',\n                text: processQuestionContent(q.content || 'Error loading question content', q.imageUrls || []),\n                options: [\n                    {\n                        label: 'a',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'b',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'c',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'd',\n                        text: 'Error loading options'\n                    }\n                ],\n                difficulty: q.difficulty || 'Unknown',\n                correctAnswer: q.answer || 'a',\n                reviewStatus: q.reviewStatus || 'pending',\n                solution: q.solution,\n                hints: q.hints\n            };\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"HQdOYEmIZrqalo67hGXpqfFK1Yc=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});