"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Used to force refetch when a question is deleted\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Helper function to process question content and replace image references with actual images\n    const processQuestionContent = (content, imageUrls)=>{\n        if (!content || !imageUrls || imageUrls.length === 0) {\n            return content || '';\n        }\n        let processedContent = content;\n        // Replace image tags or references with actual image URLs\n        // Pattern 1: Replace <img> tags with src placeholders\n        processedContent = processedContent.replace(/<img[^>]*src=[\"']([^\"']*)[\"'][^>]*>/gi, (match, src)=>{\n            // Try to find matching image in imageUrls array\n            const matchingImage = imageUrls.find((url)=>url.includes(src) || src.includes(url) || // Try to match by index if src is a number or index reference\n                src.match(/\\d+/) && imageUrls[parseInt(src.match(/\\d+/)[0]) - 1]);\n            if (matchingImage) {\n                return '<img src=\"'.concat(matchingImage, '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 2: Replace image references like ![image](reference) with actual images\n        processedContent = processedContent.replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (match, alt, ref)=>{\n            const matchingImage = imageUrls.find((url)=>url.includes(ref) || ref.includes(url) || // Try to match by index\n                ref.match(/\\d+/) && imageUrls[parseInt(ref.match(/\\d+/)[0]) - 1]);\n            if (matchingImage) {\n                return '<img src=\"'.concat(matchingImage, '\" alt=\"').concat(alt || 'Question Image', '\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n            return match;\n        });\n        // Pattern 3: If no specific references found but we have images, append the first image\n        if (!processedContent.includes('<img') && !processedContent.includes('![') && imageUrls.length > 0) {\n            // Check if content has any image-related keywords or if it's likely missing an image\n            const hasImageKeywords = /image|figure|diagram|chart|graph|picture/i.test(processedContent);\n            if (hasImageKeywords || processedContent.length < 100) {\n                processedContent += '\\n<img src=\"'.concat(imageUrls[0], '\" alt=\"Question Image\" style=\"max-width: 300px; height: auto; display: block; margin: 10px auto;\" />');\n            }\n        }\n        return processedContent;\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        try {\n            var _q_topicId;\n            let parsedOptions = [];\n            // Ensure options is an array and filter out null/undefined values\n            const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n            if (safeOptions.length > 0) {\n                if (typeof safeOptions[0] === 'string') {\n                    // Check if it's a single comma-separated string or an array of individual strings\n                    if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                        // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                        const optionTexts = safeOptions[0].split(',');\n                        parsedOptions = optionTexts.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    } else {\n                        // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                        parsedOptions = safeOptions.map((text, index)=>{\n                            const trimmedText = text.trim();\n                            // Check if the text is a base64 image\n                            if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                                return {\n                                    label: String.fromCharCode(97 + index),\n                                    text: '',\n                                    imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                    isImageOption: true\n                                };\n                            }\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: trimmedText\n                            };\n                        });\n                    }\n                } else {\n                    // If options is already an array of objects\n                    parsedOptions = safeOptions.map((opt, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                            imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                        }));\n                }\n            } else {\n                // Log warning for questions without valid options\n                console.warn(\"Question \".concat(q._id, \" has no valid options:\"), q.options);\n                // Fallback: create empty options if none exist\n                parsedOptions = [\n                    {\n                        label: 'a',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'b',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'c',\n                        text: 'No options available'\n                    },\n                    {\n                        label: 'd',\n                        text: 'No options available'\n                    }\n                ];\n            }\n            return {\n                id: q._id,\n                subject: q.subjectId.name,\n                topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n                text: q.content,\n                options: parsedOptions,\n                difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n                correctAnswer: q.answer,\n                reviewStatus: q.reviewStatus,\n                solution: q.solution,\n                hints: q.hints\n            };\n        } catch (error) {\n            var _q_subjectId, _q_topicId1;\n            console.error(\"Error formatting question \".concat(q._id, \":\"), error, q);\n            // Return a fallback question structure\n            return {\n                id: q._id || 'unknown',\n                subject: ((_q_subjectId = q.subjectId) === null || _q_subjectId === void 0 ? void 0 : _q_subjectId.name) || 'Unknown Subject',\n                topic: ((_q_topicId1 = q.topicId) === null || _q_topicId1 === void 0 ? void 0 : _q_topicId1.name) || 'No Topic',\n                text: q.content || 'Error loading question content',\n                options: [\n                    {\n                        label: 'a',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'b',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'c',\n                        text: 'Error loading options'\n                    },\n                    {\n                        label: 'd',\n                        text: 'Error loading options'\n                    }\n                ],\n                difficulty: q.difficulty || 'Unknown',\n                correctAnswer: q.answer || 'a',\n                reviewStatus: q.reviewStatus || 'pending',\n                solution: q.solution,\n                hints: q.hints\n            };\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"HQdOYEmIZrqalo67hGXpqfFK1Yc=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});