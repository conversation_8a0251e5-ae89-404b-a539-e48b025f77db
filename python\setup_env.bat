@echo off
REM Setup script for Windows to configure environment variables for Gemini AI

echo Setting up Gemini AI environment...

REM Set the Gemini API key
set GEMINI_API_KEY=AIzaSyB9NGae4_z5JCvk2wEVHBVrrM2a8RtgoMw

REM Create the Mistral API key file
echo PchpFHOtBOO5cM3j0JUUucULwNcaMIhj > key.txt

echo Environment setup complete!
echo.
echo To verify setup, run:
echo python test_gemini.py
echo.
echo To start the API server, run:
echo python api_server.py

pause
