"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/lib/api/questions.ts":
/*!**********************************!*\
  !*** ./src/lib/api/questions.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bulkUploadChemicalQuestionsPDF: () => (/* binding */ bulkUploadChemicalQuestionsPDF),\n/* harmony export */   bulkUploadQuestionsPDF: () => (/* binding */ bulkUploadQuestionsPDF),\n/* harmony export */   createQuestion: () => (/* binding */ createQuestion),\n/* harmony export */   createQuestionWithImages: () => (/* binding */ createQuestionWithImages),\n/* harmony export */   deleteQuestion: () => (/* binding */ deleteQuestion),\n/* harmony export */   getQuestionById: () => (/* binding */ getQuestionById),\n/* harmony export */   getQuestions: () => (/* binding */ getQuestions),\n/* harmony export */   getQuestionsByDifficulty: () => (/* binding */ getQuestionsByDifficulty),\n/* harmony export */   getQuestionsBySubjectAndTopic: () => (/* binding */ getQuestionsBySubjectAndTopic),\n/* harmony export */   reviewQuestion: () => (/* binding */ reviewQuestion),\n/* harmony export */   updateQuestion: () => (/* binding */ updateQuestion),\n/* harmony export */   updateQuestionWithImages: () => (/* binding */ updateQuestionWithImages)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Create a new question\n * @param questionData The question data\n * @returns The created question\n */ async function createQuestion(questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        // Create a copy of the data to avoid modifying the original\n        const dataToSend = {\n            ...questionData\n        };\n        // Remove fields that should not be sent to the API\n        if (!dataToSend.explanation || dataToSend.explanation.trim() === '') {\n            delete dataToSend.explanation;\n        }\n        // Remove status and reviewStatus as they're rejected by the API\n        delete dataToSend.status;\n        delete dataToSend.reviewStatus;\n        // Set default type if not provided\n        if (!dataToSend.type) {\n            dataToSend.type = 'multiple-choice';\n        }\n        console.log(\"Sending question data:\", JSON.stringify(dataToSend, null, 2));\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(dataToSend)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorMessages || errorData.message || \"Error: \".concat(response.status), \"Failed to create question. Please check your input and try again.\");\n            }\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to create question. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"Question created successfully!\");\n    } catch (error) {\n        console.error(\"Error creating question:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to create question. Please try again.\", \"Failed to create question. Please try again.\");\n    }\n}\n/**\n * Get all questions\n * @returns List of questions\n */ async function getQuestions() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions:\", error);\n        throw error;\n    }\n}\n/**\n * Get a question by ID\n * @param id Question ID\n * @returns The question\n */ async function getQuestionById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question\n * @param id Question ID\n * @param questionData The updated question data\n * @returns The updated question\n */ async function updateQuestion(id, questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(questionData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question with optional new images (PATCH method)\n * @param id Question ID\n * @param questionData The updated question data\n * @param questionImage Optional new question image file\n * @param optionImages Optional new option images\n * @returns The updated question\n */ async function updateQuestionWithImages(id, questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PATCH\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a question\n * @param id Question ID\n * @returns The deleted question\n */ async function deleteQuestion(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to delete question. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"Question deleted successfully!\");\n    } catch (error) {\n        console.error(\"Error deleting question:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to delete question. Please try again.\", \"Failed to delete question. Please try again.\");\n    }\n}\n/**\n * Create a question with images\n * @param questionData The question data without imageUrls\n * @param questionImage Optional question image file\n * @param optionImages Optional map of option images\n * @returns The created question\n */ async function createQuestionWithImages(questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        // Add type field with default if not provided\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        // Log form data entries for debugging\n        console.log(\"Form data entries:\");\n        for (const pair of formData.entries()){\n            console.log(pair[0], pair[1]);\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by subject and topic\n * @param subjectId Subject ID\n * @param topicId Topic ID\n * @returns List of questions\n */ async function getQuestionsBySubjectAndTopic(subjectId, topicId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?subjectId=\").concat(encodeURIComponent(subjectId), \"&topicId=\").concat(encodeURIComponent(topicId));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions by subject and topic:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by difficulty\n * @param difficulty Difficulty level ('easy', 'medium', 'hard')\n * @returns List of questions\n */ async function getQuestionsByDifficulty(difficulty) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?difficulty=\").concat(encodeURIComponent(difficulty));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching \".concat(difficulty, \" questions:\"), error);\n        throw error;\n    }\n}\n/**\n * Review a question (approve/reject)\n * @param id Question ID\n * @param reviewStatus Review status ('approved' or 'rejected')\n * @returns The updated question\n */ async function reviewQuestion(id, reviewStatus) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id, \"/review\"), {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify({\n                status: reviewStatus\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error reviewing question:\", error);\n        throw error;\n    }\n}\n/**\n * Bulk upload questions from PDF\n * @param file PDF file containing questions\n * @param subjectId Subject ID for the questions\n * @param topicId Topic ID for the questions (optional)\n * @returns The upload result\n */ async function bulkUploadQuestionsPDF(file, subjectId, topicId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add the PDF file\n        formData.append('file', file);\n        // Add required subject ID\n        formData.append('subjectId', subjectId);\n        // Add optional topic ID\n        if (topicId) {\n            formData.append('topicId', topicId);\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/bulk-upload-pdf\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"PDF upload error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error uploading PDF questions:\", error);\n        throw error;\n    }\n}\n/**\n * Bulk upload chemical questions from PDF with molecular structures\n * @param file PDF file containing chemical questions\n * @param subjectId Subject ID for the questions\n * @param topicId Topic ID for the questions (optional)\n * @returns The upload result with chemical extraction metadata\n */ async function bulkUploadChemicalQuestionsPDF(file, subjectId, topicId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add the PDF file\n        formData.append('file', file);\n        // Add required subject ID\n        formData.append('subjectId', subjectId);\n        // Add optional topic ID\n        if (topicId) {\n            formData.append('topicId', topicId);\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/bulk-upload-chemical-pdf\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"Chemical PDF upload error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error uploading chemical PDF questions:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL3F1ZXN0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQzhGO0FBRTlGLE1BQU1FLGVBQWVDLDJCQUErQixJQUFJLENBQTJCO0FBb0JuRjs7OztDQUlDLEdBQ00sZUFBZUcsZUFBZUMsWUFBMEI7SUFDN0QsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE9BQU9SLHVFQUFjQSxDQUNuQiwyQkFDQTtJQUVKO0lBRUEsSUFBSTtRQUNGLDREQUE0RDtRQUM1RCxNQUFNVyxhQUFhO1lBQUUsR0FBR0osWUFBWTtRQUFDO1FBRXJDLG1EQUFtRDtRQUNuRCxJQUFJLENBQUNJLFdBQVdDLFdBQVcsSUFBSUQsV0FBV0MsV0FBVyxDQUFDQyxJQUFJLE9BQU8sSUFBSTtZQUNuRSxPQUFPRixXQUFXQyxXQUFXO1FBQy9CO1FBRUEsZ0VBQWdFO1FBQ2hFLE9BQU9ELFdBQVdHLE1BQU07UUFDeEIsT0FBT0gsV0FBV0ksWUFBWTtRQUU5QixtQ0FBbUM7UUFDbkMsSUFBSSxDQUFDSixXQUFXSyxJQUFJLEVBQUU7WUFDcEJMLFdBQVdLLElBQUksR0FBRztRQUNwQjtRQUVBQyxRQUFRQyxHQUFHLENBQUMsMEJBQTBCQyxLQUFLQyxTQUFTLENBQUNULFlBQVksTUFBTTtRQUV2RSxNQUFNVSxVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTW1CLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJGLFNBQVEsZUFBYTtZQUNuREcsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsaUJBQWlCLFVBQWdCLE9BQU5qQjtZQUM3QjtZQUNBa0IsTUFBTVAsS0FBS0MsU0FBUyxDQUFDVDtRQUN2QjtRQUVBLElBQUksQ0FBQ1csU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3REYixRQUFRYyxLQUFLLENBQUMsdUJBQXVCSDtZQUVyQyw4Q0FBOEM7WUFDOUMsSUFBSUEsVUFBVUksT0FBTyxFQUFFO2dCQUNyQixNQUFNQyxnQkFBZ0JDLE9BQU9DLE9BQU8sQ0FBQ1AsVUFBVUksT0FBTyxFQUNuREksR0FBRyxDQUFDO3dCQUFDLENBQUNDLE9BQU9DLFFBQVE7MkJBQUssR0FBYUEsT0FBVkQsT0FBTSxNQUFZLE9BQVJDO21CQUN2Q0MsSUFBSSxDQUFDO2dCQUNSLE9BQU92Qyx1RUFBY0EsQ0FDbkJpQyxpQkFBaUJMLFVBQVVVLE9BQU8sSUFBSSxVQUEwQixPQUFoQmhCLFNBQVNSLE1BQU0sR0FDL0Q7WUFFSjtZQUVBLE9BQU9kLHVFQUFjQSxDQUNuQjRCLFVBQVVVLE9BQU8sSUFBSSxVQUEwQixPQUFoQmhCLFNBQVNSLE1BQU0sR0FDOUM7UUFFSjtRQUVBLE1BQU0wQixTQUFTLE1BQU1sQixTQUFTTyxJQUFJO1FBQ2xDLE9BQU81Qiw4RUFBcUJBLENBQUN1QyxRQUFRLE1BQU07SUFDN0MsRUFBRSxPQUFPVCxPQUFPO1FBQ2RkLFFBQVFjLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU8vQix1RUFBY0EsQ0FDbkIrQixpQkFBaUJVLFFBQVFWLE1BQU1PLE9BQU8sR0FBRyxnREFDekM7SUFFSjtBQUNGO0FBRUE7OztDQUdDLEdBQ00sZUFBZUk7SUFDcEIsTUFBTWxDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUNuQyxJQUFJLENBQUNGLE9BQU87UUFDVixNQUFNLElBQUlpQyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1wQixVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTW1CLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJGLFNBQVEsZUFBYTtZQUNuREcsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixVQUFnQixPQUFOakI7WUFDN0I7UUFDRjtRQUVBLElBQUksQ0FBQ2MsU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSVcsTUFBTWIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtRQUNoRTtRQUVBLE9BQU8sTUFBTVEsU0FBU08sSUFBSTtJQUM1QixFQUFFLE9BQU9FLE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7Q0FJQyxHQUNNLGVBQWVZLGdCQUFnQkMsRUFBVTtJQUM5QyxNQUFNcEMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSWlDLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsTUFBTXBCLFVBQVVsQiwyQkFBK0IsSUFBSSxDQUEyQjtRQUM5RSxNQUFNbUIsV0FBVyxNQUFNQyxNQUFNLEdBQXdCcUIsT0FBckJ2QixTQUFRLGVBQWdCLE9BQUh1QixLQUFNO1lBQ3pEcEIsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixVQUFnQixPQUFOakI7WUFDN0I7UUFDRjtRQUVBLElBQUksQ0FBQ2MsU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSVcsTUFBTWIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtRQUNoRTtRQUVBLE9BQU8sTUFBTVEsU0FBU08sSUFBSTtJQUM1QixFQUFFLE9BQU9FLE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7O0NBS0MsR0FDTSxlQUFlYyxlQUFlRCxFQUFVLEVBQUVyQyxZQUEwQjtJQUN6RSxNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJaUMsTUFBTTtJQUNsQjtJQUVBLElBQUk7UUFDRixNQUFNcEIsVUFBVWxCLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1tQixXQUFXLE1BQU1DLE1BQU0sR0FBd0JxQixPQUFyQnZCLFNBQVEsZUFBZ0IsT0FBSHVCLEtBQU07WUFDekRwQixRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixpQkFBaUIsVUFBZ0IsT0FBTmpCO1lBQzdCO1lBQ0FrQixNQUFNUCxLQUFLQyxTQUFTLENBQUNiO1FBQ3ZCO1FBRUEsSUFBSSxDQUFDZSxTQUFTSyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNTixTQUFTTyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7WUFDdEQsTUFBTSxJQUFJVyxNQUFNYixVQUFVVSxPQUFPLElBQUksVUFBMEIsT0FBaEJoQixTQUFTUixNQUFNO1FBQ2hFO1FBRUEsT0FBTyxNQUFNUSxTQUFTTyxJQUFJO0lBQzVCLEVBQUUsT0FBT0UsT0FBTztRQUNkZCxRQUFRYyxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7Ozs7OztDQU9DLEdBQ00sZUFBZWUseUJBQ3BCRixFQUFVLEVBQ1ZyQyxZQUE2QyxFQUM3Q3dDLGFBQTJCLEVBQzNCQyxZQUE2QztJQUU3QyxNQUFNeEMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSWlDLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsTUFBTXBCLFVBQVVsQiwyQkFBK0IsSUFBSSxDQUEyQjtRQUM5RSxNQUFNOEMsV0FBVyxJQUFJQztRQUVyQixvQkFBb0I7UUFDcEJELFNBQVNFLE1BQU0sQ0FBQyxXQUFXNUMsYUFBYTZDLE9BQU87UUFFL0Msd0NBQXdDO1FBQ3hDN0MsYUFBYThDLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDLENBQUNDLFFBQVFDO1lBQ3BDUCxTQUFTRSxNQUFNLENBQUMsV0FBaUIsT0FBTkssT0FBTSxNQUFJRDtRQUN2QztRQUVBTixTQUFTRSxNQUFNLENBQUMsVUFBVTVDLGFBQWFrRCxNQUFNO1FBQzdDUixTQUFTRSxNQUFNLENBQUMsYUFBYTVDLGFBQWFtRCxTQUFTO1FBQ25EVCxTQUFTRSxNQUFNLENBQUMsV0FBVzVDLGFBQWFvRCxPQUFPO1FBQy9DVixTQUFTRSxNQUFNLENBQUMsY0FBYzVDLGFBQWFxRCxVQUFVO1FBQ3JEWCxTQUFTRSxNQUFNLENBQUMsUUFBUTVDLGFBQWFTLElBQUksSUFBSTtRQUU3QyxrQ0FBa0M7UUFDbEMsSUFBSVQsYUFBYXNELFNBQVMsRUFBRTtZQUMxQlosU0FBU0UsTUFBTSxDQUFDLGFBQWE1QyxhQUFhc0QsU0FBUztRQUNyRDtRQUVBLHlDQUF5QztRQUN6QyxJQUFJdEQsYUFBYUssV0FBVyxJQUFJTCxhQUFhSyxXQUFXLENBQUNDLElBQUksT0FBTyxJQUFJO1lBQ3RFb0MsU0FBU0UsTUFBTSxDQUFDLGVBQWU1QyxhQUFhSyxXQUFXO1FBQ3pEO1FBRUEsaUNBQWlDO1FBQ2pDLElBQUltQyxlQUFlO1lBQ2pCRSxTQUFTRSxNQUFNLENBQUMsVUFBVUo7UUFDNUI7UUFFQSxnQ0FBZ0M7UUFDaEMsSUFBSUMsY0FBYztZQUNoQmQsT0FBT0MsT0FBTyxDQUFDYSxjQUFjTSxPQUFPLENBQUM7b0JBQUMsQ0FBQ1EsS0FBS0MsS0FBSztnQkFDL0MsSUFBSUEsTUFBTTtvQkFDUmQsU0FBU0UsTUFBTSxDQUFDLGdCQUFvQixPQUFKVyxLQUFJLE1BQUlDO2dCQUMxQztZQUNGO1FBQ0Y7UUFFQSxNQUFNekMsV0FBVyxNQUFNQyxNQUFNLEdBQXdCcUIsT0FBckJ2QixTQUFRLGVBQWdCLE9BQUh1QixLQUFNO1lBQ3pEcEIsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixVQUFnQixPQUFOakI7WUFDN0I7WUFDQWtCLE1BQU11QjtRQUNSO1FBRUEsSUFBSSxDQUFDM0IsU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSVcsTUFBTWIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtRQUNoRTtRQUVBLE9BQU8sTUFBTVEsU0FBU08sSUFBSTtJQUM1QixFQUFFLE9BQU9FLE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLHdDQUF3Q0E7UUFDdEQsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7Q0FJQyxHQUNNLGVBQWVpQyxlQUFlcEIsRUFBVTtJQUM3QyxNQUFNcEMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE9BQU9SLHVFQUFjQSxDQUNuQiwyQkFDQTtJQUVKO0lBRUEsSUFBSTtRQUNGLE1BQU1xQixVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTW1CLFdBQVcsTUFBTUMsTUFBTSxHQUF3QnFCLE9BQXJCdkIsU0FBUSxlQUFnQixPQUFIdUIsS0FBTTtZQUN6RHBCLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTmpCO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJLENBQUNjLFNBQVNLLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1OLFNBQVNPLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RCxPQUFPOUIsdUVBQWNBLENBQ25CNEIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTSxHQUM5QztRQUVKO1FBRUEsTUFBTTBCLFNBQVMsTUFBTWxCLFNBQVNPLElBQUk7UUFDbEMsT0FBTzVCLDhFQUFxQkEsQ0FBQ3VDLFFBQVEsTUFBTTtJQUM3QyxFQUFFLE9BQU9ULE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBTy9CLHVFQUFjQSxDQUNuQitCLGlCQUFpQlUsUUFBUVYsTUFBTU8sT0FBTyxHQUFHLGdEQUN6QztJQUVKO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDTSxlQUFlMkIseUJBQ3BCMUQsWUFBNkMsRUFDN0N3QyxhQUEyQixFQUMzQkMsWUFBNkM7SUFFN0MsTUFBTXhDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUNuQyxJQUFJLENBQUNGLE9BQU87UUFDVixNQUFNLElBQUlpQyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1wQixVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTThDLFdBQVcsSUFBSUM7UUFFckIsb0JBQW9CO1FBQ3BCRCxTQUFTRSxNQUFNLENBQUMsV0FBVzVDLGFBQWE2QyxPQUFPO1FBRS9DLHdDQUF3QztRQUN4QzdDLGFBQWE4QyxPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDQyxRQUFRQztZQUNwQ1AsU0FBU0UsTUFBTSxDQUFDLFdBQWlCLE9BQU5LLE9BQU0sTUFBSUQ7UUFDdkM7UUFFQU4sU0FBU0UsTUFBTSxDQUFDLFVBQVU1QyxhQUFha0QsTUFBTTtRQUM3Q1IsU0FBU0UsTUFBTSxDQUFDLGFBQWE1QyxhQUFhbUQsU0FBUztRQUNuRFQsU0FBU0UsTUFBTSxDQUFDLFdBQVc1QyxhQUFhb0QsT0FBTztRQUMvQ1YsU0FBU0UsTUFBTSxDQUFDLGNBQWM1QyxhQUFhcUQsVUFBVTtRQUVyRCw4Q0FBOEM7UUFDOUNYLFNBQVNFLE1BQU0sQ0FBQyxRQUFRNUMsYUFBYVMsSUFBSSxJQUFJO1FBRTdDLGtDQUFrQztRQUNsQyxJQUFJVCxhQUFhc0QsU0FBUyxFQUFFO1lBQzFCWixTQUFTRSxNQUFNLENBQUMsYUFBYTVDLGFBQWFzRCxTQUFTO1FBQ3JEO1FBRUEseUNBQXlDO1FBQ3pDLElBQUl0RCxhQUFhSyxXQUFXLElBQUlMLGFBQWFLLFdBQVcsQ0FBQ0MsSUFBSSxPQUFPLElBQUk7WUFDdEVvQyxTQUFTRSxNQUFNLENBQUMsZUFBZTVDLGFBQWFLLFdBQVc7UUFDekQ7UUFFQSxpQ0FBaUM7UUFDakMsSUFBSW1DLGVBQWU7WUFDakJFLFNBQVNFLE1BQU0sQ0FBQyxVQUFVSjtRQUM1QjtRQUVBLGdDQUFnQztRQUNoQyxJQUFJQyxjQUFjO1lBQ2hCZCxPQUFPQyxPQUFPLENBQUNhLGNBQWNNLE9BQU8sQ0FBQztvQkFBQyxDQUFDUSxLQUFLQyxLQUFLO2dCQUMvQyxJQUFJQSxNQUFNO29CQUNSZCxTQUFTRSxNQUFNLENBQUMsZ0JBQW9CLE9BQUpXLEtBQUksTUFBSUM7Z0JBQzFDO1lBQ0Y7UUFDRjtRQUVBLHNDQUFzQztRQUN0QzlDLFFBQVFDLEdBQUcsQ0FBQztRQUNaLEtBQUssTUFBTWdELFFBQVFqQixTQUFTZCxPQUFPLEdBQUk7WUFDckNsQixRQUFRQyxHQUFHLENBQUNnRCxJQUFJLENBQUMsRUFBRSxFQUFFQSxJQUFJLENBQUMsRUFBRTtRQUM5QjtRQUVBLE1BQU01QyxXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSRixTQUFRLGVBQWE7WUFDbkRHLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTmpCO1lBQzdCO1lBQ0FrQixNQUFNdUI7UUFDUjtRQUVBLElBQUksQ0FBQzNCLFNBQVNLLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1OLFNBQVNPLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RGIsUUFBUWMsS0FBSyxDQUFDLHVCQUF1Qkg7WUFFckMsOENBQThDO1lBQzlDLElBQUlBLFVBQVVJLE9BQU8sRUFBRTtnQkFDckIsTUFBTUMsZ0JBQWdCQyxPQUFPQyxPQUFPLENBQUNQLFVBQVVJLE9BQU8sRUFDbkRJLEdBQUcsQ0FBQzt3QkFBQyxDQUFDQyxPQUFPQyxRQUFROzJCQUFLLEdBQWFBLE9BQVZELE9BQU0sTUFBWSxPQUFSQzttQkFDdkNDLElBQUksQ0FBQztnQkFDUixNQUFNLElBQUlFLE1BQU1SLGlCQUFpQkwsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtZQUNqRjtZQUVBLE1BQU0sSUFBSTJCLE1BQU1iLFVBQVVVLE9BQU8sSUFBSSxVQUEwQixPQUFoQmhCLFNBQVNSLE1BQU07UUFDaEU7UUFFQSxPQUFPLE1BQU1RLFNBQVNPLElBQUk7SUFDNUIsRUFBRSxPQUFPRSxPQUFPO1FBQ2RkLFFBQVFjLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3RELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7OztDQUtDLEdBQ00sZUFBZW9DLDhCQUE4QlQsU0FBaUIsRUFBRUMsT0FBZTtJQUNwRixNQUFNbkQsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSWlDLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsTUFBTXBCLFVBQVVsQiwyQkFBK0IsSUFBSSxDQUEyQjtRQUM5RSxNQUFNaUUsTUFBTSxHQUFrQ0MsT0FBL0JoRCxTQUFRLHlCQUFnRWdELE9BQXpDQSxtQkFBbUJYLFlBQVcsYUFBdUMsT0FBNUJXLG1CQUFtQlY7UUFFMUcsTUFBTXJDLFdBQVcsTUFBTUMsTUFBTTZDLEtBQUs7WUFDaEM1QyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5qQjtZQUM3QjtRQUNGO1FBRUEsSUFBSSxDQUFDYyxTQUFTSyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNTixTQUFTTyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7WUFDdEQsTUFBTSxJQUFJVyxNQUFNYixVQUFVVSxPQUFPLElBQUksVUFBMEIsT0FBaEJoQixTQUFTUixNQUFNO1FBQ2hFO1FBRUEsT0FBTyxNQUFNUSxTQUFTTyxJQUFJO0lBQzVCLEVBQUUsT0FBT0UsT0FBTztRQUNkZCxRQUFRYyxLQUFLLENBQUMsa0RBQWtEQTtRQUNoRSxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sZUFBZXVDLHlCQUF5QlYsVUFBc0M7SUFDbkYsTUFBTXBELFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUNuQyxJQUFJLENBQUNGLE9BQU87UUFDVixNQUFNLElBQUlpQyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1wQixVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTWlFLE1BQU0sR0FBbUNDLE9BQWhDaEQsU0FBUSwwQkFBdUQsT0FBL0JnRCxtQkFBbUJUO1FBRWxFLE1BQU10QyxXQUFXLE1BQU1DLE1BQU02QyxLQUFLO1lBQ2hDNUMsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixVQUFnQixPQUFOakI7WUFDN0I7UUFDRjtRQUVBLElBQUksQ0FBQ2MsU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSVcsTUFBTWIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtRQUNoRTtRQUVBLE9BQU8sTUFBTVEsU0FBU08sSUFBSTtJQUM1QixFQUFFLE9BQU9FLE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLGtCQUE2QixPQUFYNkIsWUFBVyxnQkFBYzdCO1FBQ3pELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7OztDQUtDLEdBQ00sZUFBZXdDLGVBQWUzQixFQUFVLEVBQUU3QixZQUFxQztJQUNwRixNQUFNUCxRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJaUMsTUFBTTtJQUNsQjtJQUVBLElBQUk7UUFDRixNQUFNcEIsVUFBVWxCLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1tQixXQUFXLE1BQU1DLE1BQU0sR0FBd0JxQixPQUFyQnZCLFNBQVEsZUFBZ0IsT0FBSHVCLElBQUcsWUFBVTtZQUNoRXBCLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7Z0JBQ2hCLGlCQUFpQixVQUFnQixPQUFOakI7WUFDN0I7WUFDQWtCLE1BQU1QLEtBQUtDLFNBQVMsQ0FBQztnQkFBRU4sUUFBUUM7WUFBYTtRQUM5QztRQUVBLElBQUksQ0FBQ08sU0FBU0ssRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTU4sU0FBU08sSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSVcsTUFBTWIsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtRQUNoRTtRQUVBLE9BQU8sTUFBTVEsU0FBU08sSUFBSTtJQUM1QixFQUFFLE9BQU9FLE9BQU87UUFDZGQsUUFBUWMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7OztDQU1DLEdBQ00sZUFBZXlDLHVCQUNwQlQsSUFBVSxFQUNWTCxTQUFpQixFQUNqQkMsT0FBZ0I7SUFFaEIsTUFBTW5ELFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUNuQyxJQUFJLENBQUNGLE9BQU87UUFDVixNQUFNLElBQUlpQyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1wQixVQUFVbEIsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTThDLFdBQVcsSUFBSUM7UUFFckIsbUJBQW1CO1FBQ25CRCxTQUFTRSxNQUFNLENBQUMsUUFBUVk7UUFFeEIsMEJBQTBCO1FBQzFCZCxTQUFTRSxNQUFNLENBQUMsYUFBYU87UUFFN0Isd0JBQXdCO1FBQ3hCLElBQUlDLFNBQVM7WUFDWFYsU0FBU0UsTUFBTSxDQUFDLFdBQVdRO1FBQzdCO1FBRUEsTUFBTXJDLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJGLFNBQVEsK0JBQTZCO1lBQ25FRyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5qQjtZQUM3QjtZQUNBa0IsTUFBTXVCO1FBQ1I7UUFFQSxJQUFJLENBQUMzQixTQUFTSyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNTixTQUFTTyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7WUFDdERiLFFBQVFjLEtBQUssQ0FBQyw4QkFBOEJIO1lBRTVDLDhDQUE4QztZQUM5QyxJQUFJQSxVQUFVSSxPQUFPLEVBQUU7Z0JBQ3JCLE1BQU1DLGdCQUFnQkMsT0FBT0MsT0FBTyxDQUFDUCxVQUFVSSxPQUFPLEVBQ25ESSxHQUFHLENBQUM7d0JBQUMsQ0FBQ0MsT0FBT0MsUUFBUTsyQkFBSyxHQUFhQSxPQUFWRCxPQUFNLE1BQVksT0FBUkM7bUJBQ3ZDQyxJQUFJLENBQUM7Z0JBQ1IsTUFBTSxJQUFJRSxNQUFNUixpQkFBaUJMLFVBQVVVLE9BQU8sSUFBSSxVQUEwQixPQUFoQmhCLFNBQVNSLE1BQU07WUFDakY7WUFFQSxNQUFNLElBQUkyQixNQUFNYixVQUFVVSxPQUFPLElBQUksVUFBMEIsT0FBaEJoQixTQUFTUixNQUFNO1FBQ2hFO1FBRUEsT0FBTyxNQUFNUSxTQUFTTyxJQUFJO0lBQzVCLEVBQUUsT0FBT0UsT0FBTztRQUNkZCxRQUFRYyxLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7Ozs7O0NBTUMsR0FDTSxlQUFlMEMsK0JBQ3BCVixJQUFVLEVBQ1ZMLFNBQWlCLEVBQ2pCQyxPQUFnQjtJQUVoQixNQUFNbkQsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSWlDLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsTUFBTXBCLFVBQVVsQiwyQkFBK0IsSUFBSSxDQUEyQjtRQUM5RSxNQUFNOEMsV0FBVyxJQUFJQztRQUVyQixtQkFBbUI7UUFDbkJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRWTtRQUV4QiwwQkFBMEI7UUFDMUJkLFNBQVNFLE1BQU0sQ0FBQyxhQUFhTztRQUU3Qix3QkFBd0I7UUFDeEIsSUFBSUMsU0FBUztZQUNYVixTQUFTRSxNQUFNLENBQUMsV0FBV1E7UUFDN0I7UUFFQSxNQUFNckMsV0FBVyxNQUFNQyxNQUFNLEdBQVcsT0FBUkYsU0FBUSx3Q0FBc0M7WUFDNUVHLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTmpCO1lBQzdCO1lBQ0FrQixNQUFNdUI7UUFDUjtRQUVBLElBQUksQ0FBQzNCLFNBQVNLLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1OLFNBQVNPLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RGIsUUFBUWMsS0FBSyxDQUFDLHVDQUF1Q0g7WUFFckQsOENBQThDO1lBQzlDLElBQUlBLFVBQVVJLE9BQU8sRUFBRTtnQkFDckIsTUFBTUMsZ0JBQWdCQyxPQUFPQyxPQUFPLENBQUNQLFVBQVVJLE9BQU8sRUFDbkRJLEdBQUcsQ0FBQzt3QkFBQyxDQUFDQyxPQUFPQyxRQUFROzJCQUFLLEdBQWFBLE9BQVZELE9BQU0sTUFBWSxPQUFSQzttQkFDdkNDLElBQUksQ0FBQztnQkFDUixNQUFNLElBQUlFLE1BQU1SLGlCQUFpQkwsVUFBVVUsT0FBTyxJQUFJLFVBQTBCLE9BQWhCaEIsU0FBU1IsTUFBTTtZQUNqRjtZQUVBLE1BQU0sSUFBSTJCLE1BQU1iLFVBQVVVLE9BQU8sSUFBSSxVQUEwQixPQUFoQmhCLFNBQVNSLE1BQU07UUFDaEU7UUFFQSxPQUFPLE1BQU1RLFNBQVNPLElBQUk7SUFDNUIsRUFBRSxPQUFPRSxPQUFPO1FBQ2RkLFFBQVFjLEtBQUssQ0FBQywyQ0FBMkNBO1FBQ3pELE1BQU1BO0lBQ1I7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXHNyY1xcbGliXFxhcGlcXHF1ZXN0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB7IGhhbmRsZUFwaUVycm9yLCBjcmVhdGVTdWNjZXNzUmVzcG9uc2UsIEFwaVJlc3BvbnNlIH0gZnJvbSAnQC9saWIvdXRpbHMvZXJyb3JIYW5kbGVyJztcblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG5cbi8qKlxuICogSW50ZXJmYWNlIGZvciBxdWVzdGlvbiBkYXRhXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgUXVlc3Rpb25EYXRhIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBvcHRpb25zOiBzdHJpbmdbXTtcbiAgYW5zd2VyOiBzdHJpbmc7XG4gIGltYWdlVXJscz86IHN0cmluZ1tdO1xuICBzdWJqZWN0SWQ6IHN0cmluZztcbiAgdG9waWNJZDogc3RyaW5nO1xuICBkaWZmaWN1bHR5OiAnZWFzeScgfCAnbWVkaXVtJyB8ICdoYXJkJztcbiAgdHlwZTogc3RyaW5nO1xuICBleHBsYW5hdGlvbj86IHN0cmluZztcbiAgc3RhdHVzPzogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnO1xuICByZXZpZXdTdGF0dXM/OiAncGVuZGluZycgfCAnYXBwcm92ZWQnIHwgJ3JlamVjdGVkJztcbiAgY3JlYXRlZEJ5Pzogc3RyaW5nO1xufVxuXG4vKipcbiAqIENyZWF0ZSBhIG5ldyBxdWVzdGlvblxuICogQHBhcmFtIHF1ZXN0aW9uRGF0YSBUaGUgcXVlc3Rpb24gZGF0YVxuICogQHJldHVybnMgVGhlIGNyZWF0ZWQgcXVlc3Rpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZVF1ZXN0aW9uKHF1ZXN0aW9uRGF0YTogUXVlc3Rpb25EYXRhKTogUHJvbWlzZTxBcGlSZXNwb25zZT4ge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xuICBpZiAoIXRva2VuKSB7XG4gICAgcmV0dXJuIGhhbmRsZUFwaUVycm9yKFxuICAgICAgXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiLFxuICAgICAgXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIlxuICAgICk7XG4gIH1cblxuICB0cnkge1xuICAgIC8vIENyZWF0ZSBhIGNvcHkgb2YgdGhlIGRhdGEgdG8gYXZvaWQgbW9kaWZ5aW5nIHRoZSBvcmlnaW5hbFxuICAgIGNvbnN0IGRhdGFUb1NlbmQgPSB7IC4uLnF1ZXN0aW9uRGF0YSB9O1xuXG4gICAgLy8gUmVtb3ZlIGZpZWxkcyB0aGF0IHNob3VsZCBub3QgYmUgc2VudCB0byB0aGUgQVBJXG4gICAgaWYgKCFkYXRhVG9TZW5kLmV4cGxhbmF0aW9uIHx8IGRhdGFUb1NlbmQuZXhwbGFuYXRpb24udHJpbSgpID09PSAnJykge1xuICAgICAgZGVsZXRlIGRhdGFUb1NlbmQuZXhwbGFuYXRpb247XG4gICAgfVxuXG4gICAgLy8gUmVtb3ZlIHN0YXR1cyBhbmQgcmV2aWV3U3RhdHVzIGFzIHRoZXkncmUgcmVqZWN0ZWQgYnkgdGhlIEFQSVxuICAgIGRlbGV0ZSBkYXRhVG9TZW5kLnN0YXR1cztcbiAgICBkZWxldGUgZGF0YVRvU2VuZC5yZXZpZXdTdGF0dXM7XG5cbiAgICAvLyBTZXQgZGVmYXVsdCB0eXBlIGlmIG5vdCBwcm92aWRlZFxuICAgIGlmICghZGF0YVRvU2VuZC50eXBlKSB7XG4gICAgICBkYXRhVG9TZW5kLnR5cGUgPSAnbXVsdGlwbGUtY2hvaWNlJztcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhcIlNlbmRpbmcgcXVlc3Rpb24gZGF0YTpcIiwgSlNPTi5zdHJpbmdpZnkoZGF0YVRvU2VuZCwgbnVsbCwgMikpO1xuXG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vcXVlc3Rpb25zYCwge1xuICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGRhdGFUb1NlbmQpXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQVBJIGVycm9yIHJlc3BvbnNlOlwiLCBlcnJvckRhdGEpO1xuXG4gICAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGRldGFpbGVkIHZhbGlkYXRpb24gZXJyb3JzXG4gICAgICBpZiAoZXJyb3JEYXRhLmRldGFpbHMpIHtcbiAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlcyA9IE9iamVjdC5lbnRyaWVzKGVycm9yRGF0YS5kZXRhaWxzKVxuICAgICAgICAgIC5tYXAoKFtmaWVsZCwgbWVzc2FnZV0pID0+IGAke2ZpZWxkfTogJHttZXNzYWdlfWApXG4gICAgICAgICAgLmpvaW4oJywgJyk7XG4gICAgICAgIHJldHVybiBoYW5kbGVBcGlFcnJvcihcbiAgICAgICAgICBlcnJvck1lc3NhZ2VzIHx8IGVycm9yRGF0YS5tZXNzYWdlIHx8IGBFcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCxcbiAgICAgICAgICBcIkZhaWxlZCB0byBjcmVhdGUgcXVlc3Rpb24uIFBsZWFzZSBjaGVjayB5b3VyIGlucHV0IGFuZCB0cnkgYWdhaW4uXCJcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGhhbmRsZUFwaUVycm9yKFxuICAgICAgICBlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWAsXG4gICAgICAgIFwiRmFpbGVkIHRvIGNyZWF0ZSBxdWVzdGlvbi4gUGxlYXNlIHRyeSBhZ2Fpbi5cIlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgcmV0dXJuIGNyZWF0ZVN1Y2Nlc3NSZXNwb25zZShyZXN1bHQsIHRydWUsIFwiUXVlc3Rpb24gY3JlYXRlZCBzdWNjZXNzZnVsbHkhXCIpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBxdWVzdGlvbjpcIiwgZXJyb3IpO1xuICAgIHJldHVybiBoYW5kbGVBcGlFcnJvcihcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gY3JlYXRlIHF1ZXN0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuICAgICAgXCJGYWlsZWQgdG8gY3JlYXRlIHF1ZXN0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiXG4gICAgKTtcbiAgfVxufVxuXG4vKipcbiAqIEdldCBhbGwgcXVlc3Rpb25zXG4gKiBAcmV0dXJucyBMaXN0IG9mIHF1ZXN0aW9uc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UXVlc3Rpb25zKCkge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xuICBpZiAoIXRva2VuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIik7XG4gIH1cbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vcXVlc3Rpb25zYCwge1xuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWBcbiAgICAgIH1cbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgcXVlc3Rpb25zOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgYSBxdWVzdGlvbiBieSBJRFxuICogQHBhcmFtIGlkIFF1ZXN0aW9uIElEXG4gKiBAcmV0dXJucyBUaGUgcXVlc3Rpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFF1ZXN0aW9uQnlJZChpZDogc3RyaW5nKSB7XG4gIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJiYWNrZW5kVG9rZW5cIik7XG4gIGlmICghdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKTtcbiAgfVxuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9xdWVzdGlvbnMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBxdWVzdGlvbjpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogVXBkYXRlIGEgcXVlc3Rpb25cbiAqIEBwYXJhbSBpZCBRdWVzdGlvbiBJRFxuICogQHBhcmFtIHF1ZXN0aW9uRGF0YSBUaGUgdXBkYXRlZCBxdWVzdGlvbiBkYXRhXG4gKiBAcmV0dXJucyBUaGUgdXBkYXRlZCBxdWVzdGlvblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlUXVlc3Rpb24oaWQ6IHN0cmluZywgcXVlc3Rpb25EYXRhOiBRdWVzdGlvbkRhdGEpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9xdWVzdGlvbnMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6IFwiUEFUQ0hcIixcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHF1ZXN0aW9uRGF0YSlcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cblxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIHF1ZXN0aW9uOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBVcGRhdGUgYSBxdWVzdGlvbiB3aXRoIG9wdGlvbmFsIG5ldyBpbWFnZXMgKFBBVENIIG1ldGhvZClcbiAqIEBwYXJhbSBpZCBRdWVzdGlvbiBJRFxuICogQHBhcmFtIHF1ZXN0aW9uRGF0YSBUaGUgdXBkYXRlZCBxdWVzdGlvbiBkYXRhXG4gKiBAcGFyYW0gcXVlc3Rpb25JbWFnZSBPcHRpb25hbCBuZXcgcXVlc3Rpb24gaW1hZ2UgZmlsZVxuICogQHBhcmFtIG9wdGlvbkltYWdlcyBPcHRpb25hbCBuZXcgb3B0aW9uIGltYWdlc1xuICogQHJldHVybnMgVGhlIHVwZGF0ZWQgcXVlc3Rpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZVF1ZXN0aW9uV2l0aEltYWdlcyhcbiAgaWQ6IHN0cmluZyxcbiAgcXVlc3Rpb25EYXRhOiBPbWl0PFF1ZXN0aW9uRGF0YSwgJ2ltYWdlVXJscyc+LFxuICBxdWVzdGlvbkltYWdlPzogRmlsZSB8IG51bGwsXG4gIG9wdGlvbkltYWdlcz86IHsgW2tleTogc3RyaW5nXTogRmlsZSB8IG51bGwgfVxuKSB7XG4gIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJiYWNrZW5kVG9rZW5cIik7XG4gIGlmICghdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKTtcbiAgfVxuXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG5cbiAgICAvLyBBZGQgcXVlc3Rpb24gZGF0YVxuICAgIGZvcm1EYXRhLmFwcGVuZCgnY29udGVudCcsIHF1ZXN0aW9uRGF0YS5jb250ZW50KTtcblxuICAgIC8vIEFkZCBvcHRpb25zIGFzIGluZGl2aWR1YWwgZm9ybSBmaWVsZHNcbiAgICBxdWVzdGlvbkRhdGEub3B0aW9ucy5mb3JFYWNoKChvcHRpb24sIGluZGV4KSA9PiB7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoYG9wdGlvbnNbJHtpbmRleH1dYCwgb3B0aW9uKTtcbiAgICB9KTtcblxuICAgIGZvcm1EYXRhLmFwcGVuZCgnYW5zd2VyJywgcXVlc3Rpb25EYXRhLmFuc3dlcik7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdzdWJqZWN0SWQnLCBxdWVzdGlvbkRhdGEuc3ViamVjdElkKTtcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3RvcGljSWQnLCBxdWVzdGlvbkRhdGEudG9waWNJZCk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdkaWZmaWN1bHR5JywgcXVlc3Rpb25EYXRhLmRpZmZpY3VsdHkpO1xuICAgIGZvcm1EYXRhLmFwcGVuZCgndHlwZScsIHF1ZXN0aW9uRGF0YS50eXBlIHx8ICdtdWx0aXBsZS1jaG9pY2UnKTtcblxuICAgIC8vIEFkZCBjcmVhdGVkQnkgZmllbGQgaWYgcHJvdmlkZWRcbiAgICBpZiAocXVlc3Rpb25EYXRhLmNyZWF0ZWRCeSkge1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjcmVhdGVkQnknLCBxdWVzdGlvbkRhdGEuY3JlYXRlZEJ5KTtcbiAgICB9XG5cbiAgICAvLyBPbmx5IGFkZCBleHBsYW5hdGlvbiBpZiBpdCBoYXMgYSB2YWx1ZVxuICAgIGlmIChxdWVzdGlvbkRhdGEuZXhwbGFuYXRpb24gJiYgcXVlc3Rpb25EYXRhLmV4cGxhbmF0aW9uLnRyaW0oKSAhPT0gJycpIHtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZXhwbGFuYXRpb24nLCBxdWVzdGlvbkRhdGEuZXhwbGFuYXRpb24pO1xuICAgIH1cblxuICAgIC8vIEFkZCBxdWVzdGlvbiBpbWFnZSBpZiBwcm92aWRlZFxuICAgIGlmIChxdWVzdGlvbkltYWdlKSB7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ltYWdlcycsIHF1ZXN0aW9uSW1hZ2UpO1xuICAgIH1cblxuICAgIC8vIEFkZCBvcHRpb24gaW1hZ2VzIGlmIHByb3ZpZGVkXG4gICAgaWYgKG9wdGlvbkltYWdlcykge1xuICAgICAgT2JqZWN0LmVudHJpZXMob3B0aW9uSW1hZ2VzKS5mb3JFYWNoKChba2V5LCBmaWxlXSkgPT4ge1xuICAgICAgICBpZiAoZmlsZSkge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChgb3B0aW9uSW1hZ2VzWyR7a2V5fV1gLCBmaWxlKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9xdWVzdGlvbnMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6IFwiUEFUQ0hcIixcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICB9LFxuICAgICAgYm9keTogZm9ybURhdGFcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cblxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIHF1ZXN0aW9uIHdpdGggaW1hZ2VzOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBEZWxldGUgYSBxdWVzdGlvblxuICogQHBhcmFtIGlkIFF1ZXN0aW9uIElEXG4gKiBAcmV0dXJucyBUaGUgZGVsZXRlZCBxdWVzdGlvblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlUXVlc3Rpb24oaWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U+IHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHJldHVybiBoYW5kbGVBcGlFcnJvcihcbiAgICAgIFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIixcbiAgICAgIFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uXCJcbiAgICApO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9xdWVzdGlvbnMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xuICAgICAgcmV0dXJuIGhhbmRsZUFwaUVycm9yKFxuICAgICAgICBlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWAsXG4gICAgICAgIFwiRmFpbGVkIHRvIGRlbGV0ZSBxdWVzdGlvbi4gUGxlYXNlIHRyeSBhZ2Fpbi5cIlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgcmV0dXJuIGNyZWF0ZVN1Y2Nlc3NSZXNwb25zZShyZXN1bHQsIHRydWUsIFwiUXVlc3Rpb24gZGVsZXRlZCBzdWNjZXNzZnVsbHkhXCIpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBxdWVzdGlvbjpcIiwgZXJyb3IpO1xuICAgIHJldHVybiBoYW5kbGVBcGlFcnJvcihcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJGYWlsZWQgdG8gZGVsZXRlIHF1ZXN0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuICAgICAgXCJGYWlsZWQgdG8gZGVsZXRlIHF1ZXN0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLlwiXG4gICAgKTtcbiAgfVxufVxuXG4vKipcbiAqIENyZWF0ZSBhIHF1ZXN0aW9uIHdpdGggaW1hZ2VzXG4gKiBAcGFyYW0gcXVlc3Rpb25EYXRhIFRoZSBxdWVzdGlvbiBkYXRhIHdpdGhvdXQgaW1hZ2VVcmxzXG4gKiBAcGFyYW0gcXVlc3Rpb25JbWFnZSBPcHRpb25hbCBxdWVzdGlvbiBpbWFnZSBmaWxlXG4gKiBAcGFyYW0gb3B0aW9uSW1hZ2VzIE9wdGlvbmFsIG1hcCBvZiBvcHRpb24gaW1hZ2VzXG4gKiBAcmV0dXJucyBUaGUgY3JlYXRlZCBxdWVzdGlvblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlUXVlc3Rpb25XaXRoSW1hZ2VzKFxuICBxdWVzdGlvbkRhdGE6IE9taXQ8UXVlc3Rpb25EYXRhLCAnaW1hZ2VVcmxzJz4sXG4gIHF1ZXN0aW9uSW1hZ2U/OiBGaWxlIHwgbnVsbCxcbiAgb3B0aW9uSW1hZ2VzPzogeyBba2V5OiBzdHJpbmddOiBGaWxlIHwgbnVsbCB9XG4pIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG4gIFxuICB0cnkge1xuICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgIFxuICAgIC8vIEFkZCBxdWVzdGlvbiBkYXRhXG4gICAgZm9ybURhdGEuYXBwZW5kKCdjb250ZW50JywgcXVlc3Rpb25EYXRhLmNvbnRlbnQpO1xuICAgIFxuICAgIC8vIEFkZCBvcHRpb25zIGFzIGluZGl2aWR1YWwgZm9ybSBmaWVsZHNcbiAgICBxdWVzdGlvbkRhdGEub3B0aW9ucy5mb3JFYWNoKChvcHRpb24sIGluZGV4KSA9PiB7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoYG9wdGlvbnNbJHtpbmRleH1dYCwgb3B0aW9uKTtcbiAgICB9KTtcbiAgICBcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2Fuc3dlcicsIHF1ZXN0aW9uRGF0YS5hbnN3ZXIpO1xuICAgIGZvcm1EYXRhLmFwcGVuZCgnc3ViamVjdElkJywgcXVlc3Rpb25EYXRhLnN1YmplY3RJZCk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCd0b3BpY0lkJywgcXVlc3Rpb25EYXRhLnRvcGljSWQpO1xuICAgIGZvcm1EYXRhLmFwcGVuZCgnZGlmZmljdWx0eScsIHF1ZXN0aW9uRGF0YS5kaWZmaWN1bHR5KTtcbiAgICBcbiAgICAvLyBBZGQgdHlwZSBmaWVsZCB3aXRoIGRlZmF1bHQgaWYgbm90IHByb3ZpZGVkXG4gICAgZm9ybURhdGEuYXBwZW5kKCd0eXBlJywgcXVlc3Rpb25EYXRhLnR5cGUgfHwgJ211bHRpcGxlLWNob2ljZScpO1xuICAgIFxuICAgIC8vIEFkZCBjcmVhdGVkQnkgZmllbGQgaWYgcHJvdmlkZWRcbiAgICBpZiAocXVlc3Rpb25EYXRhLmNyZWF0ZWRCeSkge1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjcmVhdGVkQnknLCBxdWVzdGlvbkRhdGEuY3JlYXRlZEJ5KTtcbiAgICB9XG4gICAgXG4gICAgLy8gT25seSBhZGQgZXhwbGFuYXRpb24gaWYgaXQgaGFzIGEgdmFsdWVcbiAgICBpZiAocXVlc3Rpb25EYXRhLmV4cGxhbmF0aW9uICYmIHF1ZXN0aW9uRGF0YS5leHBsYW5hdGlvbi50cmltKCkgIT09ICcnKSB7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2V4cGxhbmF0aW9uJywgcXVlc3Rpb25EYXRhLmV4cGxhbmF0aW9uKTtcbiAgICB9XG4gICAgXG4gICAgLy8gQWRkIHF1ZXN0aW9uIGltYWdlIGlmIHByb3ZpZGVkXG4gICAgaWYgKHF1ZXN0aW9uSW1hZ2UpIHtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnaW1hZ2VzJywgcXVlc3Rpb25JbWFnZSk7XG4gICAgfVxuICAgIFxuICAgIC8vIEFkZCBvcHRpb24gaW1hZ2VzIGlmIHByb3ZpZGVkXG4gICAgaWYgKG9wdGlvbkltYWdlcykge1xuICAgICAgT2JqZWN0LmVudHJpZXMob3B0aW9uSW1hZ2VzKS5mb3JFYWNoKChba2V5LCBmaWxlXSkgPT4ge1xuICAgICAgICBpZiAoZmlsZSkge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChgb3B0aW9uSW1hZ2VzWyR7a2V5fV1gLCBmaWxlKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIC8vIExvZyBmb3JtIGRhdGEgZW50cmllcyBmb3IgZGVidWdnaW5nXG4gICAgY29uc29sZS5sb2coXCJGb3JtIGRhdGEgZW50cmllczpcIik7XG4gICAgZm9yIChjb25zdCBwYWlyIG9mIGZvcm1EYXRhLmVudHJpZXMoKSkge1xuICAgICAgY29uc29sZS5sb2cocGFpclswXSwgcGFpclsxXSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vcXVlc3Rpb25zYCwge1xuICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICB9LFxuICAgICAgYm9keTogZm9ybURhdGFcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQVBJIGVycm9yIHJlc3BvbnNlOlwiLCBlcnJvckRhdGEpO1xuICAgICAgXG4gICAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGRldGFpbGVkIHZhbGlkYXRpb24gZXJyb3JzXG4gICAgICBpZiAoZXJyb3JEYXRhLmRldGFpbHMpIHtcbiAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlcyA9IE9iamVjdC5lbnRyaWVzKGVycm9yRGF0YS5kZXRhaWxzKVxuICAgICAgICAgIC5tYXAoKFtmaWVsZCwgbWVzc2FnZV0pID0+IGAke2ZpZWxkfTogJHttZXNzYWdlfWApXG4gICAgICAgICAgLmpvaW4oJywgJyk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2VzIHx8IGVycm9yRGF0YS5tZXNzYWdlIHx8IGBFcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBxdWVzdGlvbiB3aXRoIGltYWdlczpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogR2V0IHF1ZXN0aW9ucyBieSBzdWJqZWN0IGFuZCB0b3BpY1xuICogQHBhcmFtIHN1YmplY3RJZCBTdWJqZWN0IElEXG4gKiBAcGFyYW0gdG9waWNJZCBUb3BpYyBJRFxuICogQHJldHVybnMgTGlzdCBvZiBxdWVzdGlvbnNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFF1ZXN0aW9uc0J5U3ViamVjdEFuZFRvcGljKHN1YmplY3RJZDogc3RyaW5nLCB0b3BpY0lkOiBzdHJpbmcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG4gIFxuICB0cnkge1xuICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcbiAgICBjb25zdCB1cmwgPSBgJHtiYXNlVXJsfS9xdWVzdGlvbnM/c3ViamVjdElkPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHN1YmplY3RJZCl9JnRvcGljSWQ9JHtlbmNvZGVVUklDb21wb25lbnQodG9waWNJZCl9YDtcbiAgICBcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWBcbiAgICAgIH1cbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgcXVlc3Rpb25zIGJ5IHN1YmplY3QgYW5kIHRvcGljOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgcXVlc3Rpb25zIGJ5IGRpZmZpY3VsdHlcbiAqIEBwYXJhbSBkaWZmaWN1bHR5IERpZmZpY3VsdHkgbGV2ZWwgKCdlYXN5JywgJ21lZGl1bScsICdoYXJkJylcbiAqIEByZXR1cm5zIExpc3Qgb2YgcXVlc3Rpb25zXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRRdWVzdGlvbnNCeURpZmZpY3VsdHkoZGlmZmljdWx0eTogJ2Vhc3knIHwgJ21lZGl1bScgfCAnaGFyZCcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgdXJsID0gYCR7YmFzZVVybH0vcXVlc3Rpb25zP2RpZmZpY3VsdHk9JHtlbmNvZGVVUklDb21wb25lbnQoZGlmZmljdWx0eSl9YDtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCB7XG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBFcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciBmZXRjaGluZyAke2RpZmZpY3VsdHl9IHF1ZXN0aW9uczpgLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBSZXZpZXcgYSBxdWVzdGlvbiAoYXBwcm92ZS9yZWplY3QpXG4gKiBAcGFyYW0gaWQgUXVlc3Rpb24gSURcbiAqIEBwYXJhbSByZXZpZXdTdGF0dXMgUmV2aWV3IHN0YXR1cyAoJ2FwcHJvdmVkJyBvciAncmVqZWN0ZWQnKVxuICogQHJldHVybnMgVGhlIHVwZGF0ZWQgcXVlc3Rpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJldmlld1F1ZXN0aW9uKGlkOiBzdHJpbmcsIHJldmlld1N0YXR1czogJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9xdWVzdGlvbnMvJHtpZH0vcmV2aWV3YCwge1xuICAgICAgbWV0aG9kOiBcIlBBVENIXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWBcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1czogcmV2aWV3U3RhdHVzIH0pXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZXZpZXdpbmcgcXVlc3Rpb246XCIsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vKipcbiAqIEJ1bGsgdXBsb2FkIHF1ZXN0aW9ucyBmcm9tIFBERlxuICogQHBhcmFtIGZpbGUgUERGIGZpbGUgY29udGFpbmluZyBxdWVzdGlvbnNcbiAqIEBwYXJhbSBzdWJqZWN0SWQgU3ViamVjdCBJRCBmb3IgdGhlIHF1ZXN0aW9uc1xuICogQHBhcmFtIHRvcGljSWQgVG9waWMgSUQgZm9yIHRoZSBxdWVzdGlvbnMgKG9wdGlvbmFsKVxuICogQHJldHVybnMgVGhlIHVwbG9hZCByZXN1bHRcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGJ1bGtVcGxvYWRRdWVzdGlvbnNQREYoXG4gIGZpbGU6IEZpbGUsXG4gIHN1YmplY3RJZDogc3RyaW5nLFxuICB0b3BpY0lkPzogc3RyaW5nXG4pIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcblxuICAgIC8vIEFkZCB0aGUgUERGIGZpbGVcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKTtcblxuICAgIC8vIEFkZCByZXF1aXJlZCBzdWJqZWN0IElEXG4gICAgZm9ybURhdGEuYXBwZW5kKCdzdWJqZWN0SWQnLCBzdWJqZWN0SWQpO1xuXG4gICAgLy8gQWRkIG9wdGlvbmFsIHRvcGljIElEXG4gICAgaWYgKHRvcGljSWQpIHtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgndG9waWNJZCcsIHRvcGljSWQpO1xuICAgIH1cblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vcXVlc3Rpb25zL2J1bGstdXBsb2FkLXBkZmAsIHtcbiAgICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfSxcbiAgICAgIGJvZHk6IGZvcm1EYXRhXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICBjb25zb2xlLmVycm9yKFwiUERGIHVwbG9hZCBlcnJvciByZXNwb25zZTpcIiwgZXJyb3JEYXRhKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgd2UgaGF2ZSBkZXRhaWxlZCB2YWxpZGF0aW9uIGVycm9yc1xuICAgICAgaWYgKGVycm9yRGF0YS5kZXRhaWxzKSB7XG4gICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSBPYmplY3QuZW50cmllcyhlcnJvckRhdGEuZGV0YWlscylcbiAgICAgICAgICAubWFwKChbZmllbGQsIG1lc3NhZ2VdKSA9PiBgJHtmaWVsZH06ICR7bWVzc2FnZX1gKVxuICAgICAgICAgIC5qb2luKCcsICcpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlcyB8fCBlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGxvYWRpbmcgUERGIHF1ZXN0aW9uczpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogQnVsayB1cGxvYWQgY2hlbWljYWwgcXVlc3Rpb25zIGZyb20gUERGIHdpdGggbW9sZWN1bGFyIHN0cnVjdHVyZXNcbiAqIEBwYXJhbSBmaWxlIFBERiBmaWxlIGNvbnRhaW5pbmcgY2hlbWljYWwgcXVlc3Rpb25zXG4gKiBAcGFyYW0gc3ViamVjdElkIFN1YmplY3QgSUQgZm9yIHRoZSBxdWVzdGlvbnNcbiAqIEBwYXJhbSB0b3BpY0lkIFRvcGljIElEIGZvciB0aGUgcXVlc3Rpb25zIChvcHRpb25hbClcbiAqIEByZXR1cm5zIFRoZSB1cGxvYWQgcmVzdWx0IHdpdGggY2hlbWljYWwgZXh0cmFjdGlvbiBtZXRhZGF0YVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYnVsa1VwbG9hZENoZW1pY2FsUXVlc3Rpb25zUERGKFxuICBmaWxlOiBGaWxlLFxuICBzdWJqZWN0SWQ6IHN0cmluZyxcbiAgdG9waWNJZD86IHN0cmluZ1xuKSB7XG4gIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJiYWNrZW5kVG9rZW5cIik7XG4gIGlmICghdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKTtcbiAgfVxuXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG5cbiAgICAvLyBBZGQgdGhlIFBERiBmaWxlXG4gICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSk7XG5cbiAgICAvLyBBZGQgcmVxdWlyZWQgc3ViamVjdCBJRFxuICAgIGZvcm1EYXRhLmFwcGVuZCgnc3ViamVjdElkJywgc3ViamVjdElkKTtcblxuICAgIC8vIEFkZCBvcHRpb25hbCB0b3BpYyBJRFxuICAgIGlmICh0b3BpY0lkKSB7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ3RvcGljSWQnLCB0b3BpY0lkKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L3F1ZXN0aW9ucy9idWxrLXVwbG9hZC1jaGVtaWNhbC1wZGZgLCB7XG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWBcbiAgICAgIH0sXG4gICAgICBib2R5OiBmb3JtRGF0YVxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xuICAgICAgY29uc29sZS5lcnJvcihcIkNoZW1pY2FsIFBERiB1cGxvYWQgZXJyb3IgcmVzcG9uc2U6XCIsIGVycm9yRGF0YSk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHdlIGhhdmUgZGV0YWlsZWQgdmFsaWRhdGlvbiBlcnJvcnNcbiAgICAgIGlmIChlcnJvckRhdGEuZGV0YWlscykge1xuICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2VzID0gT2JqZWN0LmVudHJpZXMoZXJyb3JEYXRhLmRldGFpbHMpXG4gICAgICAgICAgLm1hcCgoW2ZpZWxkLCBtZXNzYWdlXSkgPT4gYCR7ZmllbGR9OiAke21lc3NhZ2V9YClcbiAgICAgICAgICAuam9pbignLCAnKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZXMgfHwgZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBFcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBsb2FkaW5nIGNoZW1pY2FsIFBERiBxdWVzdGlvbnM6XCIsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG5cblxuXG5cblxuXG5cblxuXG4iXSwibmFtZXMiOlsiaGFuZGxlQXBpRXJyb3IiLCJjcmVhdGVTdWNjZXNzUmVzcG9uc2UiLCJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImNyZWF0ZVF1ZXN0aW9uIiwicXVlc3Rpb25EYXRhIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiZGF0YVRvU2VuZCIsImV4cGxhbmF0aW9uIiwidHJpbSIsInN0YXR1cyIsInJldmlld1N0YXR1cyIsInR5cGUiLCJjb25zb2xlIiwibG9nIiwiSlNPTiIsInN0cmluZ2lmeSIsImJhc2VVcmwiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsImVycm9yIiwiZGV0YWlscyIsImVycm9yTWVzc2FnZXMiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwiZmllbGQiLCJtZXNzYWdlIiwiam9pbiIsInJlc3VsdCIsIkVycm9yIiwiZ2V0UXVlc3Rpb25zIiwiZ2V0UXVlc3Rpb25CeUlkIiwiaWQiLCJ1cGRhdGVRdWVzdGlvbiIsInVwZGF0ZVF1ZXN0aW9uV2l0aEltYWdlcyIsInF1ZXN0aW9uSW1hZ2UiLCJvcHRpb25JbWFnZXMiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwiY29udGVudCIsIm9wdGlvbnMiLCJmb3JFYWNoIiwib3B0aW9uIiwiaW5kZXgiLCJhbnN3ZXIiLCJzdWJqZWN0SWQiLCJ0b3BpY0lkIiwiZGlmZmljdWx0eSIsImNyZWF0ZWRCeSIsImtleSIsImZpbGUiLCJkZWxldGVRdWVzdGlvbiIsImNyZWF0ZVF1ZXN0aW9uV2l0aEltYWdlcyIsInBhaXIiLCJnZXRRdWVzdGlvbnNCeVN1YmplY3RBbmRUb3BpYyIsInVybCIsImVuY29kZVVSSUNvbXBvbmVudCIsImdldFF1ZXN0aW9uc0J5RGlmZmljdWx0eSIsInJldmlld1F1ZXN0aW9uIiwiYnVsa1VwbG9hZFF1ZXN0aW9uc1BERiIsImJ1bGtVcGxvYWRDaGVtaWNhbFF1ZXN0aW9uc1BERiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questions.ts\n"));

/***/ })

});