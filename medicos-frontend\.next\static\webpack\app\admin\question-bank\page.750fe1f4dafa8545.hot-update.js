"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Handle review status change\n    const handleReviewStatusChange = async (questionId, reviewStatus)=>{\n        try {\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_9__.reviewQuestion)(questionId, reviewStatus);\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        reviewStatus\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question \".concat(reviewStatus, \" successfully\")\n            });\n        } catch (error) {\n            console.error(\"Error updating question review status:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question review status\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q, questionIndex)=>{\n        var _q_topicId;\n        let parsedOptions = [];\n        // Ensure options is an array and filter out null/undefined values\n        const safeOptions = Array.isArray(q.options) ? q.options.filter((opt)=>opt !== null && opt !== undefined) : [];\n        if (safeOptions.length > 0) {\n            if (typeof safeOptions[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (safeOptions.length === 1 && safeOptions[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = safeOptions[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = safeOptions.map((text, index)=>{\n                        const trimmedText = text.trim();\n                        // Check if the text is a base64 image\n                        if ((0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.isBase64Image)(trimmedText)) {\n                            return {\n                                label: String.fromCharCode(97 + index),\n                                text: '',\n                                imageUrl: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_8__.ensureDataUrl)(trimmedText),\n                                isImageOption: true\n                            };\n                        }\n                        return {\n                            label: String.fromCharCode(97 + index),\n                            text: trimmedText\n                        };\n                    });\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = safeOptions.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt && opt.text || '',\n                        imageUrl: typeof opt === 'object' && opt ? opt.imageUrl : undefined\n                    }));\n            }\n        } else {\n            // Fallback: create empty options if none exist\n            parsedOptions = [\n                {\n                    label: 'a',\n                    text: 'No options available'\n                },\n                {\n                    label: 'b',\n                    text: 'No options available'\n                },\n                {\n                    label: 'c',\n                    text: 'No options available'\n                },\n                {\n                    label: 'd',\n                    text: 'No options available'\n                }\n            ];\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_q_topicId = q.topicId) === null || _q_topicId === void 0 ? void 0 : _q_topicId.name) || \"No Topic\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus,\n            solution: q.solution,\n            hints: q.hints\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange,\n                        onReviewStatusChange: handleReviewStatusChange,\n                        onQuestionDeleted: ()=>{\n                            // Refresh the questions list after deletion\n                            setPagination((prev)=>({\n                                    ...prev,\n                                    currentPage: 1\n                                }));\n                        // The useEffect will automatically refetch when pagination changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL3F1ZXN0aW9uLWJhbmsvcXVlc3Rpb24tYmFuay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQzJEO0FBQ3pEO0FBQ1I7QUFDSztBQUNhO0FBQ0Q7QUFDTDtBQUVnQjtBQUNiO0FBRXJDLFNBQVNnQjs7SUFDdEIsZ0NBQWdDO0lBQ2hDLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHbEIsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNtQixRQUFRQyxVQUFVLEdBQUdwQiwrQ0FBUUEsQ0FBVSxFQUFFO0lBRWhELG9CQUFvQjtJQUNwQixNQUFNLENBQUNxQixpQkFBaUJDLG1CQUFtQixHQUFHdEIsK0NBQVFBLENBQVM7SUFDL0QsTUFBTSxDQUFDdUIsZUFBZUMsaUJBQWlCLEdBQUd4QiwrQ0FBUUEsQ0FBUztJQUMzRCxNQUFNLENBQUN5QixhQUFhQyxlQUFlLEdBQUcxQiwrQ0FBUUEsQ0FBUztJQUV2RCxxQ0FBcUM7SUFDckMsTUFBTSxDQUFDMkIsV0FBV0MsYUFBYSxHQUFHNUIsK0NBQVFBLENBQWdCLEVBQUU7SUFDNUQsTUFBTSxDQUFDNkIsWUFBWUMsY0FBYyxHQUFHOUIsK0NBQVFBLENBQWlCO1FBQzNEK0IsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztJQUNoQjtJQUNBLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHcEMsK0NBQVFBLENBQVM7SUFDakQsTUFBTSxDQUFDcUMsU0FBU0MsV0FBVyxHQUFHdEMsK0NBQVFBLENBQVU7SUFFaEQsNkJBQTZCO0lBQzdCQyxnREFBU0E7a0NBQUM7WUFDUixNQUFNc0M7a0VBQTBCO29CQUM5QixJQUFJO3dCQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO3dCQUM5RSxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSTCxTQUFRLDBCQUF3Qjs0QkFDOURNLFNBQVM7Z0NBQ1AsaUJBQWlCLFVBQStDLE9BQXJDQyxhQUFhQyxPQUFPLENBQUM7NEJBQ2xEO3dCQUNGO3dCQUVBLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFOzRCQUNoQixNQUFNLElBQUlDLE1BQU0sNkJBQTZDLE9BQWhCTixTQUFTTyxNQUFNO3dCQUM5RDt3QkFFQSxNQUFNQyxPQUFPLE1BQU1SLFNBQVNTLElBQUk7d0JBQ2hDbkMsWUFBWWtDO29CQUNkLEVBQUUsT0FBT0UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7d0JBQzFDMUMsK0RBQUtBLENBQUM7NEJBQ0o0QyxPQUFPOzRCQUNQQyxhQUFhOzRCQUNiQyxTQUFTO3dCQUNYO29CQUNGO2dCQUNGOztZQUVBbkI7UUFDRjtpQ0FBRyxFQUFFO0lBRUwscUNBQXFDO0lBQ3JDdEMsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSW9CLG1CQUFtQkEsb0JBQW9CLGdCQUFnQjtnQkFDekQsTUFBTXNDLHFCQUFxQjFDLFNBQVMyQyxJQUFJO2lFQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxHQUFHLEtBQUt6Qzs7Z0JBQ3hELElBQUlzQyxzQkFBc0JBLG1CQUFtQnhDLE1BQU0sRUFBRTtvQkFDbkRDLFVBQVV1QyxtQkFBbUJ4QyxNQUFNO2dCQUNyQyxPQUFPO29CQUNMQyxVQUFVLEVBQUU7Z0JBQ2Q7Z0JBQ0FJLGlCQUFpQjtZQUNuQixPQUFPO2dCQUNMSixVQUFVLEVBQUU7WUFDZDtRQUNGO2lDQUFHO1FBQUNDO1FBQWlCSjtLQUFTO0lBRTlCLCtCQUErQjtJQUMvQmhCLGdEQUFTQTtrQ0FBQztZQUNSLE1BQU04RDt5REFBaUI7b0JBQ3JCekIsV0FBVztvQkFDWCxJQUFJO3dCQUNGLE1BQU1FLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO3dCQUU5RSx5QkFBeUI7d0JBQ3pCLE1BQU11QixTQUFTLElBQUlDO3dCQUNuQixJQUFJNUMsbUJBQW1CQSxvQkFBb0IsZ0JBQWdCMkMsT0FBT0UsTUFBTSxDQUFDLGFBQWE3Qzt3QkFDdEYsSUFBSUUsaUJBQWlCQSxrQkFBa0IsY0FBY3lDLE9BQU9FLE1BQU0sQ0FBQyxXQUFXM0M7d0JBQzlFLElBQUlFLGFBQWF1QyxPQUFPRSxNQUFNLENBQUMsVUFBVXpDO3dCQUN6Q3VDLE9BQU9FLE1BQU0sQ0FBQyxRQUFRckMsV0FBV0UsV0FBVyxDQUFDb0MsUUFBUTt3QkFDckRILE9BQU9FLE1BQU0sQ0FBQyxTQUFTL0IsU0FBU2dDLFFBQVE7d0JBRXhDLE1BQU12QixXQUFXLE1BQU1DLE1BQU0sR0FBd0JtQixPQUFyQnhCLFNBQVEsZUFBK0IsT0FBbEJ3QixPQUFPRyxRQUFRLEtBQU07NEJBQ3hFckIsU0FBUztnQ0FDUCxpQkFBaUIsVUFBK0MsT0FBckNDLGFBQWFDLE9BQU8sQ0FBQzs0QkFDbEQ7d0JBQ0Y7d0JBRUEsSUFBSSxDQUFDSixTQUFTSyxFQUFFLEVBQUU7NEJBQ2hCLE1BQU0sSUFBSUMsTUFBTSw4QkFBOEMsT0FBaEJOLFNBQVNPLE1BQU07d0JBQy9EO3dCQUVBLE1BQU1DLE9BQU8sTUFBTVIsU0FBU1MsSUFBSTt3QkFDaEN6QixhQUFhd0IsS0FBS3pCLFNBQVM7d0JBQzNCRyxjQUFjc0IsS0FBS3ZCLFVBQVU7b0JBQy9CLEVBQUUsT0FBT3lCLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO3dCQUMzQzFDLCtEQUFLQSxDQUFDOzRCQUNKNEMsT0FBTzs0QkFDUEMsYUFBYTs0QkFDYkMsU0FBUzt3QkFDWDtvQkFDRixTQUFVO3dCQUNScEIsV0FBVztvQkFDYjtnQkFDRjs7WUFFQXlCO1FBQ0Y7aUNBQUc7UUFBQzFDO1FBQWlCRTtRQUFlRTtRQUFhSSxXQUFXRSxXQUFXO1FBQUVJO0tBQVM7SUFFbEYscUJBQXFCO0lBQ3JCLE1BQU1pQyxtQkFBbUIsQ0FBQ0M7UUFDeEJ2QyxjQUFjd0MsQ0FBQUEsT0FBUztnQkFDckIsR0FBR0EsSUFBSTtnQkFDUHZDLGFBQWFzQztZQUNmO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUUsdUJBQXVCLENBQUNDO1FBQzVCcEMsWUFBWW9DO1FBQ1oxQyxjQUFjd0MsQ0FBQUEsT0FBUztnQkFDckIsR0FBR0EsSUFBSTtnQkFDUHZDLGFBQWE7Z0JBQ2JHLGNBQWNzQztZQUNoQjtJQUNGO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1DLHlCQUF5QixPQUFPQyxZQUFvQkM7UUFDeEQsSUFBSTtZQUNGLE1BQU1uQyxVQUFVQywyQkFBK0IsSUFBSSxDQUEyQjtZQUM5RSxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBd0I2QixPQUFyQmxDLFNBQVEsZUFBd0IsT0FBWGtDLGFBQWM7Z0JBQ2pFRSxRQUFRO2dCQUNSOUIsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUErQyxPQUFyQ0MsYUFBYUMsT0FBTyxDQUFDO2dCQUNsRDtnQkFDQTZCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUo7Z0JBQVc7WUFDcEM7WUFFQSxJQUFJLENBQUMvQixTQUFTSyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTSw4QkFBOEMsT0FBaEJOLFNBQVNPLE1BQU07WUFDL0Q7WUFFQSxxQkFBcUI7WUFDckJ2QixhQUFhMEMsQ0FBQUEsT0FDWEEsS0FBS1UsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbkIsR0FBRyxLQUFLWSxhQUFhO3dCQUFFLEdBQUdPLENBQUM7d0JBQUVOO29CQUFXLElBQUlNO1lBRzlEckUsK0RBQUtBLENBQUM7Z0JBQ0o0QyxPQUFPO2dCQUNQQyxhQUFhO1lBQ2Y7UUFDRixFQUFFLE9BQU9ILE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQxQywrREFBS0EsQ0FBQztnQkFDSjRDLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTXdCLDJCQUEyQixPQUFPUixZQUFvQlM7UUFDMUQsSUFBSTtZQUNGLE1BQU1wRSxrRUFBY0EsQ0FBQzJELFlBQVlTO1lBRWpDLHFCQUFxQjtZQUNyQnZELGFBQWEwQyxDQUFBQSxPQUNYQSxLQUFLVSxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVuQixHQUFHLEtBQUtZLGFBQWE7d0JBQUUsR0FBR08sQ0FBQzt3QkFBRUU7b0JBQWEsSUFBSUY7WUFHaEVyRSwrREFBS0EsQ0FBQztnQkFDSjRDLE9BQU87Z0JBQ1BDLGFBQWEsWUFBeUIsT0FBYjBCLGNBQWE7WUFDeEM7UUFDRixFQUFFLE9BQU83QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1lBQ3hEMUMsK0RBQUtBLENBQUM7Z0JBQ0o0QyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU0wQixxQkFBMEN6RCxVQUFVcUQsR0FBRyxDQUFDLENBQUNDLEdBQUdJO1lBOEV2REo7UUF2RVQsSUFBSUssZ0JBQWdDLEVBQUU7UUFFdEMsa0VBQWtFO1FBQ2xFLE1BQU1DLGNBQWNDLE1BQU1DLE9BQU8sQ0FBQ1IsRUFBRVMsT0FBTyxJQUFJVCxFQUFFUyxPQUFPLENBQUNDLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsUUFBUSxRQUFRQSxRQUFRQyxhQUFhLEVBQUU7UUFFOUcsSUFBSU4sWUFBWU8sTUFBTSxHQUFHLEdBQUc7WUFDMUIsSUFBSSxPQUFPUCxXQUFXLENBQUMsRUFBRSxLQUFLLFVBQVU7Z0JBQ3RDLGtGQUFrRjtnQkFDbEYsSUFBSUEsWUFBWU8sTUFBTSxLQUFLLEtBQUtQLFdBQVcsQ0FBQyxFQUFFLENBQUNRLFFBQVEsQ0FBQyxNQUFNO29CQUM1RCxnRUFBZ0U7b0JBQ2hFLE1BQU1DLGNBQWMsV0FBeUIsQ0FBQyxFQUFFLENBQUNDLEtBQUssQ0FBQztvQkFDdkRYLGdCQUFnQlUsWUFBWWhCLEdBQUcsQ0FBQyxDQUFDa0IsTUFBTUM7d0JBQ3JDLE1BQU1DLGNBQWNGLEtBQUtHLElBQUk7d0JBRTdCLHNDQUFzQzt3QkFDdEMsSUFBSXhGLGdFQUFhQSxDQUFDdUYsY0FBYzs0QkFDOUIsT0FBTztnQ0FDTEUsT0FBT0MsT0FBT0MsWUFBWSxDQUFDLEtBQUtMO2dDQUNoQ0QsTUFBTTtnQ0FDTk8sVUFBVTNGLGdFQUFhQSxDQUFDc0Y7Z0NBQ3hCTSxlQUFlOzRCQUNqQjt3QkFDRjt3QkFFQSxPQUFPOzRCQUNMSixPQUFPQyxPQUFPQyxZQUFZLENBQUMsS0FBS0w7NEJBQ2hDRCxNQUFNRTt3QkFDUjtvQkFDRjtnQkFDRixPQUFPO29CQUNMLDZFQUE2RTtvQkFDN0VkLGdCQUFnQixZQUEwQk4sR0FBRyxDQUFDLENBQUNrQixNQUFNQzt3QkFDbkQsTUFBTUMsY0FBY0YsS0FBS0csSUFBSTt3QkFFN0Isc0NBQXNDO3dCQUN0QyxJQUFJeEYsZ0VBQWFBLENBQUN1RixjQUFjOzRCQUM5QixPQUFPO2dDQUNMRSxPQUFPQyxPQUFPQyxZQUFZLENBQUMsS0FBS0w7Z0NBQ2hDRCxNQUFNO2dDQUNOTyxVQUFVM0YsZ0VBQWFBLENBQUNzRjtnQ0FDeEJNLGVBQWU7NEJBQ2pCO3dCQUNGO3dCQUVBLE9BQU87NEJBQ0xKLE9BQU9DLE9BQU9DLFlBQVksQ0FBQyxLQUFLTDs0QkFDaENELE1BQU1FO3dCQUNSO29CQUNGO2dCQUNGO1lBQ0YsT0FBTztnQkFDTCw0Q0FBNEM7Z0JBQzVDZCxnQkFBZ0IsWUFBdUJOLEdBQUcsQ0FBQyxDQUFDWSxLQUFLTyxRQUFXO3dCQUMxREcsT0FBT0MsT0FBT0MsWUFBWSxDQUFDLEtBQUtMO3dCQUNoQ0QsTUFBTSxPQUFPTixRQUFRLFdBQVdBLE1BQU0sT0FBUUEsSUFBSU0sSUFBSSxJQUFLO3dCQUMzRE8sVUFBVSxPQUFPYixRQUFRLFlBQVlBLE1BQU1BLElBQUlhLFFBQVEsR0FBR1o7b0JBQzVEO1lBQ0Y7UUFDRixPQUFPO1lBQ0wsK0NBQStDO1lBQy9DUCxnQkFBZ0I7Z0JBQ2Q7b0JBQUVnQixPQUFPO29CQUFLSixNQUFNO2dCQUF1QjtnQkFDM0M7b0JBQUVJLE9BQU87b0JBQUtKLE1BQU07Z0JBQXVCO2dCQUMzQztvQkFBRUksT0FBTztvQkFBS0osTUFBTTtnQkFBdUI7Z0JBQzNDO29CQUFFSSxPQUFPO29CQUFLSixNQUFNO2dCQUF1QjthQUM1QztRQUNIO1FBRUEsT0FBTztZQUNMUyxJQUFJMUIsRUFBRW5CLEdBQUc7WUFDVDhDLFNBQVMzQixFQUFFNEIsU0FBUyxDQUFDQyxJQUFJO1lBQ3pCQyxPQUFPOUIsRUFBQUEsYUFBQUEsRUFBRStCLE9BQU8sY0FBVC9CLGlDQUFBQSxXQUFXNkIsSUFBSSxLQUFJO1lBQzFCWixNQUFNakIsRUFBRWdDLE9BQU87WUFDZnZCLFNBQVNKO1lBQ1RYLFlBQVlNLEVBQUVOLFVBQVUsQ0FBQ3VDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtsQyxFQUFFTixVQUFVLENBQUN5QyxLQUFLLENBQUM7WUFDdEVDLGVBQWVwQyxFQUFFcUMsTUFBTTtZQUN2Qm5DLGNBQWNGLEVBQUVFLFlBQVk7WUFDNUJvQyxVQUFVdEMsRUFBRXNDLFFBQVE7WUFDcEJDLE9BQU92QyxFQUFFdUMsS0FBSztRQUNoQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEOzswQ0FDQyw4REFBQ25CO2dDQUFNb0IsV0FBVTswQ0FBaUM7Ozs7OzswQ0FDbEQsOERBQUN4SCx5REFBTUE7Z0NBQUN5SCxPQUFPdEc7Z0NBQWlCdUcsZUFBZXRHOztrREFDN0MsOERBQUNqQixnRUFBYUE7a0RBQ1osNEVBQUNDLDhEQUFXQTs0Q0FBQ3VILGFBQVk7Ozs7Ozs7Ozs7O2tEQUUzQiw4REFBQzFILGdFQUFhQTs7MERBQ1osOERBQUNDLDZEQUFVQTtnREFBQ3VILE9BQU07MERBQWU7Ozs7Ozs0Q0FDaEMxRyxTQUFTK0QsR0FBRyxDQUFDLENBQUM0Qix3QkFDYiw4REFBQ3hHLDZEQUFVQTtvREFBbUJ1SCxPQUFPZixRQUFROUMsR0FBRzs4REFDN0M4QyxRQUFRRSxJQUFJO21EQURFRixRQUFROUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUXBDLDhEQUFDMkQ7OzBDQUNDLDhEQUFDbkI7Z0NBQU1vQixXQUFVOzBDQUFpQzs7Ozs7OzBDQUNsRCw4REFBQ3hILHlEQUFNQTtnQ0FDTHlILE9BQU9wRztnQ0FDUHFHLGVBQWVwRztnQ0FDZnNHLFVBQVV6RyxvQkFBb0Isa0JBQWtCRixPQUFPMkUsTUFBTSxLQUFLOztrREFFbEUsOERBQUN6RixnRUFBYUE7a0RBQ1osNEVBQUNDLDhEQUFXQTs0Q0FBQ3VILGFBQWF4RyxvQkFBb0IsaUJBQWlCLGlCQUFpQjs7Ozs7Ozs7Ozs7a0RBRWxGLDhEQUFDbEIsZ0VBQWFBOzswREFDWiw4REFBQ0MsNkRBQVVBO2dEQUFDdUgsT0FBTTswREFBYTs7Ozs7OzRDQUM5QnhHLE9BQU82RCxHQUFHLENBQUMsQ0FBQytCLHNCQUNYLDhEQUFDM0csNkRBQVVBO29EQUFpQnVILE9BQU9aLE1BQU1qRCxHQUFHOzhEQUN6Q2lELE1BQU1ELElBQUk7bURBRElDLE1BQU1qRCxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRbEMsOERBQUMyRDs7MENBQ0MsOERBQUNuQjtnQ0FBTW9CLFdBQVU7MENBQWlDOzs7Ozs7MENBQ2xELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNsSCxtRkFBTUE7d0NBQUNrSCxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDbkgsdURBQUtBO3dDQUNKd0gsTUFBSzt3Q0FDTEYsYUFBWTt3Q0FDWkgsV0FBVTt3Q0FDVkMsT0FBT2xHO3dDQUNQdUcsVUFBVSxDQUFDQyxJQUFNdkcsZUFBZXVHLEVBQUVDLE1BQU0sQ0FBQ1AsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3JEdEYsd0JBQ0MsOERBQUNvRjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMvRyxnRUFBZ0JBOzs7OztrQ0FDakIsOERBQUNBLGdFQUFnQkE7Ozs7O2tDQUNqQiw4REFBQ0EsZ0VBQWdCQTs7Ozs7Ozs7Ozt1QkFFakJ5RSxtQkFBbUJVLE1BQU0sR0FBRyxrQkFDOUI7O2tDQUNFLDhEQUFDckYsc0RBQVlBO3dCQUNYa0IsV0FBV3lEO3dCQUNYK0Msb0JBQW9CMUQ7d0JBQ3BCMkQsc0JBQXNCbEQ7d0JBQ3RCbUQsbUJBQW1COzRCQUNqQiw0Q0FBNEM7NEJBQzVDdkcsY0FBY3dDLENBQUFBLE9BQVM7b0NBQUUsR0FBR0EsSUFBSTtvQ0FBRXZDLGFBQWE7Z0NBQUU7d0JBQ2pELG1FQUFtRTt3QkFDckU7Ozs7OztvQkFJREYsV0FBV0ksVUFBVSxHQUFHLG1CQUN2Qiw4REFBQ3ZCLGlFQUFVQTt3QkFDVHFCLGFBQWFGLFdBQVdFLFdBQVc7d0JBQ25DQyxZQUFZSCxXQUFXRyxVQUFVO3dCQUNqQ0csVUFBVUE7d0JBQ1ZGLFlBQVlKLFdBQVdJLFVBQVU7d0JBQ2pDcUcsY0FBY2xFO3dCQUNkbUUsa0JBQWtCaEU7d0JBQ2xCaUUsaUJBQWlCOzRCQUFDOzRCQUFHOzRCQUFJOzRCQUFJO3lCQUFHOzs7Ozs7OzZDQUt0Qyw4REFBQ2Y7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNlO29CQUFFZixXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLL0M7R0F0WHdCMUc7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxtZWRpY29zXFxtZWRpY29zLWZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGFkbWluXFxxdWVzdGlvbi1iYW5rXFxxdWVzdGlvbi1iYW5rLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXHJcbmltcG9ydCB7IFNlYXJjaCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5pbXBvcnQgUXVlc3Rpb25MaXN0IGZyb20gXCIuL3F1ZXN0aW9uLWxpc3RcIlxyXG5pbXBvcnQgeyBQYWdpbmF0aW9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9wYWdpbmF0aW9uXCJcclxuaW1wb3J0IHsgUXVlc3Rpb25Ta2VsZXRvbiB9IGZyb20gXCIuL3F1ZXN0aW9uLXNrZWxldG9uXCJcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdFwiXHJcbmltcG9ydCB7IEFwaVF1ZXN0aW9uLCBGb3JtYXR0ZWRRdWVzdGlvbiwgUGFnaW5hdGlvbkluZm8sIFN1YmplY3QsIFRvcGljIH0gZnJvbSBcIkAvdHlwZXMvcXVlc3Rpb25cIlxyXG5pbXBvcnQgeyBpc0Jhc2U2NEltYWdlLCBlbnN1cmVEYXRhVXJsIH0gZnJvbSBcIkAvdXRpbHMvaW1hZ2VVdGlsc1wiXHJcbmltcG9ydCB7IHJldmlld1F1ZXN0aW9uIH0gZnJvbSBcIkAvbGliL2FwaS9xdWVzdGlvbnNcIlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUXVlc3Rpb25CYW5rKCkge1xyXG4gIC8vIFN0YXRlIGZvciBzdWJqZWN0cyBhbmQgdG9waWNzXHJcbiAgY29uc3QgW3N1YmplY3RzLCBzZXRTdWJqZWN0c10gPSB1c2VTdGF0ZTxTdWJqZWN0W10+KFtdKVxyXG4gIGNvbnN0IFt0b3BpY3MsIHNldFRvcGljc10gPSB1c2VTdGF0ZTxUb3BpY1tdPihbXSlcclxuICBcclxuICAvLyBTdGF0ZSBmb3IgZmlsdGVyc1xyXG4gIGNvbnN0IFtzZWxlY3RlZFN1YmplY3QsIHNldFNlbGVjdGVkU3ViamVjdF0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiYWxsX3N1YmplY3RzXCIpXHJcbiAgY29uc3QgW3NlbGVjdGVkVG9waWMsIHNldFNlbGVjdGVkVG9waWNdID0gdXNlU3RhdGU8c3RyaW5nPihcImFsbF90b3BpY3NcIilcclxuICBjb25zdCBbc2VhcmNoUXVlcnksIHNldFNlYXJjaFF1ZXJ5XSA9IHVzZVN0YXRlPHN0cmluZz4oXCJcIilcclxuICBcclxuICAvLyBTdGF0ZSBmb3IgcXVlc3Rpb25zIGFuZCBwYWdpbmF0aW9uXHJcbiAgY29uc3QgW3F1ZXN0aW9ucywgc2V0UXVlc3Rpb25zXSA9IHVzZVN0YXRlPEFwaVF1ZXN0aW9uW10+KFtdKVxyXG4gIGNvbnN0IFtwYWdpbmF0aW9uLCBzZXRQYWdpbmF0aW9uXSA9IHVzZVN0YXRlPFBhZ2luYXRpb25JbmZvPih7XHJcbiAgICBjdXJyZW50UGFnZTogMSxcclxuICAgIHRvdGFsUGFnZXM6IDEsXHJcbiAgICB0b3RhbEl0ZW1zOiAwLFxyXG4gICAgaXRlbXNQZXJQYWdlOiAxMFxyXG4gIH0pXHJcbiAgY29uc3QgW3BhZ2VTaXplLCBzZXRQYWdlU2l6ZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDEwKVxyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpXHJcblxyXG4gIC8vIEZldGNoIHN1YmplY3RzIHdpdGggdG9waWNzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoU3ViamVjdHNXaXRoVG9waWNzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L3N1YmplY3RzL3dpdGgtdG9waWNzYCwge1xyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpfWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBcclxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBzdWJqZWN0czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0U3ViamVjdHMoZGF0YSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHN1YmplY3RzOlwiLCBlcnJvcik7XHJcbiAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBsb2FkIHN1YmplY3RzLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxyXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgICBcclxuICAgIGZldGNoU3ViamVjdHNXaXRoVG9waWNzKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBVcGRhdGUgdG9waWNzIHdoZW4gc3ViamVjdCBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzZWxlY3RlZFN1YmplY3QgJiYgc2VsZWN0ZWRTdWJqZWN0ICE9PSBcImFsbF9zdWJqZWN0c1wiKSB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkU3ViamVjdE9iaiA9IHN1YmplY3RzLmZpbmQocyA9PiBzLl9pZCA9PT0gc2VsZWN0ZWRTdWJqZWN0KTtcclxuICAgICAgaWYgKHNlbGVjdGVkU3ViamVjdE9iaiAmJiBzZWxlY3RlZFN1YmplY3RPYmoudG9waWNzKSB7XHJcbiAgICAgICAgc2V0VG9waWNzKHNlbGVjdGVkU3ViamVjdE9iai50b3BpY3MpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldFRvcGljcyhbXSk7XHJcbiAgICAgIH1cclxuICAgICAgc2V0U2VsZWN0ZWRUb3BpYyhcImFsbF90b3BpY3NcIik7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRUb3BpY3MoW10pO1xyXG4gICAgfVxyXG4gIH0sIFtzZWxlY3RlZFN1YmplY3QsIHN1YmplY3RzXSk7XHJcblxyXG4gIC8vIEZldGNoIHF1ZXN0aW9ucyB3aXRoIGZpbHRlcnNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hRdWVzdGlvbnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIEJ1aWxkIHF1ZXJ5IHBhcmFtZXRlcnNcclxuICAgICAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XHJcbiAgICAgICAgaWYgKHNlbGVjdGVkU3ViamVjdCAmJiBzZWxlY3RlZFN1YmplY3QgIT09IFwiYWxsX3N1YmplY3RzXCIpIHBhcmFtcy5hcHBlbmQoJ3N1YmplY3RJZCcsIHNlbGVjdGVkU3ViamVjdCk7XHJcbiAgICAgICAgaWYgKHNlbGVjdGVkVG9waWMgJiYgc2VsZWN0ZWRUb3BpYyAhPT0gXCJhbGxfdG9waWNzXCIpIHBhcmFtcy5hcHBlbmQoJ3RvcGljSWQnLCBzZWxlY3RlZFRvcGljKTtcclxuICAgICAgICBpZiAoc2VhcmNoUXVlcnkpIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaFF1ZXJ5KTtcclxuICAgICAgICBwYXJhbXMuYXBwZW5kKCdwYWdlJywgcGFnaW5hdGlvbi5jdXJyZW50UGFnZS50b1N0cmluZygpKTtcclxuICAgICAgICBwYXJhbXMuYXBwZW5kKCdsaW1pdCcsIHBhZ2VTaXplLnRvU3RyaW5nKCkpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vcXVlc3Rpb25zPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCwge1xyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpfWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBcclxuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBxdWVzdGlvbnM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHNldFF1ZXN0aW9ucyhkYXRhLnF1ZXN0aW9ucyk7XHJcbiAgICAgICAgc2V0UGFnaW5hdGlvbihkYXRhLnBhZ2luYXRpb24pO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBxdWVzdGlvbnM6XCIsIGVycm9yKTtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGxvYWQgcXVlc3Rpb25zLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxyXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgICBcclxuICAgIGZldGNoUXVlc3Rpb25zKCk7XHJcbiAgfSwgW3NlbGVjdGVkU3ViamVjdCwgc2VsZWN0ZWRUb3BpYywgc2VhcmNoUXVlcnksIHBhZ2luYXRpb24uY3VycmVudFBhZ2UsIHBhZ2VTaXplXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBwYWdlIGNoYW5nZVxyXG4gIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAocGFnZU51bWJlcjogbnVtYmVyKSA9PiB7XHJcbiAgICBzZXRQYWdpbmF0aW9uKHByZXYgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgY3VycmVudFBhZ2U6IHBhZ2VOdW1iZXJcclxuICAgIH0pKTtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgcGFnZSBzaXplIGNoYW5nZVxyXG4gIGNvbnN0IGhhbmRsZVBhZ2VTaXplQ2hhbmdlID0gKG5ld1BhZ2VTaXplOiBudW1iZXIpID0+IHtcclxuICAgIHNldFBhZ2VTaXplKG5ld1BhZ2VTaXplKTtcclxuICAgIHNldFBhZ2luYXRpb24ocHJldiA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICBjdXJyZW50UGFnZTogMSwgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGNoYW5naW5nIHBhZ2Ugc2l6ZVxyXG4gICAgICBpdGVtc1BlclBhZ2U6IG5ld1BhZ2VTaXplXHJcbiAgICB9KSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGRpZmZpY3VsdHkgY2hhbmdlXHJcbiAgY29uc3QgaGFuZGxlRGlmZmljdWx0eUNoYW5nZSA9IGFzeW5jIChxdWVzdGlvbklkOiBzdHJpbmcsIGRpZmZpY3VsdHk6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L3F1ZXN0aW9ucy8ke3F1ZXN0aW9uSWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJQQVRDSFwiLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHtsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKX1gXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGRpZmZpY3VsdHkgfSlcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gdXBkYXRlIHF1ZXN0aW9uOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlXHJcbiAgICAgIHNldFF1ZXN0aW9ucyhwcmV2ID0+XHJcbiAgICAgICAgcHJldi5tYXAocSA9PiBxLl9pZCA9PT0gcXVlc3Rpb25JZCA/IHsgLi4ucSwgZGlmZmljdWx0eSB9IDogcSlcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJTdWNjZXNzXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiUXVlc3Rpb24gZGlmZmljdWx0eSB1cGRhdGVkIHN1Y2Nlc3NmdWxseVwiLFxyXG4gICAgICB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGRhdGluZyBxdWVzdGlvbiBkaWZmaWN1bHR5OlwiLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byB1cGRhdGUgcXVlc3Rpb24gZGlmZmljdWx0eVwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgcmV2aWV3IHN0YXR1cyBjaGFuZ2VcclxuICBjb25zdCBoYW5kbGVSZXZpZXdTdGF0dXNDaGFuZ2UgPSBhc3luYyAocXVlc3Rpb25JZDogc3RyaW5nLCByZXZpZXdTdGF0dXM6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgcmV2aWV3UXVlc3Rpb24ocXVlc3Rpb25JZCwgcmV2aWV3U3RhdHVzIGFzICdhcHByb3ZlZCcgfCAncmVqZWN0ZWQnKTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSBsb2NhbCBzdGF0ZVxyXG4gICAgICBzZXRRdWVzdGlvbnMocHJldiA9PlxyXG4gICAgICAgIHByZXYubWFwKHEgPT4gcS5faWQgPT09IHF1ZXN0aW9uSWQgPyB7IC4uLnEsIHJldmlld1N0YXR1cyB9IDogcSlcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJTdWNjZXNzXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGBRdWVzdGlvbiAke3Jldmlld1N0YXR1c30gc3VjY2Vzc2Z1bGx5YCxcclxuICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBkYXRpbmcgcXVlc3Rpb24gcmV2aWV3IHN0YXR1czpcIiwgZXJyb3IpO1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gdXBkYXRlIHF1ZXN0aW9uIHJldmlldyBzdGF0dXNcIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRm9ybWF0IHF1ZXN0aW9ucyBmb3IgdGhlIFF1ZXN0aW9uTGlzdCBjb21wb25lbnRcclxuICBjb25zdCBmb3JtYXR0ZWRRdWVzdGlvbnM6IEZvcm1hdHRlZFF1ZXN0aW9uW10gPSBxdWVzdGlvbnMubWFwKChxLCBxdWVzdGlvbkluZGV4KSA9PiB7XHJcbiAgICAvLyBQYXJzZSBvcHRpb25zIC0gdGhleSBtaWdodCBiZSBhIHN0cmluZyBhcnJheSBvciBhbiBhcnJheSBvZiBvYmplY3RzXHJcbiAgICBpbnRlcmZhY2UgUGFyc2VkT3B0aW9uIHtcclxuICAgICAgbGFiZWw6IHN0cmluZztcclxuICAgICAgdGV4dDogc3RyaW5nO1xyXG4gICAgICBpbWFnZVVybD86IHN0cmluZztcclxuICAgIH1cclxuICAgIGxldCBwYXJzZWRPcHRpb25zOiBQYXJzZWRPcHRpb25bXSA9IFtdO1xyXG5cclxuICAgIC8vIEVuc3VyZSBvcHRpb25zIGlzIGFuIGFycmF5IGFuZCBmaWx0ZXIgb3V0IG51bGwvdW5kZWZpbmVkIHZhbHVlc1xyXG4gICAgY29uc3Qgc2FmZU9wdGlvbnMgPSBBcnJheS5pc0FycmF5KHEub3B0aW9ucykgPyBxLm9wdGlvbnMuZmlsdGVyKG9wdCA9PiBvcHQgIT09IG51bGwgJiYgb3B0ICE9PSB1bmRlZmluZWQpIDogW107XHJcblxyXG4gICAgaWYgKHNhZmVPcHRpb25zLmxlbmd0aCA+IDApIHtcclxuICAgICAgaWYgKHR5cGVvZiBzYWZlT3B0aW9uc1swXSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgICAvLyBDaGVjayBpZiBpdCdzIGEgc2luZ2xlIGNvbW1hLXNlcGFyYXRlZCBzdHJpbmcgb3IgYW4gYXJyYXkgb2YgaW5kaXZpZHVhbCBzdHJpbmdzXHJcbiAgICAgICAgaWYgKHNhZmVPcHRpb25zLmxlbmd0aCA9PT0gMSAmJiBzYWZlT3B0aW9uc1swXS5pbmNsdWRlcygnLCcpKSB7XHJcbiAgICAgICAgICAvLyBTaW5nbGUgY29tbWEtc2VwYXJhdGVkIHN0cmluZzogW1wiUGFyaXMsTG9uZG9uLEJlcmxpbixNYWRyaWRcIl1cclxuICAgICAgICAgIGNvbnN0IG9wdGlvblRleHRzID0gKHNhZmVPcHRpb25zIGFzIHN0cmluZ1tdKVswXS5zcGxpdCgnLCcpO1xyXG4gICAgICAgICAgcGFyc2VkT3B0aW9ucyA9IG9wdGlvblRleHRzLm1hcCgodGV4dCwgaW5kZXgpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdHJpbW1lZFRleHQgPSB0ZXh0LnRyaW0oKTtcclxuXHJcbiAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoZSB0ZXh0IGlzIGEgYmFzZTY0IGltYWdlXHJcbiAgICAgICAgICAgIGlmIChpc0Jhc2U2NEltYWdlKHRyaW1tZWRUZXh0KSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogU3RyaW5nLmZyb21DaGFyQ29kZSg5NyArIGluZGV4KSwgLy8gYSwgYiwgYywgZC4uLlxyXG4gICAgICAgICAgICAgICAgdGV4dDogJycsIC8vIEVtcHR5IHRleHQgc2luY2UgdGhpcyBpcyBhbiBpbWFnZSBvcHRpb25cclxuICAgICAgICAgICAgICAgIGltYWdlVXJsOiBlbnN1cmVEYXRhVXJsKHRyaW1tZWRUZXh0KSxcclxuICAgICAgICAgICAgICAgIGlzSW1hZ2VPcHRpb246IHRydWVcclxuICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIGxhYmVsOiBTdHJpbmcuZnJvbUNoYXJDb2RlKDk3ICsgaW5kZXgpLCAvLyBhLCBiLCBjLCBkLi4uXHJcbiAgICAgICAgICAgICAgdGV4dDogdHJpbW1lZFRleHRcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBBcnJheSBvZiBpbmRpdmlkdWFsIHN0cmluZ3M6IFtcIkNlcmVicnVtXCIsIFwiQ2VyZWJlbGx1bVwiLCBcIk1lZHVsbGFcIiwgXCJQb25zXCJdXHJcbiAgICAgICAgICBwYXJzZWRPcHRpb25zID0gKHNhZmVPcHRpb25zIGFzIHN0cmluZ1tdKS5tYXAoKHRleHQsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRyaW1tZWRUZXh0ID0gdGV4dC50cmltKCk7XHJcblxyXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgdGV4dCBpcyBhIGJhc2U2NCBpbWFnZVxyXG4gICAgICAgICAgICBpZiAoaXNCYXNlNjRJbWFnZSh0cmltbWVkVGV4dCkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IFN0cmluZy5mcm9tQ2hhckNvZGUoOTcgKyBpbmRleCksIC8vIGEsIGIsIGMsIGQuLi5cclxuICAgICAgICAgICAgICAgIHRleHQ6ICcnLCAvLyBFbXB0eSB0ZXh0IHNpbmNlIHRoaXMgaXMgYW4gaW1hZ2Ugb3B0aW9uXHJcbiAgICAgICAgICAgICAgICBpbWFnZVVybDogZW5zdXJlRGF0YVVybCh0cmltbWVkVGV4dCksXHJcbiAgICAgICAgICAgICAgICBpc0ltYWdlT3B0aW9uOiB0cnVlXHJcbiAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICBsYWJlbDogU3RyaW5nLmZyb21DaGFyQ29kZSg5NyArIGluZGV4KSwgLy8gYSwgYiwgYywgZC4uLlxyXG4gICAgICAgICAgICAgIHRleHQ6IHRyaW1tZWRUZXh0XHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gSWYgb3B0aW9ucyBpcyBhbHJlYWR5IGFuIGFycmF5IG9mIG9iamVjdHNcclxuICAgICAgICBwYXJzZWRPcHRpb25zID0gKHNhZmVPcHRpb25zIGFzIGFueVtdKS5tYXAoKG9wdCwgaW5kZXgpID0+ICh7XHJcbiAgICAgICAgICBsYWJlbDogU3RyaW5nLmZyb21DaGFyQ29kZSg5NyArIGluZGV4KSxcclxuICAgICAgICAgIHRleHQ6IHR5cGVvZiBvcHQgPT09ICdzdHJpbmcnID8gb3B0IDogKG9wdCAmJiBvcHQudGV4dCkgfHwgJycsXHJcbiAgICAgICAgICBpbWFnZVVybDogdHlwZW9mIG9wdCA9PT0gJ29iamVjdCcgJiYgb3B0ID8gb3B0LmltYWdlVXJsIDogdW5kZWZpbmVkXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBGYWxsYmFjazogY3JlYXRlIGVtcHR5IG9wdGlvbnMgaWYgbm9uZSBleGlzdFxyXG4gICAgICBwYXJzZWRPcHRpb25zID0gW1xyXG4gICAgICAgIHsgbGFiZWw6ICdhJywgdGV4dDogJ05vIG9wdGlvbnMgYXZhaWxhYmxlJyB9LFxyXG4gICAgICAgIHsgbGFiZWw6ICdiJywgdGV4dDogJ05vIG9wdGlvbnMgYXZhaWxhYmxlJyB9LFxyXG4gICAgICAgIHsgbGFiZWw6ICdjJywgdGV4dDogJ05vIG9wdGlvbnMgYXZhaWxhYmxlJyB9LFxyXG4gICAgICAgIHsgbGFiZWw6ICdkJywgdGV4dDogJ05vIG9wdGlvbnMgYXZhaWxhYmxlJyB9XHJcbiAgICAgIF07XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgaWQ6IHEuX2lkLFxyXG4gICAgICBzdWJqZWN0OiBxLnN1YmplY3RJZC5uYW1lLFxyXG4gICAgICB0b3BpYzogcS50b3BpY0lkPy5uYW1lIHx8IFwiTm8gVG9waWNcIixcclxuICAgICAgdGV4dDogcS5jb250ZW50LFxyXG4gICAgICBvcHRpb25zOiBwYXJzZWRPcHRpb25zLFxyXG4gICAgICBkaWZmaWN1bHR5OiBxLmRpZmZpY3VsdHkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBxLmRpZmZpY3VsdHkuc2xpY2UoMSksIC8vIENhcGl0YWxpemVcclxuICAgICAgY29ycmVjdEFuc3dlcjogcS5hbnN3ZXIsXHJcbiAgICAgIHJldmlld1N0YXR1czogcS5yZXZpZXdTdGF0dXMsXHJcbiAgICAgIHNvbHV0aW9uOiBxLnNvbHV0aW9uLFxyXG4gICAgICBoaW50czogcS5oaW50c1xyXG4gICAgfTtcclxuICB9KTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgIHsvKiBIZWFkZXIgRmlsdGVycyAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTFcIj5TdWJqZWN0PC9sYWJlbD5cclxuICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NlbGVjdGVkU3ViamVjdH0gb25WYWx1ZUNoYW5nZT17c2V0U2VsZWN0ZWRTdWJqZWN0fT5cclxuICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPVwiU2VsZWN0IFN1YmplY3RcIiAvPlxyXG4gICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsX3N1YmplY3RzXCI+QWxsIFN1YmplY3RzPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgIHtzdWJqZWN0cy5tYXAoKHN1YmplY3QpID0+IChcclxuICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17c3ViamVjdC5faWR9IHZhbHVlPXtzdWJqZWN0Ll9pZH0+XHJcbiAgICAgICAgICAgICAgICAgIHtzdWJqZWN0Lm5hbWV9XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cclxuICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gbWItMVwiPlRvcGljPC9sYWJlbD5cclxuICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkVG9waWN9XHJcbiAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9e3NldFNlbGVjdGVkVG9waWN9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtzZWxlY3RlZFN1YmplY3QgPT09IFwiYWxsX3N1YmplY3RzXCIgfHwgdG9waWNzLmxlbmd0aCA9PT0gMH1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPXtzZWxlY3RlZFN1YmplY3QgIT09IFwiYWxsX3N1YmplY3RzXCIgPyBcIlNlbGVjdCBUb3BpY1wiIDogXCJTZWxlY3QgU3ViamVjdCBGaXJzdFwifSAvPlxyXG4gICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsX3RvcGljc1wiPkFsbCBUb3BpY3M8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAge3RvcGljcy5tYXAoKHRvcGljKSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e3RvcGljLl9pZH0gdmFsdWU9e3RvcGljLl9pZH0+XHJcbiAgICAgICAgICAgICAgICAgIHt0b3BpYy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICA8L1NlbGVjdD5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTFcIj5TZWFyY2g8L2xhYmVsPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMi41IHRvcC0yLjUgaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxyXG4gICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggcXVlc3Rpb25zLi4uXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC04XCJcclxuICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogUXVlc3Rpb24gTGlzdCAqL31cclxuICAgICAge2xvYWRpbmcgPyAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgIDxRdWVzdGlvblNrZWxldG9uIC8+XHJcbiAgICAgICAgICA8UXVlc3Rpb25Ta2VsZXRvbiAvPlxyXG4gICAgICAgICAgPFF1ZXN0aW9uU2tlbGV0b24gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKSA6IGZvcm1hdHRlZFF1ZXN0aW9ucy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICA8UXVlc3Rpb25MaXN0XHJcbiAgICAgICAgICAgIHF1ZXN0aW9ucz17Zm9ybWF0dGVkUXVlc3Rpb25zfVxyXG4gICAgICAgICAgICBvbkRpZmZpY3VsdHlDaGFuZ2U9e2hhbmRsZURpZmZpY3VsdHlDaGFuZ2V9XHJcbiAgICAgICAgICAgIG9uUmV2aWV3U3RhdHVzQ2hhbmdlPXtoYW5kbGVSZXZpZXdTdGF0dXNDaGFuZ2V9XHJcbiAgICAgICAgICAgIG9uUXVlc3Rpb25EZWxldGVkPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgLy8gUmVmcmVzaCB0aGUgcXVlc3Rpb25zIGxpc3QgYWZ0ZXIgZGVsZXRpb25cclxuICAgICAgICAgICAgICBzZXRQYWdpbmF0aW9uKHByZXYgPT4gKHsgLi4ucHJldiwgY3VycmVudFBhZ2U6IDEgfSkpO1xyXG4gICAgICAgICAgICAgIC8vIFRoZSB1c2VFZmZlY3Qgd2lsbCBhdXRvbWF0aWNhbGx5IHJlZmV0Y2ggd2hlbiBwYWdpbmF0aW9uIGNoYW5nZXNcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgey8qIFBhZ2luYXRpb24gKi99XHJcbiAgICAgICAgICB7cGFnaW5hdGlvbi50b3RhbEl0ZW1zID4gMCAmJiAoXHJcbiAgICAgICAgICAgIDxQYWdpbmF0aW9uXHJcbiAgICAgICAgICAgICAgY3VycmVudFBhZ2U9e3BhZ2luYXRpb24uY3VycmVudFBhZ2V9XHJcbiAgICAgICAgICAgICAgdG90YWxQYWdlcz17cGFnaW5hdGlvbi50b3RhbFBhZ2VzfVxyXG4gICAgICAgICAgICAgIHBhZ2VTaXplPXtwYWdlU2l6ZX1cclxuICAgICAgICAgICAgICB0b3RhbEl0ZW1zPXtwYWdpbmF0aW9uLnRvdGFsSXRlbXN9XHJcbiAgICAgICAgICAgICAgb25QYWdlQ2hhbmdlPXtoYW5kbGVQYWdlQ2hhbmdlfVxyXG4gICAgICAgICAgICAgIG9uUGFnZVNpemVDaGFuZ2U9e2hhbmRsZVBhZ2VTaXplQ2hhbmdlfVxyXG4gICAgICAgICAgICAgIHBhZ2VTaXplT3B0aW9ucz17WzUsIDEwLCAyMCwgNTBdfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8Lz5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPk5vIHF1ZXN0aW9ucyBmb3VuZC4gVHJ5IGFkanVzdGluZyB5b3VyIGZpbHRlcnMuPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiSW5wdXQiLCJTZWFyY2giLCJRdWVzdGlvbkxpc3QiLCJQYWdpbmF0aW9uIiwiUXVlc3Rpb25Ta2VsZXRvbiIsInRvYXN0IiwiaXNCYXNlNjRJbWFnZSIsImVuc3VyZURhdGFVcmwiLCJyZXZpZXdRdWVzdGlvbiIsIlF1ZXN0aW9uQmFuayIsInN1YmplY3RzIiwic2V0U3ViamVjdHMiLCJ0b3BpY3MiLCJzZXRUb3BpY3MiLCJzZWxlY3RlZFN1YmplY3QiLCJzZXRTZWxlY3RlZFN1YmplY3QiLCJzZWxlY3RlZFRvcGljIiwic2V0U2VsZWN0ZWRUb3BpYyIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJxdWVzdGlvbnMiLCJzZXRRdWVzdGlvbnMiLCJwYWdpbmF0aW9uIiwic2V0UGFnaW5hdGlvbiIsImN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInRvdGFsSXRlbXMiLCJpdGVtc1BlclBhZ2UiLCJwYWdlU2l6ZSIsInNldFBhZ2VTaXplIiwibG9hZGluZyIsInNldExvYWRpbmciLCJmZXRjaFN1YmplY3RzV2l0aFRvcGljcyIsImJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsInJlc3BvbnNlIiwiZmV0Y2giLCJoZWFkZXJzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50Iiwic2VsZWN0ZWRTdWJqZWN0T2JqIiwiZmluZCIsInMiLCJfaWQiLCJmZXRjaFF1ZXN0aW9ucyIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsImFwcGVuZCIsInRvU3RyaW5nIiwiaGFuZGxlUGFnZUNoYW5nZSIsInBhZ2VOdW1iZXIiLCJwcmV2IiwiaGFuZGxlUGFnZVNpemVDaGFuZ2UiLCJuZXdQYWdlU2l6ZSIsImhhbmRsZURpZmZpY3VsdHlDaGFuZ2UiLCJxdWVzdGlvbklkIiwiZGlmZmljdWx0eSIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibWFwIiwicSIsImhhbmRsZVJldmlld1N0YXR1c0NoYW5nZSIsInJldmlld1N0YXR1cyIsImZvcm1hdHRlZFF1ZXN0aW9ucyIsInF1ZXN0aW9uSW5kZXgiLCJwYXJzZWRPcHRpb25zIiwic2FmZU9wdGlvbnMiLCJBcnJheSIsImlzQXJyYXkiLCJvcHRpb25zIiwiZmlsdGVyIiwib3B0IiwidW5kZWZpbmVkIiwibGVuZ3RoIiwiaW5jbHVkZXMiLCJvcHRpb25UZXh0cyIsInNwbGl0IiwidGV4dCIsImluZGV4IiwidHJpbW1lZFRleHQiLCJ0cmltIiwibGFiZWwiLCJTdHJpbmciLCJmcm9tQ2hhckNvZGUiLCJpbWFnZVVybCIsImlzSW1hZ2VPcHRpb24iLCJpZCIsInN1YmplY3QiLCJzdWJqZWN0SWQiLCJuYW1lIiwidG9waWMiLCJ0b3BpY0lkIiwiY29udGVudCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJjb3JyZWN0QW5zd2VyIiwiYW5zd2VyIiwic29sdXRpb24iLCJoaW50cyIsImRpdiIsImNsYXNzTmFtZSIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJ0eXBlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib25EaWZmaWN1bHR5Q2hhbmdlIiwib25SZXZpZXdTdGF0dXNDaGFuZ2UiLCJvblF1ZXN0aW9uRGVsZXRlZCIsIm9uUGFnZUNoYW5nZSIsIm9uUGFnZVNpemVDaGFuZ2UiLCJwYWdlU2l6ZU9wdGlvbnMiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});