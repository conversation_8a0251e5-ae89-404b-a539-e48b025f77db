#!/usr/bin/env python3
"""
Adobe Access Token Generator
Generates and optionally saves Adobe PDF Services access token
"""

import os
import sys
import json
import requests
from datetime import datetime, timedelta

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def load_credentials(credentials_file='pdfservices-api-credentials.json'):
    """Load Adobe credentials from file"""
    try:
        if not os.path.exists(credentials_file):
            raise FileNotFoundError(f"Credentials file not found: {credentials_file}")
        
        with open(credentials_file, 'r') as f:
            credentials = json.load(f)
        
        client_credentials = credentials.get('client_credentials', {})
        client_id = client_credentials.get('client_id')
        client_secret = client_credentials.get('client_secret')
        
        if not client_id or not client_secret:
            raise ValueError("Missing client_id or client_secret in credentials file")
        
        return client_id, client_secret
        
    except Exception as e:
        log_print(f"❌ Error loading credentials: {e}")
        return None, None

def generate_access_token(client_id, client_secret):
    """Generate access token from Adobe"""
    try:
        log_print("🔑 Generating Adobe PDF Services access token...")
        
        token_url = "https://pdf-services.adobe.io/token"
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'client_id': client_id,
            'client_secret': client_secret
        }
        
        response = requests.post(token_url, headers=headers, data=data)
        response.raise_for_status()
        
        token_data = response.json()
        access_token = token_data.get('access_token')
        expires_in = token_data.get('expires_in', 86400)  # Default 24 hours
        
        if not access_token:
            raise ValueError("No access token in response")
        
        # Calculate expiration time
        expiration_time = datetime.now() + timedelta(seconds=expires_in)
        
        log_print(f"✅ Access token generated successfully")
        log_print(f"🕒 Token expires at: {expiration_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return access_token, expiration_time
        
    except Exception as e:
        log_print(f"❌ Error generating access token: {e}")
        return None, None

def save_token_to_file(access_token, expiration_time, filename='adobe_access_token.json'):
    """Save access token to file with expiration info"""
    try:
        token_data = {
            'access_token': access_token,
            'expires_at': expiration_time.isoformat(),
            'generated_at': datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(token_data, f, indent=2)
        
        log_print(f"💾 Token saved to: {filename}")
        return True
        
    except Exception as e:
        log_print(f"❌ Error saving token: {e}")
        return False

def load_token_from_file(filename='adobe_access_token.json'):
    """Load access token from file and check if it's still valid"""
    try:
        if not os.path.exists(filename):
            return None, None
        
        with open(filename, 'r') as f:
            token_data = json.load(f)
        
        access_token = token_data.get('access_token')
        expires_at_str = token_data.get('expires_at')
        
        if not access_token or not expires_at_str:
            return None, None
        
        expires_at = datetime.fromisoformat(expires_at_str)
        
        # Check if token is still valid (with 5-minute buffer)
        if datetime.now() + timedelta(minutes=5) < expires_at:
            log_print(f"✅ Valid token found in {filename}")
            log_print(f"🕒 Expires at: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
            return access_token, expires_at
        else:
            log_print(f"⚠️ Token in {filename} has expired")
            return None, None
            
    except Exception as e:
        log_print(f"❌ Error loading token from file: {e}")
        return None, None

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate Adobe PDF Services access token")
    parser.add_argument("--credentials", default="pdfservices-api-credentials.json", 
                       help="Path to credentials file")
    parser.add_argument("--save", action="store_true", 
                       help="Save token to file")
    parser.add_argument("--output", default="adobe_access_token.json",
                       help="Output file for saved token")
    parser.add_argument("--check", action="store_true",
                       help="Check existing token file")
    parser.add_argument("--env", action="store_true",
                       help="Show environment variable command")
    
    args = parser.parse_args()
    
    log_print("🚀 Adobe PDF Services Access Token Generator")
    log_print("=" * 50)
    
    # Check existing token if requested
    if args.check:
        log_print(f"\n🔍 Checking existing token in {args.output}...")
        token, expires_at = load_token_from_file(args.output)
        if token:
            log_print(f"✅ Valid token found")
            if args.env:
                log_print(f"\n💡 To use as environment variable:")
                log_print(f"export ADOBE_ACCESS_TOKEN='{token}'")
            return
        else:
            log_print("❌ No valid token found, generating new one...")
    
    # Load credentials
    log_print(f"\n🔑 Loading credentials from {args.credentials}...")
    client_id, client_secret = load_credentials(args.credentials)
    
    if not client_id or not client_secret:
        log_print("❌ Cannot proceed without valid credentials")
        sys.exit(1)
    
    log_print(f"✅ Credentials loaded - Client ID: {client_id[:8]}...")
    
    # Generate token
    log_print("\n🔄 Generating access token...")
    access_token, expiration_time = generate_access_token(client_id, client_secret)
    
    if not access_token:
        log_print("❌ Failed to generate access token")
        sys.exit(1)
    
    # Display token
    log_print(f"\n🎯 Generated Access Token:")
    log_print(f"Token: {access_token}")
    log_print(f"Length: {len(access_token)} characters")
    
    # Save to file if requested
    if args.save:
        log_print(f"\n💾 Saving token to {args.output}...")
        if save_token_to_file(access_token, expiration_time, args.output):
            log_print("✅ Token saved successfully")
        else:
            log_print("❌ Failed to save token")
    
    # Show usage options
    log_print(f"\n💡 Usage Options:")
    log_print(f"1. Environment Variable:")
    if os.name == 'nt':  # Windows
        log_print(f"   set ADOBE_ACCESS_TOKEN={access_token}")
    else:  # Linux/Mac
        log_print(f"   export ADOBE_ACCESS_TOKEN='{access_token}'")
    
    log_print(f"\n2. In Python Code:")
    log_print(f"   extractor = AdobePDFExtractor(access_token='{access_token}')")
    
    if args.save:
        log_print(f"\n3. Load from saved file:")
        log_print(f"   # Token saved in {args.output}")
        log_print(f"   # Will be automatically loaded by the system")
    
    log_print(f"\n⚠️ Note: Token expires at {expiration_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_print("🔄 Run this script again when the token expires")

if __name__ == "__main__":
    main()
