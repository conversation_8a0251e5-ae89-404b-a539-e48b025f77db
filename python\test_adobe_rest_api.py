#!/usr/bin/env python3
"""
Simple test script for Adobe PDF Extract REST API
Tests the basic REST API workflow with your credentials
"""

import os
import sys
import json
import requests
import time

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_adobe_credentials():
    """Test Adobe credentials file"""
    log_print("🔍 [TEST] Checking Adobe credentials...")
    
    credentials_file = 'pdfservices-api-credentials.json'
    
    if not os.path.exists(credentials_file):
        log_print(f"❌ [TEST] Credentials file not found: {credentials_file}")
        return False, None, None
    
    try:
        with open(credentials_file, 'r') as f:
            credentials = json.load(f)
        
        client_credentials = credentials.get('client_credentials', {})
        client_id = client_credentials.get('client_id')
        client_secret = client_credentials.get('client_secret')
        
        if not client_id or not client_secret:
            log_print("❌ [TEST] Missing client_id or client_secret in credentials")
            return False, None, None
        
        log_print(f"✅ [TEST] Credentials loaded - Client ID: {client_id[:8]}...")
        return True, client_id, client_secret
        
    except Exception as e:
        log_print(f"❌ [TEST] Error reading credentials: {e}")
        return False, None, None

def test_access_token(client_id, client_secret):
    """Test getting access token from Adobe"""
    log_print("🔑 [TEST] Testing access token generation...")
    
    try:
        token_url = "https://pdf-services.adobe.io/token"
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'client_id': client_id,
            'client_secret': client_secret
        }
        
        log_print("📤 [TEST] Sending token request to Adobe...")
        response = requests.post(token_url, headers=headers, data=data)
        
        log_print(f"📥 [TEST] Response status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            
            if access_token:
                log_print(f"✅ [TEST] Access token obtained: {access_token[:20]}...")
                return True, access_token
            else:
                log_print("❌ [TEST] No access token in response")
                log_print(f"Response: {response.text}")
                return False, None
        else:
            log_print(f"❌ [TEST] Token request failed: {response.status_code}")
            log_print(f"Response: {response.text}")
            return False, None
            
    except Exception as e:
        log_print(f"❌ [TEST] Error getting access token: {e}")
        return False, None

def test_asset_upload(client_id, access_token):
    """Test asset upload endpoint (without actually uploading)"""
    log_print("📤 [TEST] Testing asset upload endpoint...")
    
    try:
        assets_url = "https://pdf-services.adobe.io/assets"
        
        headers = {
            'X-API-Key': client_id,
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'mediaType': 'application/pdf'
        }
        
        log_print("📤 [TEST] Requesting upload URI...")
        response = requests.post(assets_url, headers=headers, json=data)
        
        log_print(f"📥 [TEST] Response status: {response.status_code}")
        
        if response.status_code == 200:
            upload_data = response.json()
            upload_uri = upload_data.get('uploadUri')
            asset_id = upload_data.get('assetID')
            
            if upload_uri and asset_id:
                log_print(f"✅ [TEST] Upload URI obtained - Asset ID: {asset_id}")
                return True, asset_id
            else:
                log_print("❌ [TEST] Missing uploadUri or assetID in response")
                log_print(f"Response: {response.text}")
                return False, None
        else:
            log_print(f"❌ [TEST] Asset upload request failed: {response.status_code}")
            log_print(f"Response: {response.text}")
            return False, None
            
    except Exception as e:
        log_print(f"❌ [TEST] Error testing asset upload: {e}")
        return False, None

def run_adobe_rest_api_tests():
    """Run all Adobe REST API tests"""
    log_print("🚀 [TEST] Starting Adobe PDF Extract REST API tests...")
    log_print("=" * 60)
    
    # Test 1: Check credentials
    log_print("\n🧪 [TEST] Test 1: Adobe Credentials")
    creds_ok, client_id, client_secret = test_adobe_credentials()
    if not creds_ok:
        log_print("❌ [TEST] Cannot proceed without valid credentials")
        return False
    
    # Test 2: Get access token
    log_print("\n🧪 [TEST] Test 2: Access Token Generation")
    token_ok, access_token = test_access_token(client_id, client_secret)
    if not token_ok:
        log_print("❌ [TEST] Cannot proceed without access token")
        return False
    
    # Test 3: Test asset upload endpoint
    log_print("\n🧪 [TEST] Test 3: Asset Upload Endpoint")
    upload_ok, asset_id = test_asset_upload(client_id, access_token)
    if not upload_ok:
        log_print("❌ [TEST] Asset upload endpoint test failed")
        return False
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 [TEST] Test Results Summary:")
    log_print("  ✅ Adobe Credentials: PASS")
    log_print("  ✅ Access Token Generation: PASS")
    log_print("  ✅ Asset Upload Endpoint: PASS")
    
    log_print("\n🎉 [TEST] All Adobe REST API tests passed!")
    log_print("💡 [NEXT] Your Adobe PDF Extract API integration is ready to use.")
    log_print("\nNext steps:")
    log_print("  1. Run: python test_adobe_integration.py")
    log_print("  2. Test with a sample PDF")
    log_print("  3. Start the enhanced API server")
    
    return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Adobe PDF Extract REST API")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    success = run_adobe_rest_api_tests()
    
    if success:
        log_print("\n✅ [SUCCESS] Adobe REST API integration is working correctly!")
    else:
        log_print("\n❌ [FAILURE] Adobe REST API integration has issues. Please check:")
        log_print("  1. Ensure pdfservices-api-credentials.json is in the current directory")
        log_print("  2. Verify your Adobe PDF Services account is active")
        log_print("  3. Check your internet connection")
        log_print("  4. Ensure your credentials have PDF Extract API access")
    
    sys.exit(0 if success else 1)
