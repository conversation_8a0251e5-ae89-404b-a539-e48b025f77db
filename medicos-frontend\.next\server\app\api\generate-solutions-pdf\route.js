/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-solutions-pdf/route";
exports.ids = ["app/api/generate-solutions-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-solutions-pdf/route.ts */ \"(rsc)/./src/app/api/generate-solutions-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-solutions-pdf/route\",\n        pathname: \"/api/generate-solutions-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-solutions-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-solutions-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-solutions-pdf/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/generate-solutions-pdf/route.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Function to process text for PDF generation - handles tables and images\nfunction processTextForPDF(text) {\n    if (!text) return '';\n    let processedText = text;\n    // First, handle tables - convert markdown tables to HTML\n    processedText = processedText.replace(/(\\|[^|\\n]*\\|[^|\\n]*\\|[\\s\\S]*?)(?=\\n\\n|\\n(?!\\|)|$)/g, (match)=>{\n        try {\n            // Clean up malformed table syntax\n            let cleaned = match.trim();\n            cleaned = cleaned.replace(/<br\\s*\\/?>/gi, ' ');\n            const lines = cleaned.split('\\n').filter((line)=>line.trim());\n            if (lines.length < 2) return match;\n            // Parse table structure\n            const tableLines = [];\n            let hasHeader = false;\n            for (const line of lines){\n                const cells = line.split('|').map((cell)=>cell.trim()).filter((cell)=>cell);\n                if (cells.length === 0) continue;\n                // Check if this is a separator line\n                if (cells.every((cell)=>cell.match(/^:?-+:?$/))) {\n                    hasHeader = true;\n                    continue;\n                }\n                tableLines.push(cells);\n            }\n            if (tableLines.length === 0) return match;\n            // Generate HTML table\n            let html = '<table>';\n            if (hasHeader && tableLines.length > 0) {\n                html += '<thead><tr>';\n                for (const cell of tableLines[0]){\n                    html += `<th>${cell}</th>`;\n                }\n                html += '</tr></thead>';\n                if (tableLines.length > 1) {\n                    html += '<tbody>';\n                    for(let i = 1; i < tableLines.length; i++){\n                        html += '<tr>';\n                        for (const cell of tableLines[i]){\n                            html += `<td>${cell}</td>`;\n                        }\n                        html += '</tr>';\n                    }\n                    html += '</tbody>';\n                }\n            } else {\n                html += '<tbody>';\n                for (const row of tableLines){\n                    html += '<tr>';\n                    for (const cell of row){\n                        html += `<td>${cell}</td>`;\n                    }\n                    html += '</tr>';\n                }\n                html += '</tbody>';\n            }\n            html += '</table>';\n            return html;\n        } catch (error) {\n            console.warn('Error processing table:', error);\n            return match;\n        }\n    });\n    return processedText;\n}\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        console.log('Solutions PDF API called with payload:', payload);\n        const { title, description, duration, totalMarks, questions, filename = 'question-paper-solutions.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title} - Solutions</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ]\n        });\n      }\n    });\n  </script>\n  <style>\n    @page { size: A4; margin: 20mm 15mm; }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .question { break-inside: avoid; margin-bottom: 20px; page-break-inside: avoid; }\n    .question-content { margin-bottom: 8px; }\n    .options { margin-left: 12px; margin-bottom: 8px; }\n    .options p { margin: 2px 0; }\n    .answer { margin-top: 8px; padding: 6px 10px; background-color: #f0f8ff; border-left: 4px solid #2563eb; border-radius: 3px; }\n    .answer-text { font-weight: bold; color: #000; font-size: 10pt; }\n    .solution { margin-top: 10px; padding: 8px 12px; background-color: #f9f9f9; border-left: 4px solid #10b981; border-radius: 3px; }\n    .solution-title { font-weight: bold; color: #059669; margin-bottom: 6px; font-size: 10pt; }\n    .solution-content { font-size: 9pt; line-height: 1.4; }\n    .solution-content p { margin: 4px 0; }\n    .solution-content ol { margin: 4px 0; padding-left: 16px; }\n    .solution-content li { margin: 2px 0; }\n    .hints { margin-top: 10px; padding: 8px 12px; background-color: #fef3c7; border-left: 4px solid #f59e0b; border-radius: 3px; }\n    .hints-title { font-weight: bold; color: #d97706; margin-bottom: 6px; font-size: 10pt; }\n    .hint-item { margin: 3px 0; font-size: 9pt; line-height: 1.3; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading { font-weight: bold; margin: 12px 0 8px; font-size: 12pt; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px; }\n    /* Table styling for proper rendering */\n    table {\n      border-collapse: collapse;\n      width: 100%;\n      margin: 8px 0;\n      font-size: 9pt;\n      break-inside: avoid;\n    }\n    th, td {\n      border: 1px solid #333;\n      padding: 4px 6px;\n      text-align: left;\n      vertical-align: top;\n    }\n    th {\n      background-color: #f5f5f5;\n      font-weight: bold;\n    }\n    tr:nth-child(even) {\n      background-color: #f9f9f9;\n    }\n    /* Math rendering support */\n    .katex {\n      font-size: 1em;\n    }\n    .katex-display {\n      margin: 0.3em 0;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title} - Solutions</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${questions.reduce((acc, q, idx)=>{\n            // Check if this is a new subject\n            const isNewSubject = q.subject && (idx === 0 || questions[idx - 1].subject !== q.subject);\n            // Calculate question number within current subject\n            let questionNumber = 1;\n            if (!isNewSubject) {\n                // Count questions in current subject up to this point\n                const currentSubject = q.subject;\n                for(let i = idx - 1; i >= 0; i--){\n                    if (questions[i].subject === currentSubject) {\n                        questionNumber++;\n                    } else {\n                        break;\n                    }\n                }\n            }\n            const heading = isNewSubject ? `<div class=\"subject-heading\">Subject: ${q.subject}</div>` : '';\n            // Find the correct option letter for the answer\n            const answerIndex = q.options.findIndex((opt)=>opt === q.answer);\n            const answerLetter = answerIndex !== -1 ? String.fromCharCode(97 + answerIndex) : q.answer;\n            // Build solution section\n            let solutionHtml = '';\n            if (q.solution) {\n                const solutionParts = [];\n                if (q.solution.final_explanation) {\n                    const processedExplanation = processTextForPDF(q.solution.final_explanation);\n                    solutionParts.push(`<p><strong>Explanation:</strong> ${processedExplanation}</p>`);\n                }\n                if (q.solution.methodology) {\n                    const processedMethodology = processTextForPDF(q.solution.methodology);\n                    solutionParts.push(`<p><strong>Method:</strong> ${processedMethodology}</p>`);\n                }\n                if (q.solution.steps && q.solution.steps.length > 0) {\n                    const processedSteps = q.solution.steps.map((step)=>processTextForPDF(step));\n                    solutionParts.push(`<p><strong>Steps:</strong></p><ol>${processedSteps.map((step)=>`<li>${step}</li>`).join('')}</ol>`);\n                }\n                if (solutionParts.length > 0) {\n                    solutionHtml = `\n            <div class=\"solution\">\n              <div class=\"solution-title\">Solution:</div>\n              <div class=\"solution-content\">\n                ${solutionParts.join('')}\n              </div>\n            </div>`;\n                }\n            }\n            // Build hints section\n            let hintsHtml = '';\n            if (q.hints && q.hints.length > 0) {\n                const processedHints = q.hints.map((hint)=>processTextForPDF(hint));\n                hintsHtml = `\n          <div class=\"hints\">\n            <div class=\"hints-title\">Hints:</div>\n            ${processedHints.map((hint, i)=>`<div class=\"hint-item\">${i + 1}. ${hint}</div>`).join('')}\n          </div>`;\n            }\n            // Process question text with tables, images, and LaTeX\n            let processedQuestion = processTextForPDF(q.question);\n            // Apply image processing after table processing\n            processedQuestion = processedQuestion// Handle markdown images\n            .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle raw data URLs\n            .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Question Image\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />')// Handle image references from imageData field\n            .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                // Try to find matching image in question's imageData\n                const imageData = q.imageData || q.chemicalImages;\n                if (imageData && typeof imageData === 'object') {\n                    // Try to find matching image key\n                    const imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key) || key.includes(src.replace(/\\.(jpeg|jpg|png)$/i, '')));\n                    if (imageKey && imageData[imageKey]) {\n                        return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;\" />`;\n                    }\n                }\n                return `[Missing Image: ${src}]`;\n            })// Remove broken image references\n            .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove standalone base64 strings\n            .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n            const processedOptions = q.options.map((opt)=>{\n                // Process option text with tables first\n                let processedOpt = processTextForPDF(opt);\n                // Apply image processing after table processing\n                return processedOpt// Handle markdown images\n                .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle raw data URLs\n                .replace(/(data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*)/g, '<img src=\"$1\" alt=\"Option Image\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />')// Handle image references from imageData field\n                .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, (_, alt, src)=>{\n                    // Try to find matching image in question's imageData\n                    const imageData = q.imageData || q.chemicalImages;\n                    if (imageData && typeof imageData === 'object') {\n                        // Try to find matching image key\n                        const imageKey = Object.keys(imageData).find((key)=>key.includes(src) || src.includes(key) || key.includes(src.replace(/\\.(jpeg|jpg|png)$/i, '')));\n                        if (imageKey && imageData[imageKey]) {\n                            return `<img src=\"${imageData[imageKey]}\" alt=\"${alt}\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;\" />`;\n                        }\n                    }\n                    return `[Missing Image: ${src}]`;\n                })// Remove broken image references\n                .replace(/img\\s*[−-]\\s*\\d+\\.(jpeg|jpg|png)\\s*\\([^)]*\\)/gi, '')// Remove standalone base64 strings\n                .replace(/[A-Za-z0-9+/]{100,}={0,2}/g, '');\n            });\n            const qHtml = `\n        <div class=\"question\">\n          <div class=\"question-content\">\n            <p><strong>${questionNumber}.</strong> ${processedQuestion}</p>\n          </div>\n          <div class=\"options\">\n            ${processedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n          </div>\n          <div class=\"answer\">\n            <p class=\"answer-text\">Answer: ${answerLetter})</p>\n          </div>\n          ${solutionHtml}\n          ${hintsHtml}\n        </div>`;\n            return acc + heading + qHtml;\n        }, '')}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        console.log('Launching Puppeteer for solutions PDF generation...');\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        console.log('Setting HTML content for solutions PDF...');\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait until KaTeX has rendered math. Small delay to be safe.\n        await page.waitForFunction(()=>{\n            return Array.from(document.querySelectorAll('.katex')).length > 0;\n        }, {\n            timeout: 3000\n        }).catch(()=>{});\n        // Extra small delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 200));\n        console.log('Generating solutions PDF...');\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        console.log('Solutions PDF generated successfully, size:', pdfBuffer.length, 'bytes');\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('Solutions PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'Solutions PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-solutions-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();