#!/usr/bin/env python3
"""
Test script to verify Google Gemini AI integration
"""

import os
import sys

def test_environment():
    """Test if the environment is properly set up for Gemini AI"""
    print("🔍 Testing Gemini AI Environment Setup...")
    
    # Check if GEMINI_API_KEY is set (environment variable or file)
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        # Try to read from file
        try:
            with open('gemini_key.txt', 'r', encoding='utf-8') as f:
                gemini_key = f.read().strip()
            print(f"✅ GEMINI_API_KEY loaded from gemini_key.txt file (length: {len(gemini_key)} characters)")
        except FileNotFoundError:
            print("❌ GEMINI_API_KEY environment variable not set and gemini_key.txt file not found")
            print("   Please set environment variable: export GEMINI_API_KEY=your_api_key_here")
            print("   Or create gemini_key.txt file with your API key")
            return False
    else:
        print(f"✅ GEMINI_API_KEY is set (length: {len(gemini_key)} characters)")
    
    # Check if Mistral key exists (optional)
    if os.path.exists('key.txt'):
        print("✅ Mistral API key file (key.txt) found - OCR functionality available")
    else:
        print("⚠️ Mistral API key file (key.txt) not found - OCR functionality may be limited")
    
    return True

def test_imports():
    """Test if required packages can be imported"""
    print("\n🔍 Testing Package Imports...")
    
    try:
        import google.generativeai as genai
        print("✅ google.generativeai imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import google.generativeai: {e}")
        print("   Please install with: pip install google-generativeai")
        return False
    
    try:
        from mistralai import Mistral
        print("✅ mistralai imported successfully")
    except ImportError as e:
        print(f"⚠️ Failed to import mistralai: {e}")
        print("   OCR functionality may be limited")
    
    try:
        import flask
        print("✅ flask imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import flask: {e}")
        print("   Please install with: pip install flask")
        return False
    
    return True

def test_gemini_connection():
    """Test connection to Gemini AI"""
    print("\n🔍 Testing Gemini AI Connection...")
    
    try:
        import google.generativeai as genai
        
        # Configure with API key (try environment variable first, then file)
        gemini_key = os.getenv('GEMINI_API_KEY')
        if not gemini_key:
            try:
                with open('gemini_key.txt', 'r', encoding='utf-8') as f:
                    gemini_key = f.read().strip()
            except FileNotFoundError:
                pass

        genai.configure(api_key=gemini_key)
        
        # Create model instance
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Test with a simple prompt
        test_prompt = "Return a simple JSON array with one test object: [{\"test\": \"success\"}]"
        response = model.generate_content(test_prompt)
        
        if response and response.text:
            print("✅ Gemini AI connection successful")
            print(f"   Response preview: {response.text[:100]}...")
            return True
        else:
            print("❌ Gemini AI connection failed - no response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini AI connection failed: {e}")
        return False

def test_question_extractor():
    """Test the QuestionExtractor class with Gemini"""
    print("\n🔍 Testing QuestionExtractor with Gemini...")
    
    try:
        from question_extractor import QuestionExtractor
        
        # Initialize with Gemini
        extractor = QuestionExtractor(ai_provider='gemini')
        print("✅ QuestionExtractor initialized with Gemini AI")
        
        # Test basic functionality (without actual PDF)
        if hasattr(extractor, 'gemini_model') and extractor.gemini_model:
            print("✅ Gemini model properly initialized")
            return True
        else:
            print("❌ Gemini model not properly initialized")
            return False
            
    except Exception as e:
        print(f"❌ QuestionExtractor test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Gemini AI Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Environment Setup", test_environment),
        ("Package Imports", test_imports),
        ("Gemini Connection", test_gemini_connection),
        ("QuestionExtractor", test_question_extractor)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Gemini AI integration is ready.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the setup.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
