# PDF Question Extraction API

A simple Flask-based REST API server that processes PDF files in real-time and returns extracted questions and solutions as JSON responses.

## Features

- **Real-time Processing**: Processes PDFs on-the-fly without saving to disk
- **Question Extraction**: Extracts multiple-choice questions with options and answers
- **Solution Extraction**: Extracts detailed solution steps and procedures
- **JSON Response**: Returns structured JSON data directly
- **File Upload**: Accepts PDF files via multipart/form-data
- **Error Handling**: Comprehensive error handling and validation

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure you have your Mistral AI API key in `key.txt` file

## Running the Server

Start the API server:
```bash
python api_server.py
```

The server will start on `http://localhost:5000`

## API Endpoints

### Health Check
- **URL**: `GET /`
- **Description**: Check if the API server is running
- **Response**: Server status and available endpoints

### Extract Questions
- **URL**: `POST /api/extract/questions`
- **Description**: Extract questions from uploaded PDF
- **Content-Type**: `multipart/form-data`
- **Parameters**: 
  - `file`: PDF file to process
- **Response**: JSON with extracted questions

### Extract Solutions
- **URL**: `POST /api/extract/solutions`
- **Description**: Extract solutions from uploaded PDF
- **Content-Type**: `multipart/form-data`
- **Parameters**: 
  - `file`: PDF file to process
- **Response**: JSON with extracted solutions

## Usage Examples

### Using curl

Health check:
```bash
curl http://localhost:5000/
```

Extract questions:
```bash
curl -X POST -F "file=@document.pdf" http://localhost:5000/api/extract/questions
```

Extract solutions:
```bash
curl -X POST -F "file=@document.pdf" http://localhost:5000/api/extract/solutions
```

### Using Python requests

```python
import requests

# Health check
response = requests.get("http://localhost:5000/")
print(response.json())

# Extract questions
with open("document.pdf", "rb") as f:
    files = {"file": f}
    response = requests.post("http://localhost:5000/api/extract/questions", files=files)
    questions = response.json()
    print(questions)
```

### Using the test script

Run the included test script:
```bash
python test_api.py
```

## Response Format

### Successful Question Extraction
```json
{
  "status": "success",
  "filename": "document.pdf",
  "questions_count": 5,
  "data": [
    {
      "question": "What is 2 + 2?",
      "options": {
        "A": "a. 3",
        "B": "b. 4", 
        "C": "c. 5",
        "D": "d. 6"
      },
      "answer": "B",
      "question_number": 1
    }
  ]
}
```

### Successful Solution Extraction
```json
{
  "status": "success",
  "filename": "document.pdf", 
  "solutions_count": 3,
  "data": [
    {
      "question_number": 1,
      "solution_steps": ["Step 1: ...", "Step 2: ..."],
      "methodology": "algebraic",
      "final_answer": "4"
    }
  ]
}
```

### Error Response
```json
{
  "error": "Processing failed: Invalid PDF format"
}
```

## Configuration

- **Max file size**: 16MB
- **Allowed file types**: PDF only
- **Server host**: 0.0.0.0 (all interfaces)
- **Server port**: 5000
- **Debug mode**: Enabled (for development)

## Error Codes

- `400`: Bad request (missing file, invalid file type)
- `413`: File too large (>16MB)
- `500`: Internal server error (processing failed)

## Notes

- The API processes files in real-time and does not save them to disk
- Temporary files are automatically cleaned up after processing
- JSON responses are returned directly without intermediate file storage
- The server uses the existing `QuestionExtractor` class for all processing logic
