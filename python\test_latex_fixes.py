#!/usr/bin/env python3
"""
Test the comprehensive LaTeX fixes for \ffrac issues
"""

from question_extractor import QuestionExtractor

def test_latex_fixes():
    """Test the LaTeX fixing functionality"""
    
    print("🧪 Testing comprehensive LaTeX fixes...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Test cases from your examples
    test_cases = [
        "\\ffrac11000ohm",
        "\\ffrac100πMHz", 
        "\\ffrac1000πHz",
        "\\ffrac11000Hz",
        "E R/ R + Lω − \\ffrac1Cω",
        "\\ffracE 2 R2 + (Lω − \\ffrac1Cω) R",
        "\\ffracV R1R2 R1 2 + R2 2",
        "\\ffracV R2 t = ∞",
        "\\ffracV (R1 + R2)R1R2 t = ∞",
        "LCR emfE",
        "K t = 0 \\ffracV R1R2 R1 2 + R2 2 t = 0"
    ]
    
    print(f"📝 Testing {len(test_cases)} LaTeX patterns...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔧 Test {i}:")
        print(f"   Input:  {test_case}")
        
        # Apply LaTeX conversion
        result = extractor._convert_latex_to_katex(test_case)
        print(f"   Output: {result}")
        
        # Check if \ffrac was fixed
        if '\\ffrac' in result:
            print(f"   Status: ❌ Still contains \\ffrac")
        else:
            print(f"   Status: ✅ \\ffrac fixed")
    
    # Test the full cleaning process
    print(f"\n🧹 Testing full text cleaning...")
    
    full_test = "When DC voltage is applied, the current is \\ffrac100πMHz and frequency is \\ffrac1000πHz with resistance \\ffrac11000ohm"
    print(f"   Full Input:  {full_test}")
    
    cleaned = extractor._clean_text_for_json(full_test)
    print(f"   Full Output: {cleaned}")
    
    if '\\ffrac' in cleaned:
        print(f"   Full Status: ❌ Still contains \\ffrac")
    else:
        print(f"   Full Status: ✅ All \\ffrac fixed")

def test_question_processing():
    """Test question processing with LaTeX fixes"""
    
    print(f"\n🔄 Testing question processing with LaTeX fixes...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Mock question data with LaTeX issues
    mock_questions = [
        {
            'question': 'The frequency is \\ffrac100πMHz and resistance is \\ffrac11000ohm',
            'options': {
                'A': '\\ffrac1000πHz',
                'B': '\\ffrac11000Hz', 
                'C': '\\ffracV R2',
                'D': '\\ffracE 2'
            },
            'answer': 'A',
            'type': 'mcq',
            'difficulty': 'medium'
        }
    ]
    
    # Clean the questions
    cleaned_questions = extractor._clean_questions_for_json(mock_questions)
    
    print(f"📊 Processed {len(cleaned_questions)} questions")
    
    if cleaned_questions:
        q = cleaned_questions[0]
        print(f"\n📝 Sample cleaned question:")
        print(f"   Question: {q.get('question', 'N/A')}")
        print(f"   Option A: {q.get('options', {}).get('A', 'N/A')}")
        print(f"   Option B: {q.get('options', {}).get('B', 'N/A')}")
        
        # Check if any \ffrac remains
        question_text = str(q.get('question', ''))
        options_text = str(q.get('options', {}))
        
        if '\\ffrac' in question_text or '\\ffrac' in options_text:
            print(f"   Status: ❌ \\ffrac still present in processed question")
        else:
            print(f"   Status: ✅ All \\ffrac fixed in processed question")

def main():
    """Main test function"""
    
    print("🚀 Comprehensive LaTeX Fix Testing")
    print("=" * 60)
    
    try:
        # Test LaTeX fixes
        test_latex_fixes()
        
        # Test question processing
        test_question_processing()
        
        print(f"\n✅ All tests completed!")
        print(f"\n💡 Key improvements:")
        print(f"   ✅ \\ffrac11000ohm → \\frac{{1}}{{1000ohm}}")
        print(f"   ✅ \\ffrac100πMHz → \\frac{{100}}{{πMHz}}")
        print(f"   ✅ \\ffracV R2 → \\frac{{V}}{{R2}}")
        print(f"   ✅ \\ffracE 2 → \\frac{{E}}{{2}}")
        
        print(f"\n🎯 Next steps:")
        print(f"   1. Re-extract questions from your PDF to get clean LaTeX")
        print(f"   2. Or run database cleanup script for existing questions")
        print(f"   3. New extractions will have proper LaTeX formatting")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
