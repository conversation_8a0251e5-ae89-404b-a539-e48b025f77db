#!/usr/bin/env python3
"""
Output Formatting module to convert extracted questions to the required format
"""

class OutputFormatter:
    """Class to format extracted questions into the required output format"""
    
    def __init__(self):
        """Initialize the output formatter"""
        pass
    
    def format_single_question(self, question_data):
        """
        Format a single question into the required format:
        {QUESTIONBEGIN}
        [Question text here]
        {OPTIONSBEGIN}
        [Option A text]
        [Option B text]
        [Option C text]
        [Option D text]
        {OPTIONSEND}
        {QUESTIONEND}
        """
        try:
            question_text = question_data.get('question', '').strip()
            options = question_data.get('options', {})
            
            if not question_text:
                return None
            
            # Start building the formatted output
            formatted_output = "{QUESTIONBEGIN}\n"
            formatted_output += f"{question_text}\n"
            formatted_output += "{OPTIONSBEGIN}\n"
            
            # Add options in order A, B, C, D
            for option_key in ['A', 'B', 'C', 'D']:
                option_text = options.get(option_key, '').strip()
                if option_text and option_text != '[INCOMPLETE]':
                    formatted_output += f"{option_text}\n"
                else:
                    # If option is missing or incomplete, add a placeholder
                    formatted_output += f"[Option {option_key} - Not Available]\n"
            
            formatted_output += "{OPTIONSEND}\n"
            formatted_output += "{QUESTIONEND}\n"
            
            return formatted_output
            
        except Exception as e:
            print(f"Warning: Error formatting question: {e}")
            return None
    
    def format_all_questions(self, questions_list):
        """Format all questions into the required format"""
        try:
            if not questions_list:
                return "No questions found in the document."
            
            formatted_output = ""
            formatted_count = 0
            
            for i, question_data in enumerate(questions_list):
                formatted_question = self.format_single_question(question_data)
                
                if formatted_question:
                    formatted_output += formatted_question
                    if i < len(questions_list) - 1:  # Add separator between questions
                        formatted_output += "\n"
                    formatted_count += 1
            
            # Add summary at the end
            summary = f"\n# Summary: Successfully formatted {formatted_count} questions from {len(questions_list)} extracted questions."
            
            return formatted_output + summary
            
        except Exception as e:
            return f"Error formatting questions: {e}"
    
    def save_formatted_output(self, formatted_text, output_file="formatted_questions.txt"):
        """Save the formatted output to a file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(formatted_text)
            return True
        except Exception as e:
            print(f"Error saving formatted output: {e}")
            return False
    
    def validate_format(self, formatted_text):
        """Validate that the formatted text follows the required format"""
        try:
            # Check for required markers
            required_markers = [
                "{QUESTIONBEGIN}",
                "{OPTIONSBEGIN}",
                "{OPTIONSEND}",
                "{QUESTIONEND}"
            ]
            
            validation_results = {
                'valid': True,
                'issues': [],
                'question_count': 0
            }
            
            # Count questions
            question_count = formatted_text.count("{QUESTIONBEGIN}")
            validation_results['question_count'] = question_count
            
            # Check if all markers are present
            for marker in required_markers:
                marker_count = formatted_text.count(marker)
                if marker_count != question_count:
                    validation_results['valid'] = False
                    validation_results['issues'].append(
                        f"Marker '{marker}' appears {marker_count} times, expected {question_count}"
                    )
            
            # Check for balanced markers
            if formatted_text.count("{QUESTIONBEGIN}") != formatted_text.count("{QUESTIONEND}"):
                validation_results['valid'] = False
                validation_results['issues'].append("Unbalanced QUESTIONBEGIN/QUESTIONEND markers")
            
            if formatted_text.count("{OPTIONSBEGIN}") != formatted_text.count("{OPTIONSEND}"):
                validation_results['valid'] = False
                validation_results['issues'].append("Unbalanced OPTIONSBEGIN/OPTIONSEND markers")
            
            return validation_results
            
        except Exception as e:
            return {
                'valid': False,
                'issues': [f"Validation error: {e}"],
                'question_count': 0
            }


