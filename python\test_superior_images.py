#!/usr/bin/env python3
"""
Test script for Superior Image Extraction System
Tests the new image-enhanced extraction pipeline
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

def log_print(message):
    """Print with immediate flush to ensure visibility"""
    print(message, flush=True)
    sys.stdout.flush()

def test_dependencies():
    """Test if all required dependencies are installed"""
    log_print("🔍 [TEST] Checking image processing dependencies...")
    
    dependencies = [
        ('PyMuPDF', 'fitz'),
        ('Pillow', 'PIL'),
        ('OpenCV', 'cv2'),
        ('NumPy', 'numpy')
    ]
    
    missing_deps = []
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            log_print(f"✅ [DEPS] {dep_name} is available")
        except ImportError:
            log_print(f"❌ [DEPS] {dep_name} is missing")
            missing_deps.append(dep_name)
    
    if missing_deps:
        log_print(f"⚠️ [DEPS] Missing dependencies: {', '.join(missing_deps)}")
        log_print("💡 [DEPS] Install with: python install_image_dependencies.py")
        return False
    
    log_print("✅ [DEPS] All dependencies are available")
    return True

def test_superior_image_extractor(pdf_path):
    """Test superior image extractor directly"""
    try:
        log_print("🖼️ [TEST] Testing Superior Image Extractor...")
        
        from superior_image_extractor import SuperiorImageExtractor
        
        extractor = SuperiorImageExtractor()
        
        start_time = time.time()
        result = extractor.extract_all_images(pdf_path)
        duration = time.time() - start_time
        
        images = result.get('images', {})
        image_mapping = result.get('image_mapping', {})
        metadata = result.get('extraction_metadata', {})
        
        log_print(f"✅ [SUPERIOR] Image extraction completed in {duration:.2f}s")
        log_print(f"📊 [SUPERIOR] Total images extracted: {len(images)}")
        log_print(f"📊 [SUPERIOR] Image mapping created for {len(image_mapping)} questions")
        log_print(f"📊 [SUPERIOR] Extraction methods used: {', '.join(metadata.get('extraction_methods', []))}")
        
        # Show image sources
        image_sources = {}
        for img_id, img_info in images.items():
            source = img_info.get('source', 'unknown')
            if source not in image_sources:
                image_sources[source] = 0
            image_sources[source] += 1
        
        log_print("📊 [SUPERIOR] Images by source:")
        for source, count in image_sources.items():
            log_print(f"   {source}: {count} images")
        
        # Test image quality
        high_quality_images = sum(1 for img in images.values() 
                                if img.get('quality') == 'high')
        log_print(f"📊 [SUPERIOR] High quality images: {high_quality_images}/{len(images)}")
        
        return len(images) > 0
        
    except Exception as e:
        log_print(f"❌ [SUPERIOR] Superior image extractor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_enhanced_extractor(pdf_path):
    """Test image-enhanced question extractor"""
    try:
        log_print("🚀 [TEST] Testing Image-Enhanced Question Extractor...")
        
        from image_enhanced_extractor import ImageEnhancedExtractor
        
        extractor = ImageEnhancedExtractor(ai_provider='gemini')
        
        start_time = time.time()
        result = extractor.extract_questions_with_superior_images(pdf_path)
        duration = time.time() - start_time
        
        questions_json = result.get('questions', '[]')
        questions = json.loads(questions_json) if questions_json else []
        metadata = result.get('extraction_metadata', {})
        
        log_print(f"✅ [IMAGE_ENHANCED] Extraction completed in {duration:.2f}s")
        log_print(f"📊 [IMAGE_ENHANCED] Questions extracted: {len(questions)}")
        log_print(f"📊 [IMAGE_ENHANCED] Total images: {metadata.get('total_images', 0)}")
        log_print(f"📊 [IMAGE_ENHANCED] Questions with images: {metadata.get('questions_with_images', 0)}")
        
        # Show sample questions with images
        questions_with_images = [q for q in questions if q.get('imageUrl')]
        if questions_with_images:
            log_print("📝 [IMAGE_ENHANCED] Sample questions with images:")
            for i, q in enumerate(questions_with_images[:3]):
                content = q.get('content', '')[:80]
                image_count = len(q.get('imageUrl', {})) if isinstance(q.get('imageUrl'), dict) else (1 if q.get('imageUrl') else 0)
                log_print(f"   {i+1}. {content}... ({image_count} images)")
        
        return len(questions) > 0
        
    except Exception as e:
        log_print(f"❌ [IMAGE_ENHANCED] Image-enhanced extractor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_image_enhanced(pdf_path):
    """Test image-enhanced extraction via API"""
    try:
        log_print("🌐 [TEST] Testing image-enhanced extraction via API...")
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:5000/', timeout=5)
            if response.status_code != 200:
                log_print("❌ [API] API server not responding correctly")
                return False
        except requests.exceptions.ConnectionError:
            log_print("❌ [API] Cannot connect to API server. Start with: python api_server.py")
            return False
        
        # Test image-enhanced extraction
        log_print("📤 [API] Sending PDF for image-enhanced extraction...")
        
        with open(pdf_path, 'rb') as f:
            files = {'file': f}
            data = {
                'ai_provider': 'gemini',
                'use_enhanced': 'true',
                'use_advanced': 'false',
                'use_image_enhanced': 'true'  # Enable image-enhanced extraction
            }
            
            start_time = time.time()
            response = requests.post(
                'http://localhost:5000/api/extract', 
                files=files, 
                data=data, 
                timeout=1800  # 30 minutes timeout
            )
            duration = time.time() - start_time
        
        log_print(f"📥 [API] Response received in {duration:.2f}s")
        log_print(f"📊 [API] Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            status = result.get('status')
            questions_count = result.get('questions_count', 0)
            performance_metrics = result.get('performance_metrics', {})
            
            log_print(f"✅ [API] Image-enhanced extraction successful!")
            log_print(f"📊 [API] Status: {status}")
            log_print(f"📊 [API] Questions extracted: {questions_count}")
            log_print(f"📊 [API] Total duration: {performance_metrics.get('total_duration', 0):.2f}s")
            log_print(f"📊 [API] Extraction method: {performance_metrics.get('extraction_method', 'unknown')}")
            
            # Show image-specific metrics
            if 'total_images' in performance_metrics:
                log_print(f"📊 [API] Total images: {performance_metrics['total_images']}")
            if 'questions_with_images' in performance_metrics:
                log_print(f"📊 [API] Questions with images: {performance_metrics['questions_with_images']}")
            if 'image_sources' in performance_metrics:
                log_print(f"📊 [API] Image sources: {', '.join(performance_metrics['image_sources'])}")
            
            return questions_count > 0
        else:
            log_print(f"❌ [API] Extraction failed with status {response.status_code}")
            try:
                error_data = response.json()
                log_print(f"📄 [API] Error details: {error_data}")
            except:
                log_print(f"📄 [API] Response text: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        log_print("❌ [API] Request timed out (>30 minutes)")
        return False
    except Exception as e:
        log_print(f"❌ [API] API test failed: {e}")
        return False

def compare_image_extraction_methods(pdf_path):
    """Compare different image extraction methods"""
    try:
        log_print("⚖️ [COMPARE] Comparing image extraction methods...")
        
        methods = [
            ('Standard', {'use_enhanced': 'false', 'use_advanced': 'false', 'use_image_enhanced': 'false'}),
            ('Enhanced', {'use_enhanced': 'true', 'use_advanced': 'false', 'use_image_enhanced': 'false'}),
            ('Advanced', {'use_enhanced': 'true', 'use_advanced': 'true', 'use_image_enhanced': 'false'}),
            ('Image-Enhanced', {'use_enhanced': 'true', 'use_advanced': 'false', 'use_image_enhanced': 'true'})
        ]
        
        results = {}
        
        for method_name, params in methods:
            try:
                log_print(f"🔄 [COMPARE] Testing {method_name} method...")
                
                with open(pdf_path, 'rb') as f:
                    files = {'file': f}
                    data = {
                        'ai_provider': 'gemini',
                        **params
                    }
                    
                    start_time = time.time()
                    response = requests.post(
                        'http://localhost:5000/api/extract', 
                        files=files, 
                        data=data, 
                        timeout=1800
                    )
                    duration = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    questions_count = result.get('questions_count', 0)
                    performance_metrics = result.get('performance_metrics', {})
                    
                    results[method_name] = {
                        'questions': questions_count,
                        'duration': duration,
                        'images': performance_metrics.get('total_images', 0),
                        'questions_with_images': performance_metrics.get('questions_with_images', 0),
                        'success': True
                    }
                    
                    log_print(f"✅ [COMPARE] {method_name}: {questions_count} questions, "
                            f"{performance_metrics.get('total_images', 0)} images in {duration:.1f}s")
                else:
                    results[method_name] = {
                        'questions': 0, 'duration': duration, 'images': 0, 
                        'questions_with_images': 0, 'success': False
                    }
                    log_print(f"❌ [COMPARE] {method_name}: Failed")
                    
            except Exception as e:
                log_print(f"❌ [COMPARE] {method_name} failed: {e}")
                results[method_name] = {
                    'questions': 0, 'duration': 0, 'images': 0, 
                    'questions_with_images': 0, 'success': False
                }
        
        # Display comparison
        log_print("\n📊 [COMPARE] Image Extraction Comparison Results:")
        log_print("=" * 80)
        log_print(f"{'Method':<15} | {'Questions':<9} | {'Images':<7} | {'Q+Img':<6} | {'Time':<8}")
        log_print("-" * 80)
        
        for method, result in results.items():
            status = "✅" if result['success'] else "❌"
            log_print(f"{status} {method:<13} | {result['questions']:>7d} | "
                    f"{result['images']:>5d} | {result['questions_with_images']:>4d} | "
                    f"{result['duration']:>6.1f}s")
        
        # Find best method for images
        best_image_method = max(results.items(), 
                              key=lambda x: (x[1]['questions_with_images'], x[1]['questions']))
        log_print(f"\n🏆 [COMPARE] Best for images: {best_image_method[0]} with "
                f"{best_image_method[1]['questions_with_images']} questions with images")
        
        return results
        
    except Exception as e:
        log_print(f"❌ [COMPARE] Comparison failed: {e}")
        return {}

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Superior Image Extraction System")
    parser.add_argument("--pdf", help="Path to test PDF file", default=None)
    parser.add_argument("--deps-only", action="store_true", help="Only test dependencies")
    parser.add_argument("--images-only", action="store_true", help="Only test image extraction")
    parser.add_argument("--api-only", action="store_true", help="Only test API extraction")
    parser.add_argument("--compare", action="store_true", help="Compare all extraction methods")
    
    args = parser.parse_args()
    
    log_print("🖼️ Testing Superior Image Extraction System")
    log_print("=" * 60)
    
    # Test dependencies first
    if not test_dependencies():
        if args.deps_only:
            return False
        log_print("⚠️ Some dependencies missing, but continuing with available methods...")
    
    if args.deps_only:
        return True
    
    # Find PDF file
    pdf_path = args.pdf
    if not pdf_path:
        pdf_files = list(Path('.').glob('*.pdf'))
        if not pdf_files:
            log_print("❌ No PDF files found. Please specify --pdf path/to/file.pdf")
            return False
        pdf_path = str(pdf_files[0])
        log_print(f"📄 Using PDF: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        log_print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Superior Image Extractor
    if not args.api_only:
        log_print("\n🖼️ Test 1: Superior Image Extractor")
        total_tests += 1
        if test_superior_image_extractor(pdf_path):
            success_count += 1
        
        if args.images_only:
            log_print(f"\n📊 Image extraction test completed.")
            return success_count > 0
    
    # Test 2: Image-Enhanced Question Extractor
    if not args.api_only and not args.images_only:
        log_print("\n🚀 Test 2: Image-Enhanced Question Extractor")
        total_tests += 1
        if test_image_enhanced_extractor(pdf_path):
            success_count += 1
    
    # Test 3: API Image-Enhanced Extraction
    if not args.images_only:
        log_print("\n🌐 Test 3: API Image-Enhanced Extraction")
        total_tests += 1
        if test_api_image_enhanced(pdf_path):
            success_count += 1
        
        if args.api_only:
            log_print(f"\n📊 API extraction test completed.")
            return success_count > 0
    
    # Test 4: Method Comparison
    if args.compare:
        log_print("\n⚖️ Test 4: Image Extraction Method Comparison")
        total_tests += 1
        comparison_results = compare_image_extraction_methods(pdf_path)
        if comparison_results:
            success_count += 1
    
    # Summary
    log_print("\n" + "=" * 60)
    log_print("📊 Test Results Summary:")
    log_print(f"✅ Passed: {success_count}/{total_tests} tests")
    
    if success_count == total_tests:
        log_print("🎉 All tests passed! Superior Image Extraction System is working correctly.")
        log_print("\n💡 Next steps:")
        log_print("  1. Use 'use_image_enhanced=true' parameter in API calls")
        log_print("  2. Monitor image extraction quality and question-image matching")
        log_print("  3. Fine-tune image processing parameters for your PDF types")
    else:
        log_print("⚠️ Some tests failed. Check the logs above for details.")
        log_print("\n🔧 Troubleshooting:")
        log_print("  1. Install missing dependencies: python install_image_dependencies.py")
        log_print("  2. Check API server is running: python api_server.py")
        log_print("  3. Verify PDF file contains images and is readable")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
