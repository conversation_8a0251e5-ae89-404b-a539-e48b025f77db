"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/ui/chemical-image-display.tsx":
/*!******************************************************!*\
  !*** ./src/components/ui/chemical-image-display.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChemicalImageDisplay: () => (/* binding */ ChemicalImageDisplay),\n/* harmony export */   ChemicalOptionDisplay: () => (/* binding */ ChemicalOptionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _base64_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _enhanced_text_renderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./enhanced-text-renderer */ \"(app-pages-browser)/./src/components/ui/enhanced-text-renderer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/beaker.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Beaker,ChevronDown,ChevronUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChemicalImageDisplay(param) {\n    let { text, images = {}, className, imageClassName, maxImageWidth = 400, maxImageHeight = 300, showImageToggle = true } = param;\n    _s();\n    const [showImages, setShowImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedImages, setExpandedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Process text to handle chemical image placeholders\n    const processChemicalText = (inputText)=>{\n        if (!inputText) return {\n            cleanText: '',\n            imageRefs: []\n        };\n        let cleanText = inputText;\n        const imageRefs = [];\n        // Find chemical image placeholders like [CHEMICAL_IMAGE_1_question]\n        const chemicalImagePattern = /\\[CHEMICAL_IMAGE_(\\d+)_([^\\]]+)\\]/g;\n        let match;\n        while((match = chemicalImagePattern.exec(inputText)) !== null){\n            const [fullMatch, questionNum, context] = match;\n            const imageKey = \"chemical_img_\".concat(questionNum, \"_\").concat(context);\n            if (images[imageKey]) {\n                imageRefs.push(imageKey);\n                // Replace placeholder with a marker for rendering\n                cleanText = cleanText.replace(fullMatch, \"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"));\n            } else {\n                // Remove placeholder if no image found\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        }\n        // Also handle markdown image references that weren't converted\n        const markdownPattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n        cleanText = cleanText.replace(markdownPattern, (match, alt, src)=>{\n            // Try to find matching image\n            const matchingKey = Object.keys(images).find((key)=>key.includes(src) || src.includes(key.replace('chemical_img_', '')));\n            if (matchingKey) {\n                imageRefs.push(matchingKey);\n                return \"[IMAGE_PLACEHOLDER_\".concat(matchingKey, \"]\");\n            }\n            return \"[Missing Image: \".concat(src, \"]\");\n        });\n        return {\n            cleanText,\n            imageRefs: [\n                ...new Set(imageRefs)\n            ]\n        };\n    };\n    const { cleanText, imageRefs } = processChemicalText(text);\n    const hasImages = imageRefs.length > 0;\n    const toggleImageExpansion = (imageKey)=>{\n        setExpandedImages((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(imageKey)) {\n                newSet.delete(imageKey);\n            } else {\n                newSet.add(imageKey);\n            }\n            return newSet;\n        });\n    };\n    const renderTextWithImagePlaceholders = (text)=>{\n        if (!text) return null;\n        const parts = text.split(/(\\[IMAGE_PLACEHOLDER_[^\\]]+\\])/);\n        return parts.map((part, index)=>{\n            const placeholderMatch = part.match(/\\[IMAGE_PLACEHOLDER_([^\\]]+)\\]/);\n            if (placeholderMatch) {\n                const imageKey = placeholderMatch[1];\n                const imageData = images[imageKey];\n                if (imageData && showImages) {\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Chemical Structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-2 p-2 bg-gray-100 border border-gray-200 rounded text-sm text-gray-600\",\n                    children: [\n                        \"[Chemical Structure - \",\n                        imageKey,\n                        \"]\"\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this);\n            }\n            return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_text_renderer__WEBPACK_IMPORTED_MODULE_4__.EnhancedTextRenderer, {\n                    text: part\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this) : null;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-3\", className),\n        children: [\n            hasImages && showImageToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            imageRefs.length,\n                            \" Chemical Structure\",\n                            imageRefs.length !== 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>setShowImages(!showImages),\n                        className: \"h-7 px-3 text-xs\",\n                        children: showImages ? 'Hide Images' : 'Show Images'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-base leading-relaxed\",\n                children: renderTextWithImagePlaceholders(cleanText)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            showImages && imageRefs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: imageRefs.map((imageKey)=>{\n                    // Skip if this image was already rendered inline\n                    if (cleanText.includes(\"[IMAGE_PLACEHOLDER_\".concat(imageKey, \"]\"))) {\n                        return null;\n                    }\n                    const imageData = images[imageKey];\n                    if (!imageData) return null;\n                    const isExpanded = expandedImages.has(imageKey);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chemical Structure - \",\n                                            imageKey.replace('chemical_img_', '').replace(/_/g, ' ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>toggleImageExpansion(imageKey),\n                                        className: \"h-6 px-2 text-blue-600 hover:text-blue-700\",\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Collapse\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Expand\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                src: imageData,\n                                alt: \"Chemical structure \".concat(imageKey),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border border-blue-300 rounded-md transition-all duration-200\", imageClassName, isExpanded ? \"cursor-zoom-out\" : \"cursor-zoom-in\"),\n                                maxWidth: isExpanded ? maxImageWidth * 1.5 : maxImageWidth,\n                                maxHeight: isExpanded ? maxImageHeight * 1.5 : maxImageHeight,\n                                onClick: ()=>toggleImageExpansion(imageKey)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, imageKey, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(ChemicalImageDisplay, \"WkjgmkVJW42NseduZzN6jEUfq8M=\");\n_c = ChemicalImageDisplay;\nfunction ChemicalOptionDisplay(param) {\n    let { option, images = {}, isCorrect = false, className } = param;\n    // If option has imageUrl as string, it might be a key to the images object\n    const imageData = option.imageUrl && typeof option.imageUrl === 'string' ? images[option.imageUrl] || option.imageUrl : option.imageUrl;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-start p-3 rounded-md border transition-colors\", isCorrect ? \"border-green-500 bg-green-50\" : \"border-gray-200 hover:border-blue-300\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: option.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChemicalImageDisplay, {\n                            text: option.text,\n                            images: images,\n                            maxImageWidth: 200,\n                            maxImageHeight: 150,\n                            showImageToggle: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: option.isImageOption ? \"\" : \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-50 border border-blue-200 rounded-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2 text-xs text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Beaker_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Chemical Structure Option\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base64_image__WEBPACK_IMPORTED_MODULE_3__.Base64Image, {\n                                    src: imageData,\n                                    alt: \"Option \".concat(option.label, \" - Chemical Structure\"),\n                                    maxWidth: 200,\n                                    maxHeight: 150,\n                                    className: \"border border-blue-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this),\n                    option.isImageOption && !imageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 italic text-sm\",\n                        children: \"Chemical structure option (image not available)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\chemical-image-display.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChemicalOptionDisplay;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChemicalImageDisplay\");\n$RefreshReg$(_c1, \"ChemicalOptionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/chemical-image-display.tsx\n"));

/***/ })

});