# PDF Question and Solution Extraction Tool Requirements
# 
# This file contains all the Python package dependencies required to run
# the PDF Question Parser with Solution Extraction functionality.
#
# Install all dependencies with:
# pip install -r requirements.txt

# Core AI clients for OCR and chat completion
mistralai>=1.0.0                # Mistral AI client (optional, for fallback)
google-generativeai>=0.3.0      # Google Gemini AI client (primary)

# HTTP requests for Adobe PDF Services REST API
requests>=2.28.0                # HTTP library for Adobe PDF Services REST API

# Flask web framework for API server
flask>=2.0.0

# Standard library modules (included with Python, listed for reference):
# - os (file system operations)
# - sys (system-specific parameters and functions)
# - argparse (command-line argument parsing)
# - base64 (base64 encoding/decoding for PDF processing)
# - re (regular expressions for text processing)
# - json (JSON parsing and generation)
# - pathlib (object-oriented filesystem paths)

# Optional development dependencies (uncomment if needed for development):
# pytest>=7.0.0          # For running unit tests
# black>=22.0.0           # Code formatting
# flake8>=4.0.0           # Code linting
# mypy>=0.950             # Type checking

# Optional documentation dependencies (uncomment if needed):
# sphinx>=4.0.0           # Documentation generation
# sphinx-rtd-theme>=1.0.0 # Read the Docs theme for Sphinx
