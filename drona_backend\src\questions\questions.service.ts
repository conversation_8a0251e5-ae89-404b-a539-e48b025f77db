import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Question } from '../schema/question.schema';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { FilterQuestionsDto } from './dto/filter-questions.dto';
import { BulkUploadPdfDto } from './dto/bulk-upload-pdf.dto';
import { MistralAiService } from '../mistral-ai/mistral-ai.service';
import { ImageCompressionService } from '../common/services/image-compression.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as FormData from 'form-data';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectModel(Question.name) private questionModel: Model<Question>,
    private readonly mistralAiService: MistralAiService,
    private readonly imageCompressionService: ImageCompressionService,
    private readonly httpService: HttpService,
  ) {}

  async create(
    createQuestionDto: CreateQuestionDto,
    user?: any,
    images?: Express.Multer.File[],
  ): Promise<Question> {
    // Validate that user is provided and has an ID
    if (!user || !user._id) {
      throw new BadRequestException(
        'User information is required to create a question',
      );
    }

    // Check for duplicate question content
    const duplicateQuestion = await this.checkForDuplicateQuestion(createQuestionDto.content);
    if (duplicateQuestion) {
      throw new BadRequestException(
        `A question with similar content already exists in the database (ID: ${duplicateQuestion._id}). Please modify the question content to make it unique.`,
      );
    }

    let imageUrls: string[] = [];

    // Process and compress uploaded images
    if (images && images.length > 0) {
      try {
        const compressionPromises = images.map((image) =>
          this.imageCompressionService.compressAndSaveImage(image, {
            maxSizeBytes: 2 * 1024 * 1024, // 2MB max compression target
            quality: 85,
            format: 'jpeg',
          }),
        );

        const compressedImages = await Promise.all(compressionPromises);
        imageUrls = compressedImages.map((result) => result.url);

        console.log(`Compressed ${images.length} images for question creation`);
      } catch (error) {
        throw new BadRequestException(
          `Image compression failed: ${error.message}`,
        );
      }
    }

    const questionData = {
      ...createQuestionDto,
      imageUrls: [...(createQuestionDto.imageUrls || []), ...imageUrls], // Merge existing URLs with new ones
      createdBy: user._id,
      // Ensure new questions are marked as pending review
      reviewStatus: 'pending',
    };

    const createdQuestion = new this.questionModel(questionData);
    return createdQuestion.save();
  }

  async findAll(filters: FilterQuestionsDto): Promise<any> {
    const query: any = {};

    // If topicId is provided, subjectId must also be provided
    if (filters.topicId && !filters.subjectId) {
      throw new BadRequestException(
        'SubjectId is required when topicId is provided',
      );
    }

    // Build query filters
    if (filters.subjectId) {
      query.subjectId = filters.subjectId;
    }
    if (filters.topicId) {
      query.topicId = filters.topicId;
    }
    if (filters.difficulty) {
      query.difficulty = filters.difficulty;
    }
    if (filters.type) {
      query.type = filters.type;
    }
    if (filters.reviewStatus) {
      query.reviewStatus = filters.reviewStatus;
    }
    if (filters.search) {
      query.$or = [
        { content: { $regex: filters.search, $options: 'i' } },
        { answer: { $regex: filters.search, $options: 'i' } },
      ];
    }

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalItems = await this.questionModel.countDocuments(query);
    const totalPages = Math.ceil(totalItems / limit);

    // Get questions with optimized population (including solution and hints)
    const questions = await this.questionModel
      .find(query)
      .select('_id content options answer difficulty type reviewStatus status createdAt solution hints')
      .populate('subjectId', '_id name')
      .populate('topicId', '_id name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean() // Use lean() for better performance
      .exec();

    return {
      questions,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
      },
    };
  }

  async findOne(id: string): Promise<Question> {
    const question = await this.questionModel
      .findById(id)
      .populate('subjectId', '_id name')
      .populate('topicId', '_id name')
      .populate('createdBy', '_id displayName email')
      .populate('reviewedBy', '_id displayName email')
      .lean()
      .exec();
    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }
    return question;
  }

  async update(
    id: string,
    updateQuestionDto: UpdateQuestionDto,
    images?: Express.Multer.File[],
  ): Promise<Question> {
    // Check for duplicate question content if content is being updated
    if (updateQuestionDto.content) {
      const duplicateQuestion = await this.checkForDuplicateQuestionExcluding(
        updateQuestionDto.content,
        id,
      );
      if (duplicateQuestion) {
        throw new BadRequestException(
          `A question with similar content already exists in the database (ID: ${duplicateQuestion._id}). Please modify the question content to make it unique.`,
        );
      }
    }

    let newImageUrls: string[] = [];

    // Process and compress uploaded images
    if (images && images.length > 0) {
      try {
        const compressionPromises = images.map((image) =>
          this.imageCompressionService.compressAndSaveImage(image, {
            maxSizeBytes: 2 * 1024 * 1024, // 2MB max compression target
            quality: 85,
            format: 'jpeg',
          }),
        );

        const compressedImages = await Promise.all(compressionPromises);
        newImageUrls = compressedImages.map((result) => result.url);

        console.log(
          `Compressed ${images.length} new images for question update`,
        );
      } catch (error) {
        throw new BadRequestException(
          `Image compression failed: ${error.message}`,
        );
      }
    }

    // Merge new image URLs with existing ones if provided
    const updateData = { ...updateQuestionDto };
    if (newImageUrls.length > 0) {
      // Get existing question to merge image URLs
      const existingQuestion = await this.questionModel.findById(id);
      if (existingQuestion) {
        updateData.imageUrls = [
          ...(existingQuestion.imageUrls || []),
          ...newImageUrls,
        ];
      } else {
        updateData.imageUrls = newImageUrls;
      }
    }

    const updatedQuestion = await this.questionModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('subjectId')
      .populate('topicId')
      .populate('createdBy')
      .exec();
    if (!updatedQuestion) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }
    return updatedQuestion;
  }

  async remove(id: string): Promise<Question> {
    const deletedQuestion = await this.questionModel
      .findByIdAndDelete(id)
      .exec();
    if (!deletedQuestion) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }
    return deletedQuestion;
  }

  async findDuplicates(filters?: {
    subjectId?: string;
    limit?: number;
  }): Promise<any> {
    const limit = filters?.limit || 50;

    // Use aggregation pipeline for better performance
    const pipeline: any[] = [
      // Stage 1: Match filters if provided
      ...(filters?.subjectId
        ? [{ $match: { subjectId: filters.subjectId } }]
        : []),

      // Stage 2: Group by content to find exact duplicates
      {
        $group: {
          _id: '$content',
          questions: { $push: '$$ROOT' },
          count: { $sum: 1 },
        },
      },

      // Stage 3: Filter groups with more than one question (duplicates)
      {
        $match: {
          count: { $gt: 1 },
        },
      },

      // Stage 4: Limit results
      { $limit: limit },

      // Stage 5: Lookup subject information
      {
        $lookup: {
          from: 'subjects',
          localField: 'questions.subjectId',
          foreignField: '_id',
          as: 'subjectInfo',
        },
      },

      // Stage 6: Project final structure
      {
        $project: {
          _id: 0,
          content: '$_id',
          duplicateCount: '$count',
          questions: {
            $map: {
              input: '$questions',
              as: 'question',
              in: {
                _id: '$$question._id',
                content: '$$question.content',
                difficulty: '$$question.difficulty',
                type: '$$question.type',
                reviewStatus: '$$question.reviewStatus',
                createdAt: '$$question.createdAt',
                subject: {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: '$subjectInfo',
                        cond: { $eq: ['$$this._id', '$$question.subjectId'] },
                      },
                    },
                    0,
                  ],
                },
              },
            },
          },
        },
      },
    ];

    const duplicateGroups = await this.questionModel.aggregate(pipeline).exec();

    // Transform the result to match expected format
    const result = duplicateGroups.map((group) => ({
      originalQuestion: group.questions[0],
      duplicates: group.questions.slice(1).map((q) => ({
        ...q,
        similarity: 'exact_match',
      })),
    }));

    return {
      duplicateGroups: result,
      totalGroups: result.length,
    };
  }

  async findPendingReviews(filters?: {
    subjectId?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    const query: any = {
      reviewStatus: 'pending',
    };

    if (filters?.subjectId) {
      query.subjectId = filters.subjectId;
    }

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalItems = await this.questionModel.countDocuments(query);
    const totalPages = Math.ceil(totalItems / limit);

    // Get questions with optimized population (including solution and hints)
    const questions = await this.questionModel
      .find(query)
      .select('_id content options answer difficulty type createdAt solution hints')
      .populate('subjectId', '_id name')
      .populate('topicId', '_id name')
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(limit)
      .lean() // Use lean() for better performance
      .exec();

    return {
      questions,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
      },
    };
  }

  async removeQuestion(id: string): Promise<void> {
    const question = await this.questionModel.findById(id).exec();
    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }
    await this.questionModel.deleteOne({ _id: id }).exec();
  }



  async getDebugCounts(): Promise<any> {
    const total = await this.questionModel.countDocuments();

    const byReviewStatus = {
      pending: await this.questionModel.countDocuments({ reviewStatus: 'pending' }),
      approved: await this.questionModel.countDocuments({ reviewStatus: 'approved' }),
      rejected: await this.questionModel.countDocuments({ reviewStatus: 'rejected' }),
    };

    const byStatus = {
      active: await this.questionModel.countDocuments({ status: 'active' }),
      inactive: await this.questionModel.countDocuments({ status: 'inactive' }),
    };

    // Get a few sample questions to see their structure
    const sampleQuestions = await this.questionModel
      .find({})
      .select('_id content reviewStatus status subjectId createdAt')
      .populate('subjectId', 'name')
      .limit(5)
      .lean()
      .exec();

    return {
      total,
      byReviewStatus,
      byStatus,
      sampleQuestions,
    };
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Normalize question content by removing whitespaces, newlines, and converting to lowercase
   * for duplicate comparison
   */
  private normalizeQuestionContent(content: string): string {
    if (!content || typeof content !== 'string') {
      return '';
    }

    return content
      .replace(/\s+/g, '') // Remove all whitespaces (spaces, tabs, etc.)
      .replace(/[\n\r]/g, '') // Remove newlines and carriage returns
      .replace(/[^\w\d]/g, '') // Remove all non-alphanumeric characters (punctuation, special chars)
      .toLowerCase() // Convert to lowercase for case-insensitive comparison
      .trim();
  }

  /**
   * Check if a question with similar content already exists in the database
   * Uses regex search for efficient duplicate detection
   */
  private async checkForDuplicateQuestion(content: string): Promise<any | null> {
    const normalizedContent = this.normalizeQuestionContent(content);

    // Get all questions with content only for efficient comparison
    const allQuestions = await this.questionModel
      .find({}, { content: 1 })
      .lean()
      .exec();

    // Check each question's normalized content
    for (const question of allQuestions) {
      const existingNormalizedContent = this.normalizeQuestionContent(question.content);
      if (existingNormalizedContent === normalizedContent) {
        return question;
      }
    }

    return null;
  }

  /**
   * Check if a question with similar content already exists in the database,
   * excluding a specific question ID (used for updates)
   */
  private async checkForDuplicateQuestionExcluding(
    content: string,
    excludeId: string,
  ): Promise<any | null> {
    const normalizedContent = this.normalizeQuestionContent(content);

    // Get all questions except the one being updated
    const allQuestions = await this.questionModel
      .find({ _id: { $ne: excludeId } }, { content: 1 })
      .lean()
      .exec();

    // Check each question's normalized content
    for (const question of allQuestions) {
      const existingNormalizedContent = this.normalizeQuestionContent(question.content);
      if (existingNormalizedContent === normalizedContent) {
        return question;
      }
    }

    return null;
  }

  async reviewQuestion(
    id: string,
    reviewDto: { status: 'approved' | 'rejected'; notes?: string },
    user: any,
  ) {
    // Validate input parameters
    if (!reviewDto || !reviewDto.status) {
      throw new BadRequestException('Review status is required');
    }

    if (!['approved', 'rejected'].includes(reviewDto.status)) {
      throw new BadRequestException('Status must be either approved or rejected');
    }

    const question = await this.questionModel.findById(id);
    if (!question) {
      throw new NotFoundException(`Question with ID ${id} not found`);
    }

    // Update question with review details
    question.reviewStatus = reviewDto.status;
    question.reviewedBy = user._id;
    question.reviewDate = new Date();
    question.reviewNotes = reviewDto.notes || '';

    // If approved, set status to active
    if (reviewDto.status === 'approved') {
      question.status = 'active';
    }

    return question.save();
  }

  async bulkReview(
    questionIds: string[],
    reviewDto: { status: 'approved' | 'rejected'; notes?: string },
    user: any,
  ) {
    // Find all questions
    const questions = await this.questionModel.find({
      _id: { $in: questionIds },
    });

    if (questions.length === 0) {
      throw new NotFoundException('No questions found with the provided IDs');
    }

    // Check if all questions were found
    if (questions.length !== questionIds.length) {
      const foundIds = questions.map((q) => q._id.toString());
      const missingIds = questionIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Some questions were not found: ${missingIds.join(', ')}`,
      );
    }

    // Check if any questions are already reviewed
    const alreadyReviewed = questions.filter(
      (q) => q.reviewStatus !== 'pending',
    );
    if (alreadyReviewed.length > 0) {
      const reviewedIds = alreadyReviewed.map((q) => q._id.toString());
      throw new BadRequestException(
        `Some questions are already reviewed: ${reviewedIds.join(', ')}`,
      );
    }

    // Prepare bulk update operation
    const bulkOps = questions.map((question) => ({
      updateOne: {
        filter: { _id: question._id },
        update: {
          $set: {
            reviewStatus: reviewDto.status,
            reviewedBy: user._id,
            reviewDate: new Date(),
            reviewNotes: reviewDto.notes || '',
            ...(reviewDto.status === 'approved' ? { status: 'active' } : {}),
          },
        },
      },
    }));

    // Execute bulk update
    const result = await this.questionModel.bulkWrite(bulkOps);

    return {
      message: `Successfully reviewed ${result.modifiedCount} questions`,
      reviewedCount: result.modifiedCount,
      status: reviewDto.status,
      details: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        upsertedCount: result.upsertedCount,
        deletedCount: result.deletedCount,
      },
    };
  }

  async bulkUploadFromPdf(
    file: Express.Multer.File,
    uploadDto: BulkUploadPdfDto,
    user: any,
  ) {
    try {
      // Create FormData for the API call
      const formData = new FormData();
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });
      formData.append('ai_provider', 'gemini'); // Use Gemini AI as primary provider

      // Make API call to localhost:5000/api/extract with extended timeout for large PDFs
      const response = await firstValueFrom(
        this.httpService.post('http://localhost:5000/api/extract', formData, {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 15 * 60 * 1000, // 15 minutes timeout for large PDFs with chunking
        })
      );

      const extractedData = response.data;

      // Validate response structure
      if (!extractedData || !extractedData.data || !Array.isArray(extractedData.data)) {
        throw new BadRequestException('Invalid response format from extraction API');
      }

      if (extractedData.data.length === 0) {
        throw new BadRequestException('No questions found in the PDF');
      }

      // Save questions to database
      const result = await this.saveExtractedQuestions(
        extractedData.data,
        uploadDto.subjectId,
        uploadDto.topicId,
        user._id,
      );

      return {
        message: `PDF processed successfully. ${result.questionsAdded} questions added, ${result.questionsFailed} failed.`,
        questionsAdded: result.questionsAdded,
        questionsFailed: result.questionsFailed,
        questions: result.questions,
        errors: result.errors,
      };
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new BadRequestException('Unable to connect to extraction service. Please ensure the service is running on localhost:5000');
      }
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new BadRequestException('PDF processing timed out. Please try with a smaller file or try again later.');
      }
      throw new BadRequestException(`Failed to process PDF: ${error.message}`);
    }
  }

  async bulkUploadChemicalFromPdf(
    file: Express.Multer.File,
    uploadDto: BulkUploadPdfDto,
    user: any,
  ) {
    try {
      // Create FormData for the chemical API call
      const formData = new FormData();
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });
      formData.append('ai_provider', 'gemini'); // Use Gemini AI as primary provider

      // Make API call to localhost:5000/api/extract-chemical with extended timeout
      const response = await firstValueFrom(
        this.httpService.post('http://localhost:5000/api/extract-chemical', formData, {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 20 * 60 * 1000, // 20 minutes timeout for chemical PDFs (more complex processing)
        })
      );

      const extractedData = response.data;

      // Log the response for debugging
      console.log('Chemical PDF extraction response:', {
        success: extractedData.success,
        questionsCount: extractedData.questions?.length || 0,
        questionsWithImages: extractedData.extraction_metadata?.questions_with_images || 0,
        hasMetadata: !!extractedData.extraction_metadata
      });

      // Validate response structure for chemical extraction
      // Handle both 'questions' and 'data' formats for compatibility
      const questionsData = extractedData.questions || extractedData.data;
      if (!extractedData || !questionsData || !Array.isArray(questionsData)) {
        throw new BadRequestException('Invalid response format from chemical extraction API');
      }

      if (questionsData.length === 0) {
        throw new BadRequestException('No chemical questions found in the PDF');
      }

      // Save chemical questions to database with special handling for images
      const result = await this.saveChemicalExtractedQuestions(
        questionsData,
        uploadDto.subjectId,
        uploadDto.topicId,
        user._id,
      );

      return {
        message: `Chemical PDF processed successfully. ${result.questionsAdded} questions added, ${result.questionsFailed} failed.`,
        questionsAdded: result.questionsAdded,
        questionsFailed: result.questionsFailed,
        questions: result.questions,
        errors: result.errors,
        extractionMetadata: extractedData.extraction_metadata,
      };
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new BadRequestException('Unable to connect to chemical extraction service. Please ensure the service is running on localhost:5000');
      }
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new BadRequestException('Chemical PDF processing timed out. Please try with a smaller file or try again later.');
      }
      throw new BadRequestException(`Failed to process chemical PDF: ${error.message}`);
    }
  }

  private async saveExtractedQuestions(
    extractedQuestions: any[],
    subjectId: string,
    topicId: string,
    userId: string,
  ) {
    const result: {
      questionsAdded: number;
      questionsFailed: number;
      questions: any[];
      errors: string[];
    } = {
      questionsAdded: 0,
      questionsFailed: 0,
      questions: [],
      errors: [],
    };

    // Validate IDs
    if (!Types.ObjectId.isValid(subjectId)) {
      throw new BadRequestException(`Invalid subjectId format: ${subjectId}`);
    }
    if (!Types.ObjectId.isValid(topicId)) {
      throw new BadRequestException(`Invalid topicId format: ${topicId}`);
    }
    if (!Types.ObjectId.isValid(userId)) {
      throw new BadRequestException(`Invalid userId format: ${userId}`);
    }

    for (const [index, questionData] of extractedQuestions.entries()) {
      try {
        // Validate required fields
        if (!questionData.question || !questionData.options || !questionData.answer) {
          throw new Error('Missing required fields: question, options, or answer');
        }

        // Convert options object to array format expected by the schema
        let optionsArray: string[] = [];
        if (typeof questionData.options === 'object' && !Array.isArray(questionData.options)) {
          // Convert {A: "option1", B: "option2", ...} to ["option1", "option2", ...]
          optionsArray = Object.values(questionData.options);
        } else if (Array.isArray(questionData.options)) {
          optionsArray = questionData.options;
        } else {
          throw new Error('Invalid options format');
        }

        if (optionsArray.length < 2) {
          throw new Error(`Insufficient options: ${optionsArray.length} (minimum 2 required)`);
        }

        // Find the correct answer text from options
        let answerText = questionData.answer;
        if (typeof questionData.options === 'object' && !Array.isArray(questionData.options)) {
          // If answer is a key like "A", get the corresponding option text
          if (questionData.options[questionData.answer]) {
            answerText = questionData.options[questionData.answer];
          }
        }

        // Create question document
        const questionDoc: any = {
          content: questionData.question,
          options: optionsArray,
          answer: answerText,
          subjectId: new Types.ObjectId(subjectId),
          topicId: new Types.ObjectId(topicId),
          difficulty: 'medium', // Default difficulty
          type: 'multiple-choice',
          createdBy: new Types.ObjectId(userId),
          reviewStatus: 'pending',
          status: 'inactive',
          source: 'pdf-extract',
          // Store additional fields from the new format
          solution: questionData.solution || null,
          hints: questionData.hints || [],
          imageUrls: [], // Will be populated if images are found in question text
          imageData: {}, // New field for image data
        };

        // Handle image data from extraction API
        if (questionData.imageUrl && typeof questionData.imageUrl === 'object') {
          // Store image data as key-value pairs (for chemical questions)
          questionDoc.imageData = questionData.imageUrl;
          questionDoc.imageUrls = []; // Keep imageUrls as empty array for compatibility
        } else if (questionData.imageUrl && typeof questionData.imageUrl === 'string') {
          // Store single image URL
          questionDoc.imageData = { main: questionData.imageUrl };
          questionDoc.imageUrls = [questionData.imageUrl];
        } else {
          // Extract image references from question text if any
          const imageMatches = questionData.question.match(/!\[.*?\]\(.*?\)/g);
          if (imageMatches) {
            const imageRefs: string[] = [];
            imageMatches.forEach((match: string) => {
              const urlMatch = match.match(/\((.*?)\)/);
              if (urlMatch && urlMatch[1]) {
                imageRefs.push(urlMatch[1]);
              }
            });
            questionDoc.imageUrls = imageRefs;
            questionDoc.imageData = {};
          } else {
            questionDoc.imageUrls = [];
            questionDoc.imageData = {};
          }
        }

        // Save to database
        const createdQuestion = new this.questionModel(questionDoc);
        await createdQuestion.validate();
        const savedQuestion = await createdQuestion.save();

        result.questions.push(savedQuestion);
        result.questionsAdded++;

      } catch (error) {
        result.questionsFailed++;
        const errorMessage = `Failed to create question ${index + 1}: ${error.message}`;
        result.errors.push(errorMessage);
      }
    }

    return result;
  }

  private async saveChemicalExtractedQuestions(
    extractedQuestions: any[],
    subjectId: string,
    topicId: string,
    userId: string,
  ) {
    const result: {
      questionsAdded: number;
      questionsFailed: number;
      questions: any[];
      errors: string[];
    } = {
      questionsAdded: 0,
      questionsFailed: 0,
      questions: [],
      errors: [],
    };

    // Validate IDs
    if (!Types.ObjectId.isValid(subjectId)) {
      throw new BadRequestException(`Invalid subjectId format: ${subjectId}`);
    }
    if (!Types.ObjectId.isValid(topicId)) {
      throw new BadRequestException(`Invalid topicId format: ${topicId}`);
    }
    if (!Types.ObjectId.isValid(userId)) {
      throw new BadRequestException(`Invalid userId format: ${userId}`);
    }

    for (let index = 0; index < extractedQuestions.length; index++) {
      try {
        const questionData = extractedQuestions[index];

        // Validate required fields
        if (!questionData.content && !questionData.question) {
          result.questionsFailed++;
          result.errors.push(`Question ${index + 1}: Missing question content`);
          continue;
        }

        if (!questionData.options || Object.keys(questionData.options).length < 2) {
          result.questionsFailed++;
          result.errors.push(`Question ${index + 1}: Missing or insufficient options`);
          continue;
        }

        // Create question document with chemical-specific handling
        const questionDoc: any = {
          text: questionData.content || questionData.question,
          options: [],
          correctAnswer: '',
          subject: new Types.ObjectId(subjectId),
          topic: new Types.ObjectId(topicId),
          createdBy: new Types.ObjectId(userId),
          reviewStatus: 'pending',
          difficulty: questionData.difficulty || 'medium',
          type: 'mcq',
          isChemical: true, // Mark as chemical question
          extractionMethod: 'chemical_specialized',
        };

        // Handle chemical images in question text
        if (questionData.imageUrl && typeof questionData.imageUrl === 'object') {
          questionDoc.chemicalImages = questionData.imageUrl; // Keep for backward compatibility
          questionDoc.imageData = questionData.imageUrl; // Use new standard field
          questionDoc.imageUrls = []; // Keep as empty array

          // Replace image placeholders in text with references
          let processedText = questionDoc.text;
          Object.keys(questionData.imageUrl).forEach(imageKey => {
            const placeholder = `[CHEMICAL_IMAGE_${index + 1}_question]`;
            if (processedText.includes(placeholder)) {
              processedText = processedText.replace(placeholder, `[${imageKey}]`);
            }
          });
          questionDoc.text = processedText;
        } else {
          questionDoc.imageData = {};
          questionDoc.imageUrls = [];
        }

        // Process options with chemical image support
        const optionLabels = ['A', 'B', 'C', 'D'];
        const optionKeys = Object.keys(questionData.options);

        for (let i = 0; i < optionKeys.length && i < 4; i++) {
          const optionKey = optionKeys[i];
          const optionValue = questionData.options[optionKey];

          const option: any = {
            label: optionLabels[i],
            text: '',
            isImageOption: false,
          };

          // Handle different option types
          if (typeof optionValue === 'string') {
            // Check if it's a base64 image or image reference
            if (optionValue.startsWith('data:image/') ||
                (questionData.imageUrl && questionData.imageUrl[optionValue])) {
              option.isImageOption = true;
              option.imageUrl = questionData.imageUrl && questionData.imageUrl[optionValue]
                ? questionData.imageUrl[optionValue]
                : optionValue;
            } else {
              option.text = optionValue;
            }
          } else if (optionValue && typeof optionValue === 'object' && optionValue.src) {
            // Handle image objects
            option.isImageOption = true;
            option.imageUrl = optionValue.src;
          } else {
            option.text = String(optionValue || '');
          }

          questionDoc.options.push(option);
        }

        // Set correct answer
        if (questionData.correctAnswer) {
          questionDoc.correctAnswer = questionData.correctAnswer;
        } else if (questionData.correct_answer) {
          questionDoc.correctAnswer = questionData.correct_answer;
        } else {
          // Try to find correct answer from options
          const correctOption = questionDoc.options.find((opt: any) => opt.isCorrect);
          if (correctOption) {
            questionDoc.correctAnswer = correctOption.text || correctOption.label;
          }
        }

        // Handle solution with chemical images
        if (questionData.solution) {
          questionDoc.solution = {
            final_explanation: questionData.solution.final_explanation || questionData.solution.explanation || '',
            key_concepts: questionData.solution.key_concepts || [],
            methodology: questionData.solution.methodology || '',
            steps: questionData.solution.steps || [],
          };
        }

        // Handle hints
        if (questionData.hints && Array.isArray(questionData.hints)) {
          questionDoc.hints = questionData.hints;
        }

        // Save to database
        const savedQuestion = await this.questionModel.create(questionDoc);
        result.questions.push(savedQuestion);
        result.questionsAdded++;

      } catch (error) {
        result.questionsFailed++;
        const errorMessage = `Failed to create chemical question ${index + 1}: ${error.message}`;
        result.errors.push(errorMessage);
      }
    }

    return result;
  }
}
